{"lintSemverRanges": false, "lintFormatting": false, "semverGroups": [], "versionGroups": [{"dependencies": ["@types/**", "@testing-library/**", "typescript"], "dependencyTypes": ["prod"], "isBanned": true, "label": "These packages should only be under devDependencies."}, {"dependencies": ["@refinedev/**"], "packages": ["**"], "isIgnored": true, "label": "@refinedev dependencies are managed by changeset."}, {"dependencies": ["**"], "dependencyTypes": ["peer"], "packages": ["**"], "isIgnored": true}, {"dependencies": ["nock"], "dependencyTypes": ["dev"], "packages": ["@refinedev/appwrite", "@refinedev/graphql"], "isIgnored": true}, {"dependencies": ["next", "react", "react-dom", "@types/react", "@types/react-dom"], "packages": ["with-nextjs-headless"], "isIgnored": true}]}