{"compilerOptions": {"module": "esnext", "lib": ["dom", "esnext", "DOM.Iterable"], "importHelpers": true, "declaration": true, "sourceMap": true, "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "allowImportingTsExtensions": true, "moduleResolution": "node", "jsx": "react", "esModuleInterop": true, "noEmit": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true}}