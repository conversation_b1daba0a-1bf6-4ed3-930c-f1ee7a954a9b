{"version": 3, "file": "helix.js", "sourceRoot": "", "sources": ["../../src/coord/helix.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAE3C,OAAO,UAAuB,MAAM,QAAQ,CAAC;AAE7C;;GAEG;AACH;IAAmC,yBAAU;IAQ3C,eAAY,GAAa;QAAzB,YACE,kBAAM,GAAG,CAAC,SAUX;QAlBe,aAAO,GAAY,IAAI,CAAC;QACxB,UAAI,GAAW,OAAO,CAAC;QAS7B,IAAA,KAAoF,GAAG,WAA5D,EAA3B,UAAU,mBAAG,IAAI,GAAG,IAAI,CAAC,EAAE,KAAA,EAAE,KAAuD,GAAG,SAAjC,EAAzB,QAAQ,mBAAG,IAAI,GAAG,IAAI,CAAC,EAAE,KAAA,EAAE,KAA4B,GAAG,YAAhB,EAAf,WAAW,mBAAG,CAAC,KAAA,EAAE,MAAM,GAAK,GAAG,OAAR,CAAS;QAEhG,KAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,KAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,KAAI,CAAC,OAAO,EAAE,CAAC;;IACjB,CAAC;IAEM,uBAAO,GAAd;QACE,iBAAM,OAAO,WAAE,CAAC;QAEhB,IAAM,KAAK,GAAW,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO;QACpF,IAAI,SAAS,GAAW,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;YACvD,SAAS,GAAG,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;SACrC;QAED,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAClE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;QAExC,IAAI,CAAC,CAAC,GAAG;YACP,KAAK,EAAE,IAAI,CAAC,UAAU;YACtB,GAAG,EAAE,IAAI,CAAC,QAAQ;SACnB,CAAC;QACF,IAAI,CAAC,CAAC,GAAG;YACP,KAAK,EAAE,IAAI,CAAC,WAAW,GAAG,SAAS;YACnC,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,SAAS,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI;SAClD,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,4BAAY,GAAnB,UAAoB,KAAY;;QACxB,IAAA,CAAC,GAAQ,KAAK,EAAb,EAAE,CAAC,GAAK,KAAK,EAAV,CAAW;QACrB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,KAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAd,CAAC,QAAA,EAAE,CAAC,QAAA,CAAW;SACjB;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpC,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QACvB,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAErC,OAAO;YACL,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;YAC7C,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,2BAAW,GAAlB,UAAmB,KAAY;;QAC7B,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAEhC,IAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAY,CAAC;QAE/F,IAAI,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACvC,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,wBAAwB;QAEjD,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE;YACzB,2CAA2C;YAC3C,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SACvB;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe;QACtE,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QAChC,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QACvB,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEzC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAClC,CAAC,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,KAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAd,CAAC,QAAA,EAAE,CAAC,QAAA,CAAW;SACjB;QAED,OAAO,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC;IAClB,CAAC;IACH,YAAC;AAAD,CAAC,AApGD,CAAmC,UAAU,GAoG5C"}