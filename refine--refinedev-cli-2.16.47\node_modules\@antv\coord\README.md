# coord

[![](https://img.shields.io/travis/antvis/coord.svg)](https://travis-ci.org/antvis/coord)
![](https://img.shields.io/badge/language-javascript-red.svg)
![](https://img.shields.io/badge/license-MIT-000000.svg)

[![npm package](https://img.shields.io/npm/v/@antv/coord.svg)](https://www.npmjs.com/package/@antv/coord)
[![NPM downloads](http://img.shields.io/npm/dm/@antv/coord.svg)](https://npmjs.org/package/@antv/coord)
[![Percentage of issues still open](http://isitmaintained.com/badge/open/antvis/coord.svg)](http://isitmaintained.com/project/antvis/coord "Percentage of issues still open")

Coordinate systems used in G2

## Installing

`npm install @antv/coord`

```js
import coord from '@antv/coord';

```

## API

See details at https://antv.alipay.com

## Contributing
