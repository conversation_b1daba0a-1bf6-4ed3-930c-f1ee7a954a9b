{"name": "@refinedev/mui", "version": "6.2.2", "private": false, "description": "Material-UI (MUI) support for Refine, providing enterprise-level UI components.", "license": "MIT", "author": "refine", "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "typings": "dist/index.d.ts", "scripts": {"attw": "attw --pack .", "build": "tsup && node ../shared/generate-declarations.js", "dev": "tsup --watch", "prepare": "pnpm build", "publint": "publint --strict=true --level=suggestion", "test": "jest --passWithNoTests --runInBand", "types": "node ../shared/generate-declarations.js"}, "dependencies": {"@emotion/react": "^11.8.2", "@emotion/styled": "^11.8.1", "@mui/icons-material": "^6.1.6", "@mui/lab": "^6.0.0-beta.14", "@mui/material": "^6.1.7", "@mui/system": "^6.1.6", "@mui/x-data-grid": "^7.23.5", "@refinedev/react-hook-form": "^4.10.2", "@refinedev/ui-types": "^1.24.2", "dayjs": "^1.10.7", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "notistack": "^2.0.4", "react-hook-form": "^7.57.0", "react-markdown": "^6.0.1", "remark-gfm": "^1.0.0", "tslib": "^2.6.2", "warn-once": "^0.1.0"}, "devDependencies": {"@esbuild-plugins/node-resolve": "^0.1.4", "@refinedev/cli": "^2.16.46", "@refinedev/core": "^4.57.10", "@refinedev/ui-tests": "^1.15.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.1.1", "@testing-library/react-hooks": "^8.0.0", "@testing-library/user-event": "^14.1.1", "@types/jest": "^29.2.4", "@types/lodash": "^4.14.171", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/testing-library__jest-dom": "^5.14.3", "esbuild-copy-static-files": "^0.1.0", "esbuild-plugin-inline-image": "^0.0.8", "identity-obj-proxy": "^3.0.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "postcss": "^8.1.4", "react-router": "^7.0.2", "ts-jest": "^29.1.2", "tsup": "^6.7.0", "typescript": "^5.4.2"}, "peerDependencies": {"@emotion/react": "^11.8.2", "@emotion/styled": "^11.8.1", "@mui/lab": "^6.0.0-beta.14", "@mui/material": "^6.1.7", "@mui/x-data-grid": "^7.23.5", "@refinedev/core": "^4.46.1", "@refinedev/react-hook-form": "^4.0.0", "dayjs": "^1.10.7", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0", "react-hook-form": "^7.57.0"}, "publishConfig": {"access": "public"}}