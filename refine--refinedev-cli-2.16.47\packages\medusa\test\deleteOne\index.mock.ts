import nock from "nock";

nock("https://refine-example-storefront.herokuapp.com:443", {
  encodedQueryParams: true,
})
  .delete("/store/customers/me/addresses/addr_01G8ZK866E4X9034N2XM35VZ8M")
  .reply(
    200,
    {
      customer: {
        id: "cus_01G8GCWQX1EN8CF7PX2W3HEGYM",
        created_at: "2022-07-21T12:49:32.059Z",
        updated_at: "2022-07-27T10:14:41.048Z",
        deleted_at: null,
        email: "<EMAIL>",
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        billing_address_id: null,
        phone: null,
        has_account: true,
        metadata: null,
        shipping_addresses: [
          {
            id: "addr_01G8ZK9FMJG86YBDWXQ2R4FSJF",
            created_at: "2022-07-27T10:29:57.516Z",
            updated_at: "2022-07-27T10:29:57.516Z",
            deleted_at: null,
            customer_id: "cus_01G8GCWQX1EN8CF7PX2W3HEGYM",
            company: null,
            first_name: "<PERSON>",
            last_name: "Blog<PERSON>",
            address_1: "123 Main St",
            address_2: null,
            city: "Manchester",
            country_code: "gb",
            province: null,
            postal_code: "10001",
            phone: null,
            metadata: null,
          },
          {
            id: "addr_01G8ZKB1AF9FRJD7HXF4Q9SE9R",
            created_at: "2022-07-27T10:30:48.393Z",
            updated_at: "2022-07-27T10:30:48.393Z",
            deleted_at: null,
            customer_id: "cus_01G8GCWQX1EN8CF7PX2W3HEGYM",
            company: null,
            first_name: "Angel",
            last_name: "Doe",
            address_1: "123 Main St",
            address_2: null,
            city: "New York",
            country_code: "us",
            province: null,
            postal_code: "10001",
            phone: null,
            metadata: null,
          },
        ],
        billing_address: null,
      },
    },
    [
      "Server",
      "Cowboy",
      "Connection",
      "close",
      "X-Powered-By",
      "Express",
      "Vary",
      "Origin",
      "Access-Control-Allow-Credentials",
      "true",
      "Content-Type",
      "application/json; charset=utf-8",
      "Content-Length",
      "1114",
      "Etag",
      'W/"45a-S8ZT0SbgCzIJuG0pAHnomkmIysY"',
      "Date",
      "Wed, 27 Jul 2022 10:32:36 GMT",
      "Via",
      "1.1 vegur",
    ],
  );
