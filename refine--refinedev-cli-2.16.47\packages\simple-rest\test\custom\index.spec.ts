import axios from "axios";

import JsonServer from "../../src/index";
import "./index.mock";

describe("custom", () => {
  const API_URL = "https://api.fake-rest.refine.dev";

  it("correct get response", async () => {
    const response = await JsonServer(API_URL, axios).custom!({
      url: `${API_URL}/users`,
      method: "get",
    });

    expect(response.data[0]["id"]).toBe(1);
    expect(response.data[0]["email"]).toBe("<EMAIL>");
  });

  it("correct filter response", async () => {
    const response = await JsonServer(API_URL, axios).custom!({
      url: `${API_URL}/users`,
      method: "get",
      filters: [
        {
          field: "email",
          operator: "eq",
          value: "<EMAIL>",
        },
      ],
    });

    expect(response.data[0]["id"]).toBe(1);
    expect(response.data[0]["email"]).toBe("<EMAIL>");
  });

  it("correct sort response", async () => {
    const response = await JsonServer(API_URL, axios).custom!({
      url: `${API_URL}/users`,
      method: "get",
      sorters: [
        {
          field: "id",
          order: "asc",
        },
      ],
    });

    expect(response.data[0]["id"]).toBe(1);
  });

  it("correct post request", async () => {
    const response = await JsonServer(API_URL, axios).custom!({
      url: `${API_URL}/users`,
      method: "post",
      payload: {
        firstName: "test",
        lastName: "test",
        email: "<EMAIL>",
        status: true,
      },
    });

    expect(response.data).toEqual({
      email: "<EMAIL>",
      firstName: "test",
      id: 51,
      lastName: "test",
      status: true,
    });
  });
});
