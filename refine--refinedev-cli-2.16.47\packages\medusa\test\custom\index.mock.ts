import nock from "nock";

nock("https://refine-example-storefront.herokuapp.com:443", {
  encodedQueryParams: true,
})
  .get("/store/customers/me")
  .query({})
  .reply(
    200,
    {
      customer: {
        id: "cus_01G8GCWQX1EN8CF7PX2W3HEGYM",
        created_at: "2022-07-21T12:49:32.059Z",
        updated_at: "2022-07-27T10:14:41.048Z",
        deleted_at: null,
        email: "<EMAIL>",
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        billing_address_id: null,
        phone: null,
        has_account: true,
        metadata: null,
        shipping_addresses: [],
        billing_address: null,
      },
    },
    [
      "Server",
      "Cowboy",
      "Connection",
      "close",
      "X-Powered-By",
      "Express",
      "Vary",
      "Origin",
      "Access-Control-Allow-Credentials",
      "true",
      "Content-Type",
      "application/json; charset=utf-8",
      "Content-Length",
      "336",
      "Etag",
      'W/"150-tZ/M/hmxhQUg6cHFU0qiLdiKlcc"',
      "Date",
      "Wed, 27 Jul 2022 11:25:38 GMT",
      "Via",
      "1.1 vegur",
    ],
  );

nock("https://refine-example-storefront.herokuapp.com:443", {
  encodedQueryParams: true,
})
  .get("/store/products")
  .query({ handle: "hoodie" })
  .reply(
    200,
    {
      products: [
        {
          id: "prod_01G79W21Y2X62MSX7F62Z2K1GR",
          created_at: "2022-07-06T13:44:11.005Z",
          updated_at: "2022-07-14T13:30:26.367Z",
          deleted_at: null,
          title: "Medusa Hoodie",
          subtitle: null,
          description:
            "Reimagine the feeling of a classic hoodie. With our cotton hoodie, everyday essentials no longer have to be ordinary.",
          handle: "hoodie",
          is_giftcard: false,
          status: "published",
          thumbnail:
            "https://medusa-public-images.s3.eu-west-1.amazonaws.com/black_hoodie_front.png",
          profile_id: "sp_01G79TC1JRFMCE719R8XN61CKC",
          weight: 400,
          length: null,
          height: null,
          width: null,
          hs_code: "",
          origin_country: null,
          mid_code: "",
          material: null,
          collection_id: "pcol_01G7YEBHSSJQ6M5MY09BS8VEB5",
          type_id: null,
          discountable: true,
          external_id: null,
          metadata: null,
          variants: [
            {
              id: "variant_01G79W21Z23GFN6N835KK1JZ4C",
              created_at: "2022-07-06T13:44:11.005Z",
              updated_at: "2022-07-06T13:44:11.005Z",
              deleted_at: null,
              title: "S",
              product_id: "prod_01G79W21Y2X62MSX7F62Z2K1GR",
              sku: null,
              barcode: null,
              ean: null,
              upc: null,
              inventory_quantity: 100,
              allow_backorder: false,
              manage_inventory: true,
              hs_code: null,
              origin_country: null,
              mid_code: null,
              material: null,
              weight: null,
              length: null,
              height: null,
              width: null,
              metadata: null,
              options: [
                {
                  id: "optval_01G79W21Z2QJ8JRAMARJ6YJJ84",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  value: "S",
                  option_id: "opt_01G79W21YA27KTJP8QKAXHXJNV",
                  variant_id: "variant_01G79W21Z23GFN6N835KK1JZ4C",
                  metadata: null,
                },
              ],
              prices: [
                {
                  id: "ma_01G79W21Z93VYJSRD817TBXW7N",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  currency_code: "eur",
                  amount: 3650,
                  min_quantity: null,
                  max_quantity: null,
                  price_list_id: null,
                  variant_id: "variant_01G79W21Z23GFN6N835KK1JZ4C",
                  region_id: null,
                  price_list: null,
                },
                {
                  id: "ma_01G79W21ZDMB572K9HHCQY2J25",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  currency_code: "usd",
                  amount: 4150,
                  min_quantity: null,
                  max_quantity: null,
                  price_list_id: null,
                  variant_id: "variant_01G79W21Z23GFN6N835KK1JZ4C",
                  region_id: null,
                  price_list: null,
                },
              ],
              original_price: null,
              calculated_price: null,
            },
            {
              id: "variant_01G79W21ZTW152NM1QCW2W8J0Y",
              created_at: "2022-07-06T13:44:11.005Z",
              updated_at: "2022-07-06T13:44:11.005Z",
              deleted_at: null,
              title: "M",
              product_id: "prod_01G79W21Y2X62MSX7F62Z2K1GR",
              sku: null,
              barcode: null,
              ean: null,
              upc: null,
              inventory_quantity: 100,
              allow_backorder: false,
              manage_inventory: true,
              hs_code: null,
              origin_country: null,
              mid_code: null,
              material: null,
              weight: null,
              length: null,
              height: null,
              width: null,
              metadata: null,
              options: [
                {
                  id: "optval_01G79W21ZTET99PDP96Z46E5P8",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  value: "M",
                  option_id: "opt_01G79W21YA27KTJP8QKAXHXJNV",
                  variant_id: "variant_01G79W21ZTW152NM1QCW2W8J0Y",
                  metadata: null,
                },
              ],
              prices: [
                {
                  id: "ma_01G79W21ZZG8ZWAPXX91PTF95Q",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  currency_code: "eur",
                  amount: 3650,
                  min_quantity: null,
                  max_quantity: null,
                  price_list_id: null,
                  variant_id: "variant_01G79W21ZTW152NM1QCW2W8J0Y",
                  region_id: null,
                  price_list: null,
                },
                {
                  id: "ma_01G79W2202CXPM1C013AN2VVVC",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  currency_code: "usd",
                  amount: 4150,
                  min_quantity: null,
                  max_quantity: null,
                  price_list_id: null,
                  variant_id: "variant_01G79W21ZTW152NM1QCW2W8J0Y",
                  region_id: null,
                  price_list: null,
                },
              ],
              original_price: null,
              calculated_price: null,
            },
            {
              id: "variant_01G79W220E9ST41D1JG9XKGW89",
              created_at: "2022-07-06T13:44:11.005Z",
              updated_at: "2022-07-06T13:44:11.005Z",
              deleted_at: null,
              title: "L",
              product_id: "prod_01G79W21Y2X62MSX7F62Z2K1GR",
              sku: null,
              barcode: null,
              ean: null,
              upc: null,
              inventory_quantity: 100,
              allow_backorder: false,
              manage_inventory: true,
              hs_code: null,
              origin_country: null,
              mid_code: null,
              material: null,
              weight: null,
              length: null,
              height: null,
              width: null,
              metadata: null,
              options: [
                {
                  id: "optval_01G79W220FM4BFFQ1JEDCQ2QN1",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  value: "L",
                  option_id: "opt_01G79W21YA27KTJP8QKAXHXJNV",
                  variant_id: "variant_01G79W220E9ST41D1JG9XKGW89",
                  metadata: null,
                },
              ],
              prices: [
                {
                  id: "ma_01G79W220NX0N759VCA8M7ZRQF",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  currency_code: "eur",
                  amount: 3650,
                  min_quantity: null,
                  max_quantity: null,
                  price_list_id: null,
                  variant_id: "variant_01G79W220E9ST41D1JG9XKGW89",
                  region_id: null,
                  price_list: null,
                },
                {
                  id: "ma_01G79W220SWFA2KSBDSD9XQ45G",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  currency_code: "usd",
                  amount: 4150,
                  min_quantity: null,
                  max_quantity: null,
                  price_list_id: null,
                  variant_id: "variant_01G79W220E9ST41D1JG9XKGW89",
                  region_id: null,
                  price_list: null,
                },
              ],
              original_price: null,
              calculated_price: null,
            },
            {
              id: "variant_01G79W22173DVWVBDPPVDMR3E3",
              created_at: "2022-07-06T13:44:11.005Z",
              updated_at: "2022-07-06T13:44:11.005Z",
              deleted_at: null,
              title: "XL",
              product_id: "prod_01G79W21Y2X62MSX7F62Z2K1GR",
              sku: null,
              barcode: null,
              ean: null,
              upc: null,
              inventory_quantity: 100,
              allow_backorder: false,
              manage_inventory: true,
              hs_code: null,
              origin_country: null,
              mid_code: null,
              material: null,
              weight: null,
              length: null,
              height: null,
              width: null,
              metadata: null,
              options: [
                {
                  id: "optval_01G79W221875NV4CVY38NMJFHP",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  value: "XL",
                  option_id: "opt_01G79W21YA27KTJP8QKAXHXJNV",
                  variant_id: "variant_01G79W22173DVWVBDPPVDMR3E3",
                  metadata: null,
                },
              ],
              prices: [
                {
                  id: "ma_01G79W221DYP1FP0E6AQ5NHB5Q",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  currency_code: "eur",
                  amount: 3650,
                  min_quantity: null,
                  max_quantity: null,
                  price_list_id: null,
                  variant_id: "variant_01G79W22173DVWVBDPPVDMR3E3",
                  region_id: null,
                  price_list: null,
                },
                {
                  id: "ma_01G79W221HTVF1D3NTWGDZH8XW",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  currency_code: "usd",
                  amount: 4150,
                  min_quantity: null,
                  max_quantity: null,
                  price_list_id: null,
                  variant_id: "variant_01G79W22173DVWVBDPPVDMR3E3",
                  region_id: null,
                  price_list: null,
                },
              ],
              original_price: null,
              calculated_price: null,
            },
          ],
          options: [
            {
              id: "opt_01G79W21YA27KTJP8QKAXHXJNV",
              created_at: "2022-07-06T13:44:11.005Z",
              updated_at: "2022-07-06T13:44:11.005Z",
              deleted_at: null,
              title: "Size",
              product_id: "prod_01G79W21Y2X62MSX7F62Z2K1GR",
              metadata: null,
              values: [
                {
                  id: "optval_01G79W21Z2QJ8JRAMARJ6YJJ84",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  value: "S",
                  option_id: "opt_01G79W21YA27KTJP8QKAXHXJNV",
                  variant_id: "variant_01G79W21Z23GFN6N835KK1JZ4C",
                  metadata: null,
                },
                {
                  id: "optval_01G79W21ZTET99PDP96Z46E5P8",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  value: "M",
                  option_id: "opt_01G79W21YA27KTJP8QKAXHXJNV",
                  variant_id: "variant_01G79W21ZTW152NM1QCW2W8J0Y",
                  metadata: null,
                },
                {
                  id: "optval_01G79W220FM4BFFQ1JEDCQ2QN1",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  value: "L",
                  option_id: "opt_01G79W21YA27KTJP8QKAXHXJNV",
                  variant_id: "variant_01G79W220E9ST41D1JG9XKGW89",
                  metadata: null,
                },
                {
                  id: "optval_01G79W221875NV4CVY38NMJFHP",
                  created_at: "2022-07-06T13:44:11.005Z",
                  updated_at: "2022-07-06T13:44:11.005Z",
                  deleted_at: null,
                  value: "XL",
                  option_id: "opt_01G79W21YA27KTJP8QKAXHXJNV",
                  variant_id: "variant_01G79W22173DVWVBDPPVDMR3E3",
                  metadata: null,
                },
              ],
            },
          ],
          images: [
            {
              id: "img_01G79W21Y2Q3JM26KEPJ4EZYWM",
              created_at: "2022-07-06T13:44:11.005Z",
              updated_at: "2022-07-06T13:44:11.005Z",
              deleted_at: null,
              url: "https://medusa-public-images.s3.eu-west-1.amazonaws.com/black_hoodie_front.png",
              metadata: null,
            },
            {
              id: "img_01G79W21Y264M7BSVJMXP4787Q",
              created_at: "2022-07-06T13:44:11.005Z",
              updated_at: "2022-07-06T13:44:11.005Z",
              deleted_at: null,
              url: "https://medusa-public-images.s3.eu-west-1.amazonaws.com/black_hoodie_back.png",
              metadata: null,
            },
          ],
          tags: [
            {
              id: "ptag_01G7CDNGXSDSDNNEMCRDEXBDXG",
              created_at: "2022-07-07T13:30:24.457Z",
              updated_at: "2022-07-07T13:30:24.457Z",
              deleted_at: null,
              value: "Winter",
              metadata: null,
            },
          ],
          collection: {
            id: "pcol_01G7YEBHSSJQ6M5MY09BS8VEB5",
            created_at: "2022-07-14T13:28:46.134Z",
            updated_at: "2022-07-14T13:28:46.134Z",
            deleted_at: null,
            title: "New Arrivals",
            handle: "new-arrivals",
            metadata: {},
          },
          type: null,
        },
      ],
      count: 1,
      offset: 0,
      limit: 100,
    },
    [
      "Server",
      "Cowboy",
      "Connection",
      "close",
      "X-Powered-By",
      "Express",
      "Vary",
      "Origin",
      "Access-Control-Allow-Credentials",
      "true",
      "Content-Type",
      "application/json; charset=utf-8",
      "Content-Length",
      "8737",
      "Etag",
      'W/"2221-hAuwRwHOnU6SoXYY2uBd3TAGeNE"',
      "Set-Cookie",
      "connect.sid=s%3AT_CjtXgJSPMGWmuYlJwIN4ZUrWB3gpxe.YYRob0i2XON0c%2Fk58%2BaG7bkMrxOs2G5SjbbUrmZhSsE; Path=/; Expires=Wed, 27 Jul 2022 21:42:55 GMT; HttpOnly; Secure; SameSite=None",
      "Date",
      "Wed, 27 Jul 2022 11:42:55 GMT",
      "Via",
      "1.1 vegur",
    ],
  );

nock("https://refine-example-storefront.herokuapp.com:443", {
  encodedQueryParams: true,
})
  .post("/store/customers/me/addresses", {
    address: {
      first_name: "John",
      last_name: "Doe",
      address_1: "123 Main St",
      city: "New York",
      country_code: "US",
      postal_code: "10001",
    },
  })
  .reply(
    200,
    {
      customer: {
        id: "cus_01G8GCWQX1EN8CF7PX2W3HEGYM",
        created_at: "2022-07-21T12:49:32.059Z",
        updated_at: "2022-07-27T10:14:41.048Z",
        deleted_at: null,
        email: "<EMAIL>",
        first_name: "John",
        last_name: "Doe",
        billing_address_id: null,
        phone: null,
        has_account: true,
        metadata: null,
        shipping_addresses: [
          {
            id: "addr_01G8ZR6E99AGX7VJPFH4ATJF88",
            created_at: "2022-07-27T11:55:40.691Z",
            updated_at: "2022-07-27T11:55:40.691Z",
            deleted_at: null,
            customer_id: "cus_01G8GCWQX1EN8CF7PX2W3HEGYM",
            company: null,
            first_name: "John",
            last_name: "Doe",
            address_1: "123 Main St",
            address_2: null,
            city: "New York",
            country_code: "us",
            province: null,
            postal_code: "10001",
            phone: null,
            metadata: null,
          },
        ],
        billing_address: null,
      },
    },
    [
      "Server",
      "Cowboy",
      "Connection",
      "close",
      "X-Powered-By",
      "Express",
      "Vary",
      "Origin",
      "Access-Control-Allow-Credentials",
      "true",
      "Content-Type",
      "application/json; charset=utf-8",
      "Content-Length",
      "722",
      "Etag",
      'W/"2d2-W1S0BqoiM7PHhGBZyl/fKvtk+MA"',
      "Date",
      "Wed, 27 Jul 2022 11:55:40 GMT",
      "Via",
      "1.1 vegur",
    ],
  );
nock("https://refine-example-storefront.herokuapp.com:443", {
  encodedQueryParams: true,
})
  .delete("/store/customers/me/addresses/addr_01G8ZR6E99AGX7VJPFH4ATJF88")
  .reply(
    200,
    {
      customer: {
        id: "cus_01G8GCWQX1EN8CF7PX2W3HEGYM",
        created_at: "2022-07-21T12:49:32.059Z",
        updated_at: "2022-07-27T10:14:41.048Z",
        deleted_at: null,
        email: "<EMAIL>",
        first_name: "John",
        last_name: "Doe",
        billing_address_id: null,
        phone: null,
        has_account: true,
        metadata: null,
        shipping_addresses: [],
        billing_address: null,
      },
    },
    [
      "Server",
      "Cowboy",
      "Connection",
      "close",
      "X-Powered-By",
      "Express",
      "Vary",
      "Origin",
      "Access-Control-Allow-Credentials",
      "true",
      "Content-Type",
      "application/json; charset=utf-8",
      "Content-Length",
      "336",
      "Etag",
      'W/"150-tZ/M/hmxhQUg6cHFU0qiLdiKlcc"',
      "Date",
      "Wed, 27 Jul 2022 13:17:25 GMT",
      "Via",
      "1.1 vegur",
    ],
  );
