{"version": 3, "file": "index.esm.js", "sources": ["../../src/columnHelper.ts", "../../src/utils.ts", "../../src/core/cell.ts", "../../src/core/column.ts", "../../src/core/headers.ts", "../../src/core/row.ts", "../../src/features/ColumnFaceting.ts", "../../src/filterFns.ts", "../../src/features/ColumnFiltering.ts", "../../src/aggregationFns.ts", "../../src/features/ColumnGrouping.ts", "../../src/features/ColumnOrdering.ts", "../../src/features/ColumnPinning.ts", "../../src/features/ColumnSizing.ts", "../../src/features/ColumnVisibility.ts", "../../src/features/GlobalFaceting.ts", "../../src/features/GlobalFiltering.ts", "../../src/features/RowExpanding.ts", "../../src/features/RowPagination.ts", "../../src/features/RowPinning.ts", "../../src/features/RowSelection.ts", "../../src/sortingFns.ts", "../../src/features/RowSorting.ts", "../../src/core/table.ts", "../../src/utils/getCoreRowModel.ts", "../../src/utils/getExpandedRowModel.ts", "../../src/utils/getFacetedMinMaxValues.ts", "../../src/utils/filterRowsUtils.ts", "../../src/utils/getFacetedRowModel.ts", "../../src/utils/getFacetedUniqueValues.ts", "../../src/utils/getFilteredRowModel.ts", "../../src/utils/getGroupedRowModel.ts", "../../src/utils/getPaginationRowModel.ts", "../../src/utils/getSortedRowModel.ts"], "sourcesContent": ["import {\n  AccessorFn,\n  AccessorFnColumnDef,\n  AccessorKeyColumnDef,\n  DisplayColumnDef,\n  GroupColumnDef,\n  IdentifiedColumnDef,\n  RowData,\n} from './types'\nimport { DeepKeys, DeepValue } from './utils'\n\n// type Person = {\n//   firstName: string\n//   lastName: string\n//   age: number\n//   visits: number\n//   status: string\n//   progress: number\n//   createdAt: Date\n//   nested: {\n//     foo: [\n//       {\n//         bar: 'bar'\n//       }\n//     ]\n//     bar: { subBar: boolean }[]\n//     baz: {\n//       foo: 'foo'\n//       bar: {\n//         baz: 'baz'\n//       }\n//     }\n//   }\n// }\n\n// const test: DeepKeys<Person> = 'nested.foo.0.bar'\n// const test2: DeepKeys<Person> = 'nested.bar'\n\n// const helper = createColumnHelper<Person>()\n\n// helper.accessor('nested.foo', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.foo.0.bar', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.bar', {\n//   cell: info => info.getValue(),\n// })\n\nexport type ColumnHelper<TData extends RowData> = {\n  accessor: <\n    TAccessor extends AccessorFn<TData> | DeepKeys<TData>,\n    TValue extends TAccessor extends AccessorFn<TData, infer TReturn>\n      ? TReturn\n      : TAccessor extends DeepKeys<TData>\n        ? DeepValue<TData, TAccessor>\n        : never,\n  >(\n    accessor: TAccessor,\n    column: TAccessor extends AccessorFn<TData>\n      ? DisplayColumnDef<TData, TValue>\n      : IdentifiedColumnDef<TData, TValue>\n  ) => TAccessor extends AccessorFn<TData>\n    ? AccessorFnColumnDef<TData, TValue>\n    : AccessorKeyColumnDef<TData, TValue>\n  display: (column: DisplayColumnDef<TData>) => DisplayColumnDef<TData, unknown>\n  group: (column: GroupColumnDef<TData>) => GroupColumnDef<TData, unknown>\n}\n\nexport function createColumnHelper<\n  TData extends RowData,\n>(): ColumnHelper<TData> {\n  return {\n    accessor: (accessor, column) => {\n      return typeof accessor === 'function'\n        ? ({\n            ...column,\n            accessorFn: accessor,\n          } as any)\n        : {\n            ...column,\n            accessorKey: accessor,\n          }\n    },\n    display: column => column,\n    group: column => column,\n  }\n}\n", "import { TableOptionsResolved, TableState, Updater } from './types'\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\nexport type RequiredKeys<T, K extends keyof T> = Omit<T, K> &\n  Required<Pick<T, K>>\nexport type Overwrite<T, U extends { [TKey in keyof T]?: any }> = Omit<\n  T,\n  keyof U\n> &\n  U\n\nexport type UnionToIntersection<T> = (\n  T extends any ? (x: T) => any : never\n) extends (x: infer R) => any\n  ? R\n  : never\n\nexport type IsAny<T, Y, N> = 1 extends 0 & T ? Y : N\nexport type IsKnown<T, Y, N> = unknown extends T ? N : Y\n\ntype ComputeRange<\n  N extends number,\n  Result extends Array<unknown> = [],\n> = Result['length'] extends N\n  ? Result\n  : ComputeRange<N, [...Result, Result['length']]>\ntype Index40 = ComputeRange<40>[number]\n\n// Is this type a tuple?\ntype IsTuple<T> = T extends readonly any[] & { length: infer Length }\n  ? Length extends Index40\n    ? T\n    : never\n  : never\n\n// If this type is a tuple, what indices are allowed?\ntype AllowedIndexes<\n  Tuple extends ReadonlyArray<any>,\n  Keys extends number = never,\n> = Tuple extends readonly []\n  ? Keys\n  : Tuple extends readonly [infer _, ...infer Tail]\n    ? AllowedIndexes<Tail, Keys | Tail['length']>\n    : Keys\n\nexport type DeepKeys<T, TDepth extends any[] = []> = TDepth['length'] extends 5\n  ? never\n  : unknown extends T\n    ? string\n    : T extends readonly any[] & IsTuple<T>\n      ? AllowedIndexes<T> | DeepKeysPrefix<T, AllowedIndexes<T>, TDepth>\n      : T extends any[]\n        ? DeepKeys<T[number], [...TDepth, any]>\n        : T extends Date\n          ? never\n          : T extends object\n            ? (keyof T & string) | DeepKeysPrefix<T, keyof T, TDepth>\n            : never\n\ntype DeepKeysPrefix<\n  T,\n  TPrefix,\n  TDepth extends any[],\n> = TPrefix extends keyof T & (number | string)\n  ? `${TPrefix}.${DeepKeys<T[TPrefix], [...TDepth, any]> & string}`\n  : never\n\nexport type DeepValue<T, TProp> = T extends Record<string | number, any>\n  ? TProp extends `${infer TBranch}.${infer TDeepProp}`\n    ? DeepValue<T[TBranch], TDeepProp>\n    : T[TProp & string]\n  : never\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport type Getter<TValue> = <TTValue = TValue>() => NoInfer<TTValue>\n\n///\n\nexport function functionalUpdate<T>(updater: Updater<T>, input: T): T {\n  return typeof updater === 'function'\n    ? (updater as (input: T) => T)(input)\n    : updater\n}\n\nexport function noop() {\n  //\n}\n\nexport function makeStateUpdater<K extends keyof TableState>(\n  key: K,\n  instance: unknown\n) {\n  return (updater: Updater<TableState[K]>) => {\n    ;(instance as any).setState(<TTableState>(old: TTableState) => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, (old as any)[key]),\n      }\n    })\n  }\n}\n\ntype AnyFunction = (...args: any) => any\n\nexport function isFunction<T extends AnyFunction>(d: any): d is T {\n  return d instanceof Function\n}\n\nexport function isNumberArray(d: any): d is number[] {\n  return Array.isArray(d) && d.every(val => typeof val === 'number')\n}\n\nexport function flattenBy<TNode>(\n  arr: TNode[],\n  getChildren: (item: TNode) => TNode[]\n) {\n  const flat: TNode[] = []\n\n  const recurse = (subArr: TNode[]) => {\n    subArr.forEach(item => {\n      flat.push(item)\n      const children = getChildren(item)\n      if (children?.length) {\n        recurse(children)\n      }\n    })\n  }\n\n  recurse(arr)\n\n  return flat\n}\n\nexport function memo<TDeps extends readonly any[], TDepArgs, TResult>(\n  getDeps: (depArgs?: TDepArgs) => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: any\n    debug?: () => any\n    onChange?: (result: TResult) => void\n  }\n): (depArgs?: TDepArgs) => TResult {\n  let deps: any[] = []\n  let result: TResult | undefined\n\n  return depArgs => {\n    let depTime: number\n    if (opts.key && opts.debug) depTime = Date.now()\n\n    const newDeps = getDeps(depArgs)\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug) resultTime = Date.now()\n\n    result = fn(...newDeps)\n    opts?.onChange?.(result)\n\n    if (opts.key && opts.debug) {\n      if (opts?.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n        const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n        const resultFpsPercentage = resultEndTime / 16\n\n        const pad = (str: number | string, num: number) => {\n          str = String(str)\n          while (str.length < num) {\n            str = ' ' + str\n          }\n          return str\n        }\n\n        console.info(\n          `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n          `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120)\n            )}deg 100% 31%);`,\n          opts?.key\n        )\n      }\n    }\n\n    return result!\n  }\n}\n\nexport function getMemoOptions(\n  tableOptions: Partial<TableOptionsResolved<any>>,\n  debugLevel:\n    | 'debugAll'\n    | 'debugCells'\n    | 'debugTable'\n    | 'debugColumns'\n    | 'debugRows'\n    | 'debugHeaders',\n  key: string,\n  onChange?: (result: any) => void\n) {\n  return {\n    debug: () => tableOptions?.debugAll ?? tableOptions[debugLevel],\n    key: process.env.NODE_ENV === 'development' && key,\n    onChange,\n  }\n}\n", "import { RowData, Cell, Column, Row, Table } from '../types'\nimport { Getter, getMemoOptions, memo } from '../utils'\n\nexport interface CellContext<TData extends RowData, TValue> {\n  cell: Cell<TData, TValue>\n  column: Column<TData, TValue>\n  getValue: Getter<TValue>\n  renderValue: Getter<TValue | null>\n  row: Row<TData>\n  table: Table<TData>\n}\n\nexport interface CoreCell<TData extends RowData, TValue> {\n  /**\n   * The associated Column object for the cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  column: Column<TData, TValue>\n  /**\n   * Returns the rendering context (or props) for cell-based components like cells and aggregated cells. Use these props with your framework's `flexRender` utility to render these using the template of your choice:\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  getContext: () => CellContext<TData, TValue>\n  /**\n   * Returns the value for the cell, accessed via the associated column's accessor key or accessor function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  getValue: CellContext<TData, TValue>['getValue']\n  /**\n   * The unique ID for the cell across the entire table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  id: string\n  /**\n   * Renders the value for a cell the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  renderValue: CellContext<TData, TValue>['renderValue']\n  /**\n   * The associated Row object for the cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#row)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  row: Row<TData>\n}\n\nexport function createCell<TData extends RowData, TValue>(\n  table: Table<TData>,\n  row: Row<TData>,\n  column: Column<TData, TValue>,\n  columnId: string\n): Cell<TData, TValue> {\n  const getRenderValue = () =>\n    cell.getValue() ?? table.options.renderFallbackValue\n\n  const cell: CoreCell<TData, TValue> = {\n    id: `${row.id}_${column.id}`,\n    row,\n    column,\n    getValue: () => row.getValue(columnId),\n    renderValue: getRenderValue,\n    getContext: memo(\n      () => [table, column, row, cell],\n      (table, column, row, cell) => ({\n        table,\n        column,\n        row,\n        cell: cell as Cell<TData, TValue>,\n        getValue: cell.getValue,\n        renderValue: cell.renderValue,\n      }),\n      getMemoOptions(table.options, 'debugCells', 'cell.getContext')\n    ),\n  }\n\n  table._features.forEach(feature => {\n    feature.createCell?.(\n      cell as Cell<TData, TValue>,\n      column,\n      row as Row<TData>,\n      table\n    )\n  }, {})\n\n  return cell as Cell<TData, TValue>\n}\n", "import {\n  Column,\n  Table,\n  AccessorFn,\n  ColumnDef,\n  RowData,\n  ColumnDefResolved,\n} from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport interface CoreColumn<TData extends RowData, TValue> {\n  /**\n   * The resolved accessor function to use when extracting the value for the column from each row. Will only be defined if the column def has a valid accessor key or function defined.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#accessorfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  accessorFn?: AccessorFn<TData, TValue>\n  /**\n   * The original column def used to create the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columndef)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columnDef: ColumnDef<TData, TValue>\n  /**\n   * The child column (if the column is a group column). Will be an empty array if the column is not a group column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columns: Column<TData, TValue>[]\n  /**\n   * The depth of the column (if grouped) relative to the root column def array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  depth: number\n  /**\n   * Returns the flattened array of this column and all child/grand-child columns for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getFlatColumns: () => Column<TData, TValue>[]\n  /**\n   * Returns an array of all leaf-node columns for this column. If a column has no children, it is considered the only leaf-node column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getLeafColumns: () => Column<TData, TValue>[]\n  /**\n   * The resolved unique identifier for the column resolved in this priority:\n      - A manual `id` property from the column def\n      - The accessor key from the column def\n      - The header string from the column def\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  id: string\n  /**\n   * The parent column for this column. Will be undefined if this is a root column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#parent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  parent?: Column<TData, TValue>\n}\n\nexport function createColumn<TData extends RowData, TValue>(\n  table: Table<TData>,\n  columnDef: ColumnDef<TData, TValue>,\n  depth: number,\n  parent?: Column<TData, TValue>\n): Column<TData, TValue> {\n  const defaultColumn = table._getDefaultColumnDef()\n\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef,\n  } as ColumnDefResolved<TData>\n\n  const accessorKey = resolvedColumnDef.accessorKey\n\n  let id =\n    resolvedColumnDef.id ??\n    (accessorKey ? accessorKey.replace('.', '_') : undefined) ??\n    (typeof resolvedColumnDef.header === 'string'\n      ? resolvedColumnDef.header\n      : undefined)\n\n  let accessorFn: AccessorFn<TData> | undefined\n\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = (originalRow: TData) => {\n        let result = originalRow as Record<string, any>\n\n        for (const key of accessorKey.split('.')) {\n          result = result?.[key]\n          if (process.env.NODE_ENV !== 'production' && result === undefined) {\n            console.warn(\n              `\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`\n            )\n          }\n        }\n\n        return result\n      }\n    } else {\n      accessorFn = (originalRow: TData) =>\n        (originalRow as any)[resolvedColumnDef.accessorKey]\n    }\n  }\n\n  if (!id) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(\n        resolvedColumnDef.accessorFn\n          ? `Columns require an id when using an accessorFn`\n          : `Columns require an id when using a non-string header`\n      )\n    }\n    throw new Error()\n  }\n\n  let column: CoreColumn<TData, any> = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent as any,\n    depth,\n    columnDef: resolvedColumnDef as ColumnDef<TData, any>,\n    columns: [],\n    getFlatColumns: memo(\n      () => [true],\n      () => {\n        return [\n          column as Column<TData, TValue>,\n          ...column.columns?.flatMap(d => d.getFlatColumns()),\n        ]\n      },\n      getMemoOptions(table.options, 'debugColumns', 'column.getFlatColumns')\n    ),\n    getLeafColumns: memo(\n      () => [table._getOrderColumnsFn()],\n      orderColumns => {\n        if (column.columns?.length) {\n          let leafColumns = column.columns.flatMap(column =>\n            column.getLeafColumns()\n          )\n\n          return orderColumns(leafColumns)\n        }\n\n        return [column as Column<TData, TValue>]\n      },\n      getMemoOptions(table.options, 'debugColumns', 'column.getLeafColumns')\n    ),\n  }\n\n  for (const feature of table._features) {\n    feature.createColumn?.(column as Column<TData, TValue>, table)\n  }\n\n  // Yes, we have to convert table to unknown, because we know more than the compiler here.\n  return column as Column<TData, TValue>\n}\n", "import {\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  HeaderGroup,\n  Table,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nconst debug = 'debugHeaders'\n\nexport interface CoreHeaderGroup<TData extends RowData> {\n  depth: number\n  headers: Header<TData, unknown>[]\n  id: string\n}\n\nexport interface HeaderContext<TData, TValue> {\n  /**\n   * An instance of a column.\n   */\n  column: Column<TData, TValue>\n  /**\n   * An instance of a header.\n   */\n  header: Header<TData, TValue>\n  /**\n   * The table instance.\n   */\n  table: Table<TData>\n}\n\nexport interface CoreHeader<TData extends RowData, TValue> {\n  /**\n   * The col-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#colspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  colSpan: number\n  /**\n   * The header's associated column object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  column: Column<TData, TValue>\n  /**\n   * The depth of the header, zero-indexed based.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  depth: number\n  /**\n   * Returns the rendering context (or props) for column-based components like headers, footers and filters.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getContext: () => HeaderContext<TData, TValue>\n  /**\n   * Returns the leaf headers hierarchically nested under this header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * The header's associated header group object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#headergroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  headerGroup: HeaderGroup<TData>\n  /**\n   * The unique identifier for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  id: string\n  /**\n   * The index for the header within the header group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  index: number\n  /**\n   * A boolean denoting if the header is a placeholder header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#isplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  isPlaceholder: boolean\n  /**\n   * If the header is a placeholder header, this will be a unique header ID that does not conflict with any other headers across the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#placeholderid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  placeholderId?: string\n  /**\n   * The row-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#rowspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  rowSpan: number\n  /**\n   * The header's hierarchical sub/child headers. Will be empty if the header's associated column is a leaf-column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#subheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  subHeaders: Header<TData, TValue>[]\n}\n\nexport interface HeadersInstance<TData extends RowData> {\n  /**\n   * Returns all header groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightHeaderGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns the footer groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFooterGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns headers for all columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFlatHeaders: () => Header<TData, unknown>[]\n\n  /**\n   * Returns headers for all leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightLeafHeaders: () => Header<TData, unknown>[]\n}\n\n//\n\nfunction createHeader<TData extends RowData, TValue>(\n  table: Table<TData>,\n  column: Column<TData, TValue>,\n  options: {\n    id?: string\n    isPlaceholder?: boolean\n    placeholderId?: string\n    index: number\n    depth: number\n  }\n): Header<TData, TValue> {\n  const id = options.id ?? column.id\n\n  let header: CoreHeader<TData, TValue> = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null!,\n    getLeafHeaders: (): Header<TData, unknown>[] => {\n      const leafHeaders: Header<TData, unknown>[] = []\n\n      const recurseHeader = (h: CoreHeader<TData, any>) => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader)\n        }\n        leafHeaders.push(h as Header<TData, unknown>)\n      }\n\n      recurseHeader(header)\n\n      return leafHeaders\n    },\n    getContext: () => ({\n      table,\n      header: header as Header<TData, TValue>,\n      column,\n    }),\n  }\n\n  table._features.forEach(feature => {\n    feature.createHeader?.(header as Header<TData, TValue>, table)\n  })\n\n  return header as Header<TData, TValue>\n}\n\nexport const Headers: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    // Header Groups\n\n    table.getHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        const leftColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const rightColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const centerColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n\n        const headerGroups = buildHeaderGroups(\n          allColumns,\n          [...leftColumns, ...centerColumns, ...rightColumns],\n          table\n        )\n\n        return headerGroups\n      },\n      getMemoOptions(table.options, debug, 'getHeaderGroups')\n    )\n\n    table.getCenterHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        leafColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n        return buildHeaderGroups(allColumns, leafColumns, table, 'center')\n      },\n      getMemoOptions(table.options, debug, 'getCenterHeaderGroups')\n    )\n\n    table.getLeftHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n      ],\n      (allColumns, leafColumns, left) => {\n        const orderedLeafColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'left')\n      },\n      getMemoOptions(table.options, debug, 'getLeftHeaderGroups')\n    )\n\n    table.getRightHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, right) => {\n        const orderedLeafColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'right')\n      },\n      getMemoOptions(table.options, debug, 'getRightHeaderGroups')\n    )\n\n    // Footer Groups\n\n    table.getFooterGroups = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getFooterGroups')\n    )\n\n    table.getLeftFooterGroups = memo(\n      () => [table.getLeftHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getLeftFooterGroups')\n    )\n\n    table.getCenterFooterGroups = memo(\n      () => [table.getCenterHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getCenterFooterGroups')\n    )\n\n    table.getRightFooterGroups = memo(\n      () => [table.getRightHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getRightFooterGroups')\n    )\n\n    // Flat Headers\n\n    table.getFlatHeaders = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return headerGroups\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getFlatHeaders')\n    )\n\n    table.getLeftFlatHeaders = memo(\n      () => [table.getLeftHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getLeftFlatHeaders')\n    )\n\n    table.getCenterFlatHeaders = memo(\n      () => [table.getCenterHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getCenterFlatHeaders')\n    )\n\n    table.getRightFlatHeaders = memo(\n      () => [table.getRightHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getRightFlatHeaders')\n    )\n\n    // Leaf Headers\n\n    table.getCenterLeafHeaders = memo(\n      () => [table.getCenterFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getCenterLeafHeaders')\n    )\n\n    table.getLeftLeafHeaders = memo(\n      () => [table.getLeftFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getLeftLeafHeaders')\n    )\n\n    table.getRightLeafHeaders = memo(\n      () => [table.getRightFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getRightLeafHeaders')\n    )\n\n    table.getLeafHeaders = memo(\n      () => [\n        table.getLeftHeaderGroups(),\n        table.getCenterHeaderGroups(),\n        table.getRightHeaderGroups(),\n      ],\n      (left, center, right) => {\n        return [\n          ...(left[0]?.headers ?? []),\n          ...(center[0]?.headers ?? []),\n          ...(right[0]?.headers ?? []),\n        ]\n          .map(header => {\n            return header.getLeafHeaders()\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getLeafHeaders')\n    )\n  },\n}\n\nexport function buildHeaderGroups<TData extends RowData>(\n  allColumns: Column<TData, unknown>[],\n  columnsToGroup: Column<TData, unknown>[],\n  table: Table<TData>,\n  headerFamily?: 'center' | 'left' | 'right'\n) {\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0\n\n  const findMaxDepth = (columns: Column<TData, unknown>[], depth = 1) => {\n    maxDepth = Math.max(maxDepth, depth)\n\n    columns\n      .filter(column => column.getIsVisible())\n      .forEach(column => {\n        if (column.columns?.length) {\n          findMaxDepth(column.columns, depth + 1)\n        }\n      }, 0)\n  }\n\n  findMaxDepth(allColumns)\n\n  let headerGroups: HeaderGroup<TData>[] = []\n\n  const createHeaderGroup = (\n    headersToGroup: Header<TData, unknown>[],\n    depth: number\n  ) => {\n    // The header group we are creating\n    const headerGroup: HeaderGroup<TData> = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: [],\n    }\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders: Header<TData, unknown>[] = []\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0]\n\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth\n\n      let column: Column<TData, unknown>\n      let isPlaceholder = false\n\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column\n        isPlaceholder = true\n      }\n\n      if (\n        latestPendingParentHeader &&\n        latestPendingParentHeader?.column === column\n      ) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup)\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup?.id]\n            .filter(Boolean)\n            .join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder\n            ? `${pendingParentHeaders.filter(d => d.column === column).length}`\n            : undefined,\n          depth,\n          index: pendingParentHeaders.length,\n        })\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup)\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header)\n      }\n\n      headerGroup.headers.push(headerToGroup)\n      headerToGroup.headerGroup = headerGroup\n    })\n\n    headerGroups.push(headerGroup)\n\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1)\n    }\n  }\n\n  const bottomHeaders = columnsToGroup.map((column, index) =>\n    createHeader(table, column, {\n      depth: maxDepth,\n      index,\n    })\n  )\n\n  createHeaderGroup(bottomHeaders, maxDepth - 1)\n\n  headerGroups.reverse()\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = (\n    headers: Header<TData, unknown>[]\n  ): { colSpan: number; rowSpan: number }[] => {\n    const filteredHeaders = headers.filter(header =>\n      header.column.getIsVisible()\n    )\n\n    return filteredHeaders.map(header => {\n      let colSpan = 0\n      let rowSpan = 0\n      let childRowSpans = [0]\n\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = []\n\n        recurseHeadersForSpans(header.subHeaders).forEach(\n          ({ colSpan: childColSpan, rowSpan: childRowSpan }) => {\n            colSpan += childColSpan\n            childRowSpans.push(childRowSpan)\n          }\n        )\n      } else {\n        colSpan = 1\n      }\n\n      const minChildRowSpan = Math.min(...childRowSpans)\n      rowSpan = rowSpan + minChildRowSpan\n\n      header.colSpan = colSpan\n      header.rowSpan = rowSpan\n\n      return { colSpan, rowSpan }\n    })\n  }\n\n  recurseHeadersForSpans(headerGroups[0]?.headers ?? [])\n\n  return headerGroups\n}\n", "import { RowData, Cell, Row, Table } from '../types'\nimport { flattenBy, getMemoOptions, memo } from '../utils'\nimport { createCell } from './cell'\n\nexport interface CoreRow<TData extends RowData> {\n  _getAllCellsByColumnId: () => Record<string, Cell<TData, unknown>>\n  _uniqueValuesCache: Record<string, unknown>\n  _valuesCache: Record<string, unknown>\n  /**\n   * The depth of the row (if nested or grouped) relative to the root row array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  depth: number\n  /**\n   * Returns all of the cells for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getallcells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getAllCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns the leaf rows for the row, not including any parent rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getLeafRows: () => Row<TData>[]\n  /**\n   * Returns the parent row for the row, if it exists.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRow: () => Row<TData> | undefined\n  /**\n   * Returns the parent rows for the row, all the way up to a root row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRows: () => Row<TData>[]\n  /**\n   * Returns a unique array of values from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getuniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getUniqueValues: <TValue>(columnId: string) => TValue[]\n  /**\n   * Returns the value from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getValue: <TValue>(columnId: string) => TValue\n  /**\n   * The resolved unique identifier for the row resolved via the `options.getRowId` option. Defaults to the row's index (or relative index if it is a subRow).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  id: string\n  /**\n   * The index of the row within its parent array (or the root data array).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  index: number\n  /**\n   * The original row object provided to the table. If the row is a grouped row, the original row object will be the first original in the group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#original)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  original: TData\n  /**\n   * An array of the original subRows as returned by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#originalsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  originalSubRows?: TData[]\n  /**\n   * If nested, this row's parent row id.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#parentid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  parentId?: string\n  /**\n   * Renders the value for the row in a given columnId the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  renderValue: <TValue>(columnId: string) => TValue\n  /**\n   * An array of subRows for the row as returned and created by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#subrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  subRows: Row<TData>[]\n}\n\nexport const createRow = <TData extends RowData>(\n  table: Table<TData>,\n  id: string,\n  original: TData,\n  rowIndex: number,\n  depth: number,\n  subRows?: Row<TData>[],\n  parentId?: string\n): Row<TData> => {\n  let row: CoreRow<TData> = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      row._valuesCache[columnId] = column.accessorFn(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._valuesCache[columnId] as any\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)]\n        return row._uniqueValuesCache[columnId]\n      }\n\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._uniqueValuesCache[columnId] as any\n    },\n    renderValue: columnId =>\n      row.getValue(columnId) ?? table.options.renderFallbackValue,\n    subRows: subRows ?? [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () =>\n      row.parentId ? table.getRow(row.parentId, true) : undefined,\n    getParentRows: () => {\n      let parentRows: Row<TData>[] = []\n      let currentRow = row\n      while (true) {\n        const parentRow = currentRow.getParentRow()\n        if (!parentRow) break\n        parentRows.push(parentRow)\n        currentRow = parentRow\n      }\n      return parentRows.reverse()\n    },\n    getAllCells: memo(\n      () => [table.getAllLeafColumns()],\n      leafColumns => {\n        return leafColumns.map(column => {\n          return createCell(table, row as Row<TData>, column, column.id)\n        })\n      },\n      getMemoOptions(table.options, 'debugRows', 'getAllCells')\n    ),\n\n    _getAllCellsByColumnId: memo(\n      () => [row.getAllCells()],\n      allCells => {\n        return allCells.reduce(\n          (acc, cell) => {\n            acc[cell.column.id] = cell\n            return acc\n          },\n          {} as Record<string, Cell<TData, unknown>>\n        )\n      },\n      getMemoOptions(table.options, 'debugRows', 'getAllCellsByColumnId')\n    ),\n  }\n\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i]\n    feature?.createRow?.(row as Row<TData>, table)\n  }\n\n  return row as Row<TData>\n}\n", "import { RowModel } from '..'\nimport { Column, RowData, Table, TableFeature } from '../types'\n\nexport interface FacetedColumn<TData extends RowData> {\n  _getFacetedMinMaxValues?: () => undefined | [number, number]\n  _getFacetedRowModel?: () => RowModel<TData>\n  _getFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * A function that **computes and returns** a min/max tuple derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedMinMaxValues` function to `options.getFacetedMinMaxValues`. A default implementation is provided via the exported `getFacetedMinMaxValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfacetedminmaxvalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model with all other column filters applied, excluding its own filter. Useful for displaying faceted result counts.\n   * > ⚠️ Requires that you pass a valid `getFacetedRowModel` function to `options.facetedRowModel`. A default implementation is provided via the exported `getFacetedRowModel` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedRowModel: () => RowModel<TData>\n  /**\n   * A function that **computes and returns** a `Map` of unique values and their occurrences derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedUniqueValues` function to `options.getFacetedUniqueValues`. A default implementation is provided via the exported `getFacetedUniqueValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedUniqueValues: () => Map<any, number>\n}\n\nexport interface FacetedOptions<TData extends RowData> {\n  getFacetedMinMaxValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => undefined | [number, number]\n  getFacetedRowModel?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => RowModel<TData>\n  getFacetedUniqueValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => Map<any, number>\n}\n\n//\n\nexport const ColumnFaceting: TableFeature = {\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column._getFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, column.id)\n    column.getFacetedRowModel = () => {\n      if (!column._getFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return column._getFacetedRowModel()\n    }\n    column._getFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, column.id)\n    column.getFacetedUniqueValues = () => {\n      if (!column._getFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return column._getFacetedUniqueValues()\n    }\n    column._getFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, column.id)\n    column.getFacetedMinMaxValues = () => {\n      if (!column._getFacetedMinMaxValues) {\n        return undefined\n      }\n\n      return column._getFacetedMinMaxValues()\n    }\n  },\n}\n", "import { FilterFn } from './features/ColumnFiltering'\n\nconst includesString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  const search = filterValue.toLowerCase()\n  return Boolean(\n    row\n      .getValue<string | null>(columnId)\n      ?.toString()\n      ?.toLowerCase()\n      ?.includes(search)\n  )\n}\n\nincludesString.autoRemove = (val: any) => testFalsey(val)\n\nconst includesStringSensitive: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return Boolean(\n    row.getValue<string | null>(columnId)?.toString()?.includes(filterValue)\n  )\n}\n\nincludesStringSensitive.autoRemove = (val: any) => testFalsey(val)\n\nconst equalsString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return (\n    row.getValue<string | null>(columnId)?.toString()?.toLowerCase() ===\n    filterValue?.toLowerCase()\n  )\n}\n\nequalsString.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludes: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue<unknown[]>(columnId)?.includes(filterValue)\n}\n\narrIncludes.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesAll: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return !filterValue.some(\n    val => !row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesAll.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesSome: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return filterValue.some(\n    val => row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesSome.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst equals: FilterFn<any> = (row, columnId: string, filterValue: unknown) => {\n  return row.getValue(columnId) === filterValue\n}\n\nequals.autoRemove = (val: any) => testFalsey(val)\n\nconst weakEquals: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue(columnId) == filterValue\n}\n\nweakEquals.autoRemove = (val: any) => testFalsey(val)\n\nconst inNumberRange: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: [number, number]\n) => {\n  let [min, max] = filterValue\n\n  const rowValue = row.getValue<number>(columnId)\n  return rowValue >= min && rowValue <= max\n}\n\ninNumberRange.resolveFilterValue = (val: [any, any]) => {\n  let [unsafeMin, unsafeMax] = val\n\n  let parsedMin =\n    typeof unsafeMin !== 'number' ? parseFloat(unsafeMin as string) : unsafeMin\n  let parsedMax =\n    typeof unsafeMax !== 'number' ? parseFloat(unsafeMax as string) : unsafeMax\n\n  let min =\n    unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax\n\n  if (min > max) {\n    const temp = min\n    min = max\n    max = temp\n  }\n\n  return [min, max] as const\n}\n\ninNumberRange.autoRemove = (val: any) =>\n  testFalsey(val) || (testFalsey(val[0]) && testFalsey(val[1]))\n\n// Export\n\nexport const filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange,\n}\n\nexport type BuiltInFilterFn = keyof typeof filterFns\n\n// Utils\n\nfunction testFalsey(val: any) {\n  return val === undefined || val === null || val === ''\n}\n", "import { RowModel } from '..'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  FilterFns,\n  FilterMeta,\n  OnChangeFn,\n  Row,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\nimport { functionalUpdate, isFunction, makeStateUpdater } from '../utils'\n\nexport interface ColumnFiltersTableState {\n  columnFilters: ColumnFiltersState\n}\n\nexport type ColumnFiltersState = ColumnFilter[]\n\nexport interface ColumnFilter {\n  id: string\n  value: unknown\n}\n\nexport interface ResolvedColumnFilter<TData extends RowData> {\n  filterFn: FilterFn<TData>\n  id: string\n  resolvedValue: unknown\n}\n\nexport interface FilterFn<TData extends RowData> {\n  (\n    row: Row<TData>,\n    columnId: string,\n    filterValue: any,\n    addMeta: (meta: FilterMeta) => void\n  ): boolean\n  autoRemove?: ColumnFilterAutoRemoveTestFn<TData>\n  resolveFilterValue?: TransformFilterValueFn<TData>\n}\n\nexport type TransformFilterValueFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => unknown\n\nexport type ColumnFilterAutoRemoveTestFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => boolean\n\nexport type CustomFilterFns<TData extends RowData> = Record<\n  string,\n  FilterFn<TData>\n>\n\nexport type FilterFnOption<TData extends RowData> =\n  | 'auto'\n  | BuiltInFilterFn\n  | keyof FilterFns\n  | FilterFn<TData>\n\nexport interface ColumnFiltersColumnDef<TData extends RowData> {\n  /**\n   * Enables/disables the **column** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablecolumnfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableColumnFilter?: boolean\n  /**\n   * The filter function to use with this column. Can be the name of a built-in filter function or a custom filter function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#filterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  filterFn?: FilterFnOption<TData>\n}\n\nexport interface ColumnFiltersColumn<TData extends RowData> {\n  /**\n   * Returns an automatically calculated filter function for the column based off of the columns first known value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be **column** filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getcanfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getCanFilter: () => boolean\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the columnId specified.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the index (including `-1`) of the column filter in the table's `state.columnFilters` array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilterindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterIndex: () => number\n  /**\n   * Returns the current filter value for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterValue: () => unknown\n  /**\n   * Returns whether or not the column is currently filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getisfiltered)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getIsFiltered: () => boolean\n  /**\n   * A function that sets the current filter value for the column. You can pass it a value or an updater function for immutability-safe operations on existing values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setFilterValue: (updater: Updater<any>) => void\n}\n\nexport interface ColumnFiltersRow<TData extends RowData> {\n  /**\n   * The column filters map for the row. This object tracks whether a row is passing/failing specific filters by their column ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#columnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  columnFilters: Record<string, boolean>\n  /**\n   * The column filters meta map for the row. This object tracks any filter meta for a row as optionally provided during the filtering process.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#columnfiltersmeta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  columnFiltersMeta: Record<string, FilterMeta>\n}\n\ninterface ColumnFiltersOptionsBase<TData extends RowData> {\n  /**\n   * Enables/disables **column** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablecolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableColumnFilters?: boolean\n  /**\n   * Enables/disables all filtering for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablefilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableFilters?: boolean\n  /**\n   * By default, filtering is done from parent rows down (so if a parent row is filtered out, all of its children will be filtered out as well). Setting this option to `true` will cause filtering to be done from leaf rows up (which means parent rows will be included so long as one of their child or grand-child rows is also included).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#filterfromleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  filterFromLeafRows?: boolean\n  /**\n   * If provided, this function is called **once** per table and should return a **new function** which will calculate and return the row model for the table when it's filtered.\n   * - For server-side filtering, this function is unnecessary and can be ignored since the server should already return the filtered row model.\n   * - For client-side filtering, this function is required. A default implementation is provided via any table adapter's `{ getFilteredRowModel }` export.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilteredRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Disables the `getFilteredRowModel` from being used to filter data. This may be useful if your table needs to dynamically support both client-side and server-side filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#manualfiltering)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  manualFiltering?: boolean\n  /**\n   * By default, filtering is done for all rows (max depth of 100), no matter if they are root level parent rows or the child leaf rows of a parent row. Setting this option to `0` will cause filtering to only be applied to the root level parent rows, with all sub-rows remaining unfiltered. Similarly, setting this option to `1` will cause filtering to only be applied to child leaf rows 1 level deep, and so on.\n\n   * This is useful for situations where you want a row's entire child hierarchy to be visible regardless of the applied filter.\n    * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#maxleafrowfilterdepth)\n    * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  maxLeafRowFilterDepth?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnFilters` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#oncolumnfilterschange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  onColumnFiltersChange?: OnChangeFn<ColumnFiltersState>\n}\n\ntype ResolvedFilterFns = keyof FilterFns extends never\n  ? {\n      filterFns?: Record<string, FilterFn<any>>\n    }\n  : {\n      filterFns: Record<keyof FilterFns, FilterFn<any>>\n    }\n\nexport interface ColumnFiltersOptions<TData extends RowData>\n  extends ColumnFiltersOptionsBase<TData>,\n    ResolvedFilterFns {}\n\nexport interface ColumnFiltersInstance<TData extends RowData> {\n  _getFilteredRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilteredRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getprefilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getPreFilteredRowModel: () => RowModel<TData>\n  /**\n   * Resets the **columnFilters** state to `initialState.columnFilters`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#resetcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  resetColumnFilters: (defaultState?: boolean) => void\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnFilters` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setColumnFilters: (updater: Updater<ColumnFiltersState>) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const ColumnFiltering: TableFeature = {\n  getDefaultColumnDef: <\n    TData extends RowData,\n  >(): ColumnFiltersColumnDef<TData> => {\n    return {\n      filterFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): ColumnFiltersTableState => {\n    return {\n      columnFilters: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnFiltersOptions<TData> => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100,\n    } as ColumnFiltersOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoFilterFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return filterFns.includesString\n      }\n\n      if (typeof value === 'number') {\n        return filterFns.inNumberRange\n      }\n\n      if (typeof value === 'boolean') {\n        return filterFns.equals\n      }\n\n      if (value !== null && typeof value === 'object') {\n        return filterFns.equals\n      }\n\n      if (Array.isArray(value)) {\n        return filterFns.arrIncludes\n      }\n\n      return filterFns.weakEquals\n    }\n    column.getFilterFn = () => {\n      return isFunction(column.columnDef.filterFn)\n        ? column.columnDef.filterFn\n        : column.columnDef.filterFn === 'auto'\n          ? column.getAutoFilterFn()\n          : // @ts-ignore\n            table.options.filterFns?.[column.columnDef.filterFn as string] ??\n            filterFns[column.columnDef.filterFn as BuiltInFilterFn]\n    }\n    column.getCanFilter = () => {\n      return (\n        (column.columnDef.enableColumnFilter ?? true) &&\n        (table.options.enableColumnFilters ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsFiltered = () => column.getFilterIndex() > -1\n\n    column.getFilterValue = () =>\n      table.getState().columnFilters?.find(d => d.id === column.id)?.value\n\n    column.getFilterIndex = () =>\n      table.getState().columnFilters?.findIndex(d => d.id === column.id) ?? -1\n\n    column.setFilterValue = value => {\n      table.setColumnFilters(old => {\n        const filterFn = column.getFilterFn()\n        const previousFilter = old?.find(d => d.id === column.id)\n\n        const newFilter = functionalUpdate(\n          value,\n          previousFilter ? previousFilter.value : undefined\n        )\n\n        //\n        if (\n          shouldAutoRemoveFilter(filterFn as FilterFn<TData>, newFilter, column)\n        ) {\n          return old?.filter(d => d.id !== column.id) ?? []\n        }\n\n        const newFilterObj = { id: column.id, value: newFilter }\n\n        if (previousFilter) {\n          return (\n            old?.map(d => {\n              if (d.id === column.id) {\n                return newFilterObj\n              }\n              return d\n            }) ?? []\n          )\n        }\n\n        if (old?.length) {\n          return [...old, newFilterObj]\n        }\n\n        return [newFilterObj]\n      })\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    _table: Table<TData>\n  ): void => {\n    row.columnFilters = {}\n    row.columnFiltersMeta = {}\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnFilters = (updater: Updater<ColumnFiltersState>) => {\n      const leafColumns = table.getAllLeafColumns()\n\n      const updateFn = (old: ColumnFiltersState) => {\n        return functionalUpdate(updater, old)?.filter(filter => {\n          const column = leafColumns.find(d => d.id === filter.id)\n\n          if (column) {\n            const filterFn = column.getFilterFn()\n\n            if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n              return false\n            }\n          }\n\n          return true\n        })\n      }\n\n      table.options.onColumnFiltersChange?.(updateFn)\n    }\n\n    table.resetColumnFilters = defaultState => {\n      table.setColumnFilters(\n        defaultState ? [] : table.initialState?.columnFilters ?? []\n      )\n    }\n\n    table.getPreFilteredRowModel = () => table.getCoreRowModel()\n    table.getFilteredRowModel = () => {\n      if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n        table._getFilteredRowModel = table.options.getFilteredRowModel(table)\n      }\n\n      if (table.options.manualFiltering || !table._getFilteredRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getFilteredRowModel()\n    }\n  },\n}\n\nexport function shouldAutoRemoveFilter<TData extends RowData>(\n  filterFn?: FilterFn<TData>,\n  value?: any,\n  column?: Column<TData, unknown>\n) {\n  return (\n    (filterFn && filterFn.autoRemove\n      ? filterFn.autoRemove(value, column)\n      : false) ||\n    typeof value === 'undefined' ||\n    (typeof value === 'string' && !value)\n  )\n}\n", "import { AggregationFn } from './features/ColumnGrouping'\nimport { isNumberArray } from './utils'\n\nconst sum: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId)\n    return sum + (typeof nextValue === 'number' ? nextValue : 0)\n  }, 0)\n}\n\nconst min: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n\n    if (\n      value != null &&\n      (min! > value || (min === undefined && value >= value))\n    ) {\n      min = value\n    }\n  })\n\n  return min\n}\n\nconst max: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (\n      value != null &&\n      (max! < value || (max === undefined && value >= value))\n    ) {\n      max = value\n    }\n  })\n\n  return max\n}\n\nconst extent: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value\n      } else {\n        if (min > value) min = value\n        if (max! < value) max = value\n      }\n    }\n  })\n\n  return [min, max]\n}\n\nconst mean: AggregationFn<any> = (columnId, leafRows) => {\n  let count = 0\n  let sum = 0\n\n  leafRows.forEach(row => {\n    let value = row.getValue<number>(columnId)\n    if (value != null && (value = +value) >= value) {\n      ++count, (sum += value)\n    }\n  })\n\n  if (count) return sum / count\n\n  return\n}\n\nconst median: AggregationFn<any> = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return\n  }\n\n  const values = leafRows.map(row => row.getValue(columnId))\n  if (!isNumberArray(values)) {\n    return\n  }\n  if (values.length === 1) {\n    return values[0]\n  }\n\n  const mid = Math.floor(values.length / 2)\n  const nums = values.sort((a, b) => a - b)\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1]! + nums[mid]!) / 2\n}\n\nconst unique: AggregationFn<any> = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values())\n}\n\nconst uniqueCount: AggregationFn<any> = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size\n}\n\nconst count: AggregationFn<any> = (_columnId, leafRows) => {\n  return leafRows.length\n}\n\nexport const aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count,\n}\n\nexport type BuiltInAggregationFn = keyof typeof aggregationFns\n", "import { RowModel } from '..'\nimport { BuiltInAggregationFn, aggregationFns } from '../aggregationFns'\nimport {\n  AggregationFns,\n  Cell,\n  Column,\n  ColumnDefTemplate,\n  OnChangeFn,\n  Row,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type GroupingState = string[]\n\nexport interface GroupingTableState {\n  grouping: GroupingState\n}\n\nexport type AggregationFn<TData extends RowData> = (\n  columnId: string,\n  leafRows: Row<TData>[],\n  childRows: Row<TData>[]\n) => any\n\nexport type CustomAggregationFns = Record<string, AggregationFn<any>>\n\nexport type AggregationFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof AggregationFns\n  | BuiltInAggregationFn\n  | AggregationFn<TData>\n\nexport interface GroupingColumnDef<TData extends RowData, TValue> {\n  /**\n   * The cell to display each row for the column if the cell is an aggregate. If a function is passed, it will be passed a props object with the context of the cell and should return the property type for your adapter (the exact type depends on the adapter being used).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregatedcell)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregatedCell?: ColumnDefTemplate<\n    ReturnType<Cell<TData, TValue>['getContext']>\n  >\n  /**\n   * The resolved aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregationFn?: AggregationFnOption<TData>\n  /**\n   * Enables/disables grouping for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Specify a value to be used for grouping rows on this column. If this option is not specified, the value derived from `accessorKey` / `accessorFn` will be used instead.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue?: (row: TData) => any\n}\n\nexport interface GroupingColumn<TData extends RowData> {\n  /**\n   * Returns the aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns the automatically inferred aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getautoaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAutoAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getcangroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getCanGroup: () => boolean\n  /**\n   * Returns the index of the column in the grouping state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedIndex: () => number\n  /**\n   * Returns whether or not the column is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns a function that toggles the grouping state of the column. This is useful for passing to the `onClick` prop of a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#gettogglegroupinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getToggleGroupingHandler: () => () => void\n  /**\n   * Toggles the grouping state of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#togglegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  toggleGrouping: () => void\n}\n\nexport interface GroupingRow {\n  _groupingValuesCache: Record<string, any>\n  /**\n   * Returns the grouping value for any row and column (including leaf rows).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue: (columnId: string) => unknown\n  /**\n   * Returns whether or not the row is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * If this row is grouped, this is the id of the column that this row is grouped by.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingcolumnid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingColumnId?: string\n  /**\n   * If this row is grouped, this is the unique/shared value for the `groupingColumnId` for all of the rows in this group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingValue?: unknown\n}\n\nexport interface GroupingCell {\n  /**\n   * Returns whether or not the cell is currently aggregated.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisaggregated)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsAggregated: () => boolean\n  /**\n   * Returns whether or not the cell is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns whether or not the cell is currently a placeholder cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsPlaceholder: () => boolean\n}\n\nexport interface ColumnDefaultOptions {\n  enableGrouping: boolean\n  onGroupingChange: OnChangeFn<GroupingState>\n}\n\ninterface GroupingOptionsBase {\n  /**\n   * Enables/disables grouping for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Returns the row model after grouping has taken place, but no further.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Grouping columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupedcolumnmode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupedColumnMode?: false | 'reorder' | 'remove'\n  /**\n   * Enables manual grouping. If this option is set to `true`, the table will not automatically group rows using `getGroupedRowModel()` and instead will expect you to manually group the rows before passing them to the table. This is useful if you are doing server-side grouping and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#manualgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  manualGrouping?: boolean\n  /**\n   * If this function is provided, it will be called when the grouping state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.grouping` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#ongroupingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  onGroupingChange?: OnChangeFn<GroupingState>\n}\n\ntype ResolvedAggregationFns = keyof AggregationFns extends never\n  ? {\n      aggregationFns?: Record<string, AggregationFn<any>>\n    }\n  : {\n      aggregationFns: Record<keyof AggregationFns, AggregationFn<any>>\n    }\n\nexport interface GroupingOptions\n  extends GroupingOptionsBase,\n    ResolvedAggregationFns {}\n\nexport type GroupingColumnMode = false | 'reorder' | 'remove'\n\nexport interface GroupingInstance<TData extends RowData> {\n  _getGroupedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getpregroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getPreGroupedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **grouping** state to `initialState.grouping`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#resetgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  resetGrouping: (defaultState?: boolean) => void\n  /**\n   * Updates the grouping state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#setgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  setGrouping: (updater: Updater<GroupingState>) => void\n}\n\n//\n\nexport const ColumnGrouping: TableFeature = {\n  getDefaultColumnDef: <TData extends RowData>(): GroupingColumnDef<\n    TData,\n    unknown\n  > => {\n    return {\n      aggregatedCell: props => (props.getValue() as any)?.toString?.() ?? null,\n      aggregationFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): GroupingTableState => {\n    return {\n      grouping: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GroupingOptions => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder',\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleGrouping = () => {\n      table.setGrouping(old => {\n        // Find any existing grouping for this column\n        if (old?.includes(column.id)) {\n          return old.filter(d => d !== column.id)\n        }\n\n        return [...(old ?? []), column.id]\n      })\n    }\n\n    column.getCanGroup = () => {\n      return (\n        (column.columnDef.enableGrouping ?? true) &&\n        (table.options.enableGrouping ?? true) &&\n        (!!column.accessorFn || !!column.columnDef.getGroupingValue)\n      )\n    }\n\n    column.getIsGrouped = () => {\n      return table.getState().grouping?.includes(column.id)\n    }\n\n    column.getGroupedIndex = () => table.getState().grouping?.indexOf(column.id)\n\n    column.getToggleGroupingHandler = () => {\n      const canGroup = column.getCanGroup()\n\n      return () => {\n        if (!canGroup) return\n        column.toggleGrouping()\n      }\n    }\n    column.getAutoAggregationFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'number') {\n        return aggregationFns.sum\n      }\n\n      if (Object.prototype.toString.call(value) === '[object Date]') {\n        return aggregationFns.extent\n      }\n    }\n    column.getAggregationFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.aggregationFn)\n        ? column.columnDef.aggregationFn\n        : column.columnDef.aggregationFn === 'auto'\n          ? column.getAutoAggregationFn()\n          : table.options.aggregationFns?.[\n              column.columnDef.aggregationFn as string\n            ] ??\n            aggregationFns[\n              column.columnDef.aggregationFn as BuiltInAggregationFn\n            ]\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setGrouping = updater => table.options.onGroupingChange?.(updater)\n\n    table.resetGrouping = defaultState => {\n      table.setGrouping(defaultState ? [] : table.initialState?.grouping ?? [])\n    }\n\n    table.getPreGroupedRowModel = () => table.getFilteredRowModel()\n    table.getGroupedRowModel = () => {\n      if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n        table._getGroupedRowModel = table.options.getGroupedRowModel(table)\n      }\n\n      if (table.options.manualGrouping || !table._getGroupedRowModel) {\n        return table.getPreGroupedRowModel()\n      }\n\n      return table._getGroupedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getIsGrouped = () => !!row.groupingColumnId\n    row.getGroupingValue = columnId => {\n      if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n        return row._groupingValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.columnDef.getGroupingValue) {\n        return row.getValue(columnId)\n      }\n\n      row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(\n        row.original\n      )\n\n      return row._groupingValuesCache[columnId]\n    }\n    row._groupingValuesCache = {}\n  },\n\n  createCell: <TData extends RowData, TValue>(\n    cell: Cell<TData, TValue>,\n    column: Column<TData, TValue>,\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    const getRenderValue = () =>\n      cell.getValue() ?? table.options.renderFallbackValue\n\n    cell.getIsGrouped = () =>\n      column.getIsGrouped() && column.id === row.groupingColumnId\n    cell.getIsPlaceholder = () => !cell.getIsGrouped() && column.getIsGrouped()\n    cell.getIsAggregated = () =>\n      !cell.getIsGrouped() && !cell.getIsPlaceholder() && !!row.subRows?.length\n  },\n}\n\nexport function orderColumns<TData extends RowData>(\n  leafColumns: Column<TData, unknown>[],\n  grouping: string[],\n  groupedColumnMode?: GroupingColumnMode\n) {\n  if (!grouping?.length || !groupedColumnMode) {\n    return leafColumns\n  }\n\n  const nonGroupingColumns = leafColumns.filter(\n    col => !grouping.includes(col.id)\n  )\n\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns\n  }\n\n  const groupingColumns = grouping\n    .map(g => leafColumns.find(col => col.id === g)!)\n    .filter(Boolean)\n\n  return [...groupingColumns, ...nonGroupingColumns]\n}\n", "import { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nimport {\n  Column,\n  OnChangeFn,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\n\nimport { orderColumns } from './ColumnGrouping'\nimport { ColumnPinningPosition, _getVisibleLeafColumns } from '..'\n\nexport interface ColumnOrderTableState {\n  columnOrder: ColumnOrderState\n}\n\nexport type ColumnOrderState = string[]\n\nexport interface ColumnOrderOptions {\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnOrder` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#oncolumnorderchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  onColumnOrderChange?: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderColumn {\n  /**\n   * Returns the index of the column in the order of the visible columns. Optionally pass a `position` parameter to get the index of the column in a sub-section of the table\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIndex: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Returns `true` if the column is the first column in the order of the visible columns. Optionally pass a `position` parameter to check if the column is the first in a sub-section of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getisfirstcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIsFirstColumn: (position?: ColumnPinningPosition | 'center') => boolean\n  /**\n   * Returns `true` if the column is the last column in the order of the visible columns. Optionally pass a `position` parameter to check if the column is the last in a sub-section of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getislastcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIsLastColumn: (position?: ColumnPinningPosition | 'center') => boolean\n}\n\nexport interface ColumnOrderDefaultOptions {\n  onColumnOrderChange: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderInstance<TData extends RowData> {\n  _getOrderColumnsFn: () => (\n    columns: Column<TData, unknown>[]\n  ) => Column<TData, unknown>[]\n  /**\n   * Resets the **columnOrder** state to `initialState.columnOrder`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#resetcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  resetColumnOrder: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnOrder` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#setcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  setColumnOrder: (updater: Updater<ColumnOrderState>) => void\n}\n\n//\n\nexport const ColumnOrdering: TableFeature = {\n  getInitialState: (state): ColumnOrderTableState => {\n    return {\n      columnOrder: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnOrderDefaultOptions => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table),\n    }\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getIndex = memo(\n      position => [_getVisibleLeafColumns(table, position)],\n      columns => columns.findIndex(d => d.id === column.id),\n      getMemoOptions(table.options, 'debugColumns', 'getIndex')\n    )\n    column.getIsFirstColumn = position => {\n      const columns = _getVisibleLeafColumns(table, position)\n      return columns[0]?.id === column.id\n    }\n    column.getIsLastColumn = position => {\n      const columns = _getVisibleLeafColumns(table, position)\n      return columns[columns.length - 1]?.id === column.id\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnOrder = updater =>\n      table.options.onColumnOrderChange?.(updater)\n    table.resetColumnOrder = defaultState => {\n      table.setColumnOrder(\n        defaultState ? [] : table.initialState.columnOrder ?? []\n      )\n    }\n    table._getOrderColumnsFn = memo(\n      () => [\n        table.getState().columnOrder,\n        table.getState().grouping,\n        table.options.groupedColumnMode,\n      ],\n      (columnOrder, grouping, groupedColumnMode) =>\n        (columns: Column<TData, unknown>[]) => {\n          // Sort grouped columns to the start of the column list\n          // before the headers are built\n          let orderedColumns: Column<TData, unknown>[] = []\n\n          // If there is no order, return the normal columns\n          if (!columnOrder?.length) {\n            orderedColumns = columns\n          } else {\n            const columnOrderCopy = [...columnOrder]\n\n            // If there is an order, make a copy of the columns\n            const columnsCopy = [...columns]\n\n            // And make a new ordered array of the columns\n\n            // Loop over the columns and place them in order into the new array\n            while (columnsCopy.length && columnOrderCopy.length) {\n              const targetColumnId = columnOrderCopy.shift()\n              const foundIndex = columnsCopy.findIndex(\n                d => d.id === targetColumnId\n              )\n              if (foundIndex > -1) {\n                orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]!)\n              }\n            }\n\n            // If there are any columns left, add them to the end\n            orderedColumns = [...orderedColumns, ...columnsCopy]\n          }\n\n          return orderColumns(orderedColumns, grouping, groupedColumnMode)\n        },\n      getMemoOptions(table.options, 'debugTable', '_getOrderColumnsFn')\n    )\n  },\n}\n", "import {\n  OnChangeFn,\n  Updater,\n  Table,\n  Column,\n  Row,\n  Cell,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type ColumnPinningPosition = false | 'left' | 'right'\n\nexport interface ColumnPinningState {\n  left?: string[]\n  right?: string[]\n}\n\nexport interface ColumnPinningTableState {\n  columnPinning: ColumnPinningState\n}\n\nexport interface ColumnPinningOptions {\n  /**\n   * Enables/disables column pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablecolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enableColumnPinning?: boolean\n  /**\n   * @deprecated Use `enableColumnPinning` or `enableRowPinning` instead.\n   * Enables/disables all pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablepinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enablePinning?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnPinning` changes. This overrides the default internal state management, so you will also need to supply `state.columnPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#oncolumnpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/oncolumnpinningchange)\n   */\n  onColumnPinningChange?: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningDefaultOptions {\n  onColumnPinningChange: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningColumnDef {\n  /**\n   * Enables/disables column pinning for this column. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablepinning-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enablePinning?: boolean\n}\n\nexport interface ColumnPinningColumn {\n  /**\n   * Returns whether or not the column can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcanpin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the column. (`'left'`, `'right'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getispinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getIsPinned: () => ColumnPinningPosition\n  /**\n   * Returns the numeric pinned index of the column within a pinned column group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getpinnedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a column to the `'left'` or `'right'`, or unpins the column to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#pin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  pin: (position: ColumnPinningPosition) => void\n}\n\nexport interface ColumnPinningRow<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcentervisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCenterVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all left pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getleftvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getLeftVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getrightvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getRightVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface ColumnPinningInstance<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcenterleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCenterLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether or not any columns are pinned. Optionally specify to only check for pinned columns in either the `left` or `right` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getissomecolumnspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getIsSomeColumnsPinned: (position?: ColumnPinningPosition) => boolean\n  /**\n   * Returns all left pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getleftleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getLeftLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getrightleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getRightLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the **columnPinning** state to `initialState.columnPinning`, or `true` can be passed to force a default blank state reset to `{ left: [], right: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#resetcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  resetColumnPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#setcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  setColumnPinning: (updater: Updater<ColumnPinningState>) => void\n}\n\n//\n\nconst getDefaultColumnPinningState = (): ColumnPinningState => ({\n  left: [],\n  right: [],\n})\n\nexport const ColumnPinning: TableFeature = {\n  getInitialState: (state): ColumnPinningTableState => {\n    return {\n      columnPinning: getDefaultColumnPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnPinningDefaultOptions => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.pin = position => {\n      const columnIds = column\n        .getLeafColumns()\n        .map(d => d.id)\n        .filter(Boolean) as string[]\n\n      table.setColumnPinning(old => {\n        if (position === 'right') {\n          return {\n            left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n            right: [\n              ...(old?.right ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n          }\n        }\n\n        if (position === 'left') {\n          return {\n            left: [\n              ...(old?.left ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n            right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n          }\n        }\n\n        return {\n          left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n          right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n        }\n      })\n    }\n\n    column.getCanPin = () => {\n      const leafColumns = column.getLeafColumns()\n\n      return leafColumns.some(\n        d =>\n          (d.columnDef.enablePinning ?? true) &&\n          (table.options.enableColumnPinning ??\n            table.options.enablePinning ??\n            true)\n      )\n    }\n\n    column.getIsPinned = () => {\n      const leafColumnIds = column.getLeafColumns().map(d => d.id)\n\n      const { left, right } = table.getState().columnPinning\n\n      const isLeft = leafColumnIds.some(d => left?.includes(d))\n      const isRight = leafColumnIds.some(d => right?.includes(d))\n\n      return isLeft ? 'left' : isRight ? 'right' : false\n    }\n\n    column.getPinnedIndex = () => {\n      const position = column.getIsPinned()\n\n      return position\n        ? table.getState().columnPinning?.[position]?.indexOf(column.id) ?? -1\n        : 0\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getCenterVisibleCells = memo(\n      () => [\n        row._getAllVisibleCells(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allCells, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allCells.filter(d => !leftAndRight.includes(d.column.id))\n      },\n      getMemoOptions(table.options, 'debugRows', 'getCenterVisibleCells')\n    )\n    row.getLeftVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.left],\n      (allCells, left) => {\n        const cells = (left ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'left' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      getMemoOptions(table.options, 'debugRows', 'getLeftVisibleCells')\n    )\n    row.getRightVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.right],\n      (allCells, right) => {\n        const cells = (right ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'right' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      getMemoOptions(table.options, 'debugRows', 'getRightVisibleCells')\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnPinning = updater =>\n      table.options.onColumnPinningChange?.(updater)\n\n    table.resetColumnPinning = defaultState =>\n      table.setColumnPinning(\n        defaultState\n          ? getDefaultColumnPinningState()\n          : table.initialState?.columnPinning ?? getDefaultColumnPinningState()\n      )\n\n    table.getIsSomeColumnsPinned = position => {\n      const pinningState = table.getState().columnPinning\n\n      if (!position) {\n        return Boolean(pinningState.left?.length || pinningState.right?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table.getLeftLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.left],\n      (allColumns, left) => {\n        return (left ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getLeftLeafColumns')\n    )\n\n    table.getRightLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.right],\n      (allColumns, right) => {\n        return (right ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getRightLeafColumns')\n    )\n\n    table.getCenterLeafColumns = memo(\n      () => [\n        table.getAllLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allColumns.filter(d => !leftAndRight.includes(d.id))\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getCenterLeafColumns')\n    )\n  },\n}\n", "import { _getVisibleLeafColumns } from '..'\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>um<PERSON>,\n  Header,\n  OnChangeFn,\n  Table,\n  Updater,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\nimport { ColumnPinningPosition } from './ColumnPinning'\n\n//\n\nexport interface ColumnSizingTableState {\n  columnSizing: ColumnSizingState\n  columnSizingInfo: ColumnSizingInfoState\n}\n\nexport type ColumnSizingState = Record<string, number>\n\nexport interface ColumnSizingInfoState {\n  columnSizingStart: [string, number][]\n  deltaOffset: null | number\n  deltaPercentage: null | number\n  isResizingColumn: false | string\n  startOffset: null | number\n  startSize: null | number\n}\n\nexport type ColumnResizeMode = 'onChange' | 'onEnd'\n\nexport type ColumnResizeDirection = 'ltr' | 'rtl'\n\nexport interface ColumnSizingOptions {\n  /**\n   * Determines when the columnSizing state is updated. `onChange` updates the state when the user is dragging the resize handle. `onEnd` updates the state when the user releases the resize handle.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnresizemode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeMode?: ColumnResizeMode\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enablecolumnresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableColumnResizing?: boolean\n  /**\n   * Enables or disables right-to-left support for resizing the column. defaults to 'ltr'.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnResizeDirection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeDirection?: ColumnResizeDirection\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizing` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizing` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingChange?: OnChangeFn<ColumnSizingState>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizingInfo` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizingInfo` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizinginfochange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingInfoChange?: OnChangeFn<ColumnSizingInfoState>\n}\n\nexport type ColumnSizingDefaultOptions = Pick<\n  ColumnSizingOptions,\n  | 'columnResizeMode'\n  | 'onColumnSizingChange'\n  | 'onColumnSizingInfoChange'\n  | 'columnResizeDirection'\n>\n\nexport interface ColumnSizingInstance {\n  /**\n   * If pinning, returns the total size of the center portion of the table by calculating the sum of the sizes of all unpinned/center leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcentertotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCenterTotalSize: () => number\n  /**\n   * Returns the total size of the left portion of the table by calculating the sum of the sizes of all left leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getlefttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getLeftTotalSize: () => number\n  /**\n   * Returns the total size of the right portion of the table by calculating the sum of the sizes of all right leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getrighttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getRightTotalSize: () => number\n  /**\n   * Returns the total size of the table by calculating the sum of the sizes of all leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#gettotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getTotalSize: () => number\n  /**\n   * Resets column sizing to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetColumnSizing: (defaultState?: boolean) => void\n  /**\n   * Resets column sizing info to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetheadersizeinfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetHeaderSizeInfo: (defaultState?: boolean) => void\n  /**\n   * Sets the column sizing state using an updater function or a value. This will trigger the underlying `onColumnSizingChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizing: (updater: Updater<ColumnSizingState>) => void\n  /**\n   * Sets the column sizing info state using an updater function or a value. This will trigger the underlying `onColumnSizingInfoChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizinginfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizingInfo: (updater: Updater<ColumnSizingInfoState>) => void\n}\n\nexport interface ColumnSizingColumnDef {\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enableresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableResizing?: boolean\n  /**\n   * The maximum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#maxsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  maxSize?: number\n  /**\n   * The minimum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#minsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  minSize?: number\n  /**\n   * The desired size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#size)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  size?: number\n}\n\nexport interface ColumnSizingColumn {\n  /**\n   * Returns `true` if the column can be resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcanresize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCanResize: () => boolean\n  /**\n   * Returns `true` if the column is currently being resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getisresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getIsResizing: () => boolean\n  /**\n   * Returns the current size of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding (left) headers in relation to the current column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all succeeding (right) headers in relation to the current column.\n   */\n  getAfter: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Resets the column to its initial size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetSize: () => void\n}\n\nexport interface ColumnSizingHeader {\n  /**\n   * Returns an event handler function that can be used to resize the header. It can be used as an:\n   * - `onMouseDown` handler\n   * - `onTouchStart` handler\n   *\n   * The dragging and release events are automatically handled for you.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getresizehandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getResizeHandler: (context?: Document) => (event: unknown) => void\n  /**\n   * Returns the current size of the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition) => number\n}\n\n//\n\nexport const defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER,\n}\n\nconst getDefaultColumnSizingInfoState = (): ColumnSizingInfoState => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: [],\n})\n\nexport const ColumnSizing: TableFeature = {\n  getDefaultColumnDef: (): ColumnSizingColumnDef => {\n    return defaultColumnSizing\n  },\n  getInitialState: (state): ColumnSizingTableState => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnSizingDefaultOptions => {\n    return {\n      columnResizeMode: 'onEnd',\n      columnResizeDirection: 'ltr',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getSize = () => {\n      const columnSize = table.getState().columnSizing[column.id]\n\n      return Math.min(\n        Math.max(\n          column.columnDef.minSize ?? defaultColumnSizing.minSize,\n          columnSize ?? column.columnDef.size ?? defaultColumnSizing.size\n        ),\n        column.columnDef.maxSize ?? defaultColumnSizing.maxSize\n      )\n    }\n\n    column.getStart = memo(\n      position => [\n        position,\n        _getVisibleLeafColumns(table, position),\n        table.getState().columnSizing,\n      ],\n      (position, columns) =>\n        columns\n          .slice(0, column.getIndex(position))\n          .reduce((sum, column) => sum + column.getSize(), 0),\n      getMemoOptions(table.options, 'debugColumns', 'getStart')\n    )\n\n    column.getAfter = memo(\n      position => [\n        position,\n        _getVisibleLeafColumns(table, position),\n        table.getState().columnSizing,\n      ],\n      (position, columns) =>\n        columns\n          .slice(column.getIndex(position) + 1)\n          .reduce((sum, column) => sum + column.getSize(), 0),\n      getMemoOptions(table.options, 'debugColumns', 'getAfter')\n    )\n\n    column.resetSize = () => {\n      table.setColumnSizing(({ [column.id]: _, ...rest }) => {\n        return rest\n      })\n    }\n    column.getCanResize = () => {\n      return (\n        (column.columnDef.enableResizing ?? true) &&\n        (table.options.enableColumnResizing ?? true)\n      )\n    }\n    column.getIsResizing = () => {\n      return table.getState().columnSizingInfo.isResizingColumn === column.id\n    }\n  },\n\n  createHeader: <TData extends RowData, TValue>(\n    header: Header<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    header.getSize = () => {\n      let sum = 0\n\n      const recurse = (header: Header<TData, TValue>) => {\n        if (header.subHeaders.length) {\n          header.subHeaders.forEach(recurse)\n        } else {\n          sum += header.column.getSize() ?? 0\n        }\n      }\n\n      recurse(header)\n\n      return sum\n    }\n    header.getStart = () => {\n      if (header.index > 0) {\n        const prevSiblingHeader = header.headerGroup.headers[header.index - 1]!\n        return prevSiblingHeader.getStart() + prevSiblingHeader.getSize()\n      }\n\n      return 0\n    }\n    header.getResizeHandler = _contextDocument => {\n      const column = table.getColumn(header.column.id)\n      const canResize = column?.getCanResize()\n\n      return (e: unknown) => {\n        if (!column || !canResize) {\n          return\n        }\n\n        ;(e as any).persist?.()\n\n        if (isTouchStartEvent(e)) {\n          // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n          if (e.touches && e.touches.length > 1) {\n            return\n          }\n        }\n\n        const startSize = header.getSize()\n\n        const columnSizingStart: [string, number][] = header\n          ? header.getLeafHeaders().map(d => [d.column.id, d.column.getSize()])\n          : [[column.id, column.getSize()]]\n\n        const clientX = isTouchStartEvent(e)\n          ? Math.round(e.touches[0]!.clientX)\n          : (e as MouseEvent).clientX\n\n        const newColumnSizing: ColumnSizingState = {}\n\n        const updateOffset = (\n          eventType: 'move' | 'end',\n          clientXPos?: number\n        ) => {\n          if (typeof clientXPos !== 'number') {\n            return\n          }\n\n          table.setColumnSizingInfo(old => {\n            const deltaDirection =\n              table.options.columnResizeDirection === 'rtl' ? -1 : 1\n            const deltaOffset =\n              (clientXPos - (old?.startOffset ?? 0)) * deltaDirection\n            const deltaPercentage = Math.max(\n              deltaOffset / (old?.startSize ?? 0),\n              -0.999999\n            )\n\n            old.columnSizingStart.forEach(([columnId, headerSize]) => {\n              newColumnSizing[columnId] =\n                Math.round(\n                  Math.max(headerSize + headerSize * deltaPercentage, 0) * 100\n                ) / 100\n            })\n\n            return {\n              ...old,\n              deltaOffset,\n              deltaPercentage,\n            }\n          })\n\n          if (\n            table.options.columnResizeMode === 'onChange' ||\n            eventType === 'end'\n          ) {\n            table.setColumnSizing(old => ({\n              ...old,\n              ...newColumnSizing,\n            }))\n          }\n        }\n\n        const onMove = (clientXPos?: number) => updateOffset('move', clientXPos)\n\n        const onEnd = (clientXPos?: number) => {\n          updateOffset('end', clientXPos)\n\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            isResizingColumn: false,\n            startOffset: null,\n            startSize: null,\n            deltaOffset: null,\n            deltaPercentage: null,\n            columnSizingStart: [],\n          }))\n        }\n\n        const contextDocument =\n          _contextDocument || typeof document !== 'undefined' ? document : null\n\n        const mouseEvents = {\n          moveHandler: (e: MouseEvent) => onMove(e.clientX),\n          upHandler: (e: MouseEvent) => {\n            contextDocument?.removeEventListener(\n              'mousemove',\n              mouseEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'mouseup',\n              mouseEvents.upHandler\n            )\n            onEnd(e.clientX)\n          },\n        }\n\n        const touchEvents = {\n          moveHandler: (e: TouchEvent) => {\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onMove(e.touches[0]!.clientX)\n            return false\n          },\n          upHandler: (e: TouchEvent) => {\n            contextDocument?.removeEventListener(\n              'touchmove',\n              touchEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'touchend',\n              touchEvents.upHandler\n            )\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onEnd(e.touches[0]?.clientX)\n          },\n        }\n\n        const passiveIfSupported = passiveEventSupported()\n          ? { passive: false }\n          : false\n\n        if (isTouchStartEvent(e)) {\n          contextDocument?.addEventListener(\n            'touchmove',\n            touchEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'touchend',\n            touchEvents.upHandler,\n            passiveIfSupported\n          )\n        } else {\n          contextDocument?.addEventListener(\n            'mousemove',\n            mouseEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'mouseup',\n            mouseEvents.upHandler,\n            passiveIfSupported\n          )\n        }\n\n        table.setColumnSizingInfo(old => ({\n          ...old,\n          startOffset: clientX,\n          startSize,\n          deltaOffset: 0,\n          deltaPercentage: 0,\n          columnSizingStart,\n          isResizingColumn: column.id,\n        }))\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnSizing = updater =>\n      table.options.onColumnSizingChange?.(updater)\n    table.setColumnSizingInfo = updater =>\n      table.options.onColumnSizingInfoChange?.(updater)\n    table.resetColumnSizing = defaultState => {\n      table.setColumnSizing(\n        defaultState ? {} : table.initialState.columnSizing ?? {}\n      )\n    }\n    table.resetHeaderSizeInfo = defaultState => {\n      table.setColumnSizingInfo(\n        defaultState\n          ? getDefaultColumnSizingInfoState()\n          : table.initialState.columnSizingInfo ??\n              getDefaultColumnSizingInfoState()\n      )\n    }\n    table.getTotalSize = () =>\n      table.getHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getLeftTotalSize = () =>\n      table.getLeftHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getCenterTotalSize = () =>\n      table.getCenterHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getRightTotalSize = () =>\n      table.getRightHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n  },\n}\n\nlet passiveSupported: boolean | null = null\nexport function passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported\n\n  let supported = false\n  try {\n    const options = {\n      get passive() {\n        supported = true\n        return false\n      },\n    }\n\n    const noop = () => {}\n\n    window.addEventListener('test', noop, options)\n    window.removeEventListener('test', noop)\n  } catch (err) {\n    supported = false\n  }\n  passiveSupported = supported\n  return passiveSupported\n}\n\nfunction isTouchStartEvent(e: unknown): e is TouchEvent {\n  return (e as TouchEvent).type === 'touchstart'\n}\n", "import { ColumnPinningPosition } from '..'\nimport {\n  Cell,\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  Row,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type VisibilityState = Record<string, boolean>\n\nexport interface VisibilityTableState {\n  columnVisibility: VisibilityState\n}\n\nexport interface VisibilityOptions {\n  /**\n   * Whether to enable column hiding. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#enablehiding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  enableHiding?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnVisibility` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#oncolumnvisibilitychange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  onColumnVisibilityChange?: OnChangeFn<VisibilityState>\n}\n\nexport type VisibilityDefaultOptions = Pick<\n  VisibilityOptions,\n  'onColumnVisibilityChange'\n>\n\nexport interface VisibilityInstance<TData extends RowData> {\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the unpinned/center portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcentervisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCenterVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether all columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsAllColumnsVisible: () => boolean\n  /**\n   * Returns whether any columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getissomecolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsSomeColumnsVisible: () => boolean\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the left portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getleftvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getLeftVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the right portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getrightvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getRightVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a handler for toggling the visibility of all columns, meant to be bound to a `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettoggleallcolumnsvisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleAllColumnsVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Returns a flat array of columns that are visible, including parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a flat array of leaf-node columns that are visible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the column visibility state to the initial state. If `defaultState` is provided, the state will be reset to `{}`\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#resetcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  resetColumnVisibility: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnVisibility` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#setcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  setColumnVisibility: (updater: Updater<VisibilityState>) => void\n  /**\n   * Toggles the visibility of all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#toggleallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleAllColumnsVisible: (value?: boolean) => void\n}\n\nexport interface VisibilityColumnDef {\n  enableHiding?: boolean\n}\n\nexport interface VisibilityRow<TData extends RowData> {\n  _getAllVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns an array of cells that account for column visibility for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface VisibilityColumn {\n  /**\n   * Returns whether the column can be hidden\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcanhide)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCanHide: () => boolean\n  /**\n   * Returns whether the column is visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsVisible: () => boolean\n  /**\n   * Returns a function that can be used to toggle the column visibility. This function can be used to bind to an event handler to a checkbox.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettogglevisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Toggles the visibility of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#togglevisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleVisibility: (value?: boolean) => void\n}\n\n//\n\nexport const ColumnVisibility: TableFeature = {\n  getInitialState: (state): VisibilityTableState => {\n    return {\n      columnVisibility: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): VisibilityDefaultOptions => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleVisibility = value => {\n      if (column.getCanHide()) {\n        table.setColumnVisibility(old => ({\n          ...old,\n          [column.id]: value ?? !column.getIsVisible(),\n        }))\n      }\n    }\n    column.getIsVisible = () => {\n      const childColumns = column.columns\n      return (\n        (childColumns.length\n          ? childColumns.some(c => c.getIsVisible())\n          : table.getState().columnVisibility?.[column.id]) ?? true\n      )\n    }\n\n    column.getCanHide = () => {\n      return (\n        (column.columnDef.enableHiding ?? true) &&\n        (table.options.enableHiding ?? true)\n      )\n    }\n    column.getToggleVisibilityHandler = () => {\n      return (e: unknown) => {\n        column.toggleVisibility?.(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row._getAllVisibleCells = memo(\n      () => [row.getAllCells(), table.getState().columnVisibility],\n      cells => {\n        return cells.filter(cell => cell.column.getIsVisible())\n      },\n      getMemoOptions(table.options, 'debugRows', '_getAllVisibleCells')\n    )\n    row.getVisibleCells = memo(\n      () => [\n        row.getLeftVisibleCells(),\n        row.getCenterVisibleCells(),\n        row.getRightVisibleCells(),\n      ],\n      (left, center, right) => [...left, ...center, ...right],\n      getMemoOptions(table.options, 'debugRows', 'getVisibleCells')\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    const makeVisibleColumnsMethod = (\n      key: string,\n      getColumns: () => Column<TData, unknown>[]\n    ): (() => Column<TData, unknown>[]) => {\n      return memo(\n        () => [\n          getColumns(),\n          getColumns()\n            .filter(d => d.getIsVisible())\n            .map(d => d.id)\n            .join('_'),\n        ],\n        columns => {\n          return columns.filter(d => d.getIsVisible?.())\n        },\n        getMemoOptions(table.options, 'debugColumns', key)\n      )\n    }\n\n    table.getVisibleFlatColumns = makeVisibleColumnsMethod(\n      'getVisibleFlatColumns',\n      () => table.getAllFlatColumns()\n    )\n    table.getVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getVisibleLeafColumns',\n      () => table.getAllLeafColumns()\n    )\n    table.getLeftVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getLeftVisibleLeafColumns',\n      () => table.getLeftLeafColumns()\n    )\n    table.getRightVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getRightVisibleLeafColumns',\n      () => table.getRightLeafColumns()\n    )\n    table.getCenterVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getCenterVisibleLeafColumns',\n      () => table.getCenterLeafColumns()\n    )\n\n    table.setColumnVisibility = updater =>\n      table.options.onColumnVisibilityChange?.(updater)\n\n    table.resetColumnVisibility = defaultState => {\n      table.setColumnVisibility(\n        defaultState ? {} : table.initialState.columnVisibility ?? {}\n      )\n    }\n\n    table.toggleAllColumnsVisible = value => {\n      value = value ?? !table.getIsAllColumnsVisible()\n\n      table.setColumnVisibility(\n        table.getAllLeafColumns().reduce(\n          (obj, column) => ({\n            ...obj,\n            [column.id]: !value ? !column.getCanHide?.() : value,\n          }),\n          {}\n        )\n      )\n    }\n\n    table.getIsAllColumnsVisible = () =>\n      !table.getAllLeafColumns().some(column => !column.getIsVisible?.())\n\n    table.getIsSomeColumnsVisible = () =>\n      table.getAllLeafColumns().some(column => column.getIsVisible?.())\n\n    table.getToggleAllColumnsVisibilityHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllColumnsVisible(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nexport function _getVisibleLeafColumns<TData extends RowData>(\n  table: Table<TData>,\n  position?: ColumnPinningPosition | 'center'\n) {\n  return !position\n    ? table.getVisibleLeafColumns()\n    : position === 'center'\n      ? table.getCenterVisibleLeafColumns()\n      : position === 'left'\n        ? table.getLeftVisibleLeafColumns()\n        : table.getRightVisibleLeafColumns()\n}\n", "import { RowModel } from '..'\nimport { Table, RowData, TableFeature } from '../types'\n\nexport interface GlobalFacetingInstance<TData extends RowData> {\n  _getGlobalFacetedMinMaxValues?: () => undefined | [number, number]\n  _getGlobalFacetedRowModel?: () => RowModel<TData>\n  _getGlobalFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model for the table after **global** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedRowModel: () => RowModel<TData>\n  /**\n   * Returns the faceted unique values for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedUniqueValues: () => Map<any, number>\n}\n\n//\n\nexport const GlobalFaceting: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table._getGlobalFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, '__global__')\n\n    table.getGlobalFacetedRowModel = () => {\n      if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getGlobalFacetedRowModel()\n    }\n\n    table._getGlobalFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, '__global__')\n    table.getGlobalFacetedUniqueValues = () => {\n      if (!table._getGlobalFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return table._getGlobalFacetedUniqueValues()\n    }\n\n    table._getGlobalFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, '__global__')\n    table.getGlobalFacetedMinMaxValues = () => {\n      if (!table._getGlobalFacetedMinMaxValues) {\n        return\n      }\n\n      return table._getGlobalFacetedMinMaxValues()\n    }\n  },\n}\n", "import { FilterFn, FilterFnOption } from '..'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport interface GlobalFilterTableState {\n  globalFilter: any\n}\n\nexport interface GlobalFilterColumnDef {\n  /**\n   * Enables/disables the **global** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  enableGlobalFilter?: boolean\n}\n\nexport interface GlobalFilterColumn {\n  /**\n   * Returns whether or not the column can be **globally** filtered. Set to `false` to disable a column from being scanned during global filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getcanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getCanGlobalFilter: () => boolean\n}\n\nexport interface GlobalFilterOptions<TData extends RowData> {\n  /**\n   * Enables/disables **global** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  enableGlobalFilter?: boolean\n  /**\n   * If provided, this function will be called with the column and should return `true` or `false` to indicate whether this column should be used for global filtering.\n   *\n   * This is useful if the column can contain data that is not `string` or `number` (i.e. `undefined`).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getcolumncanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getColumnCanGlobalFilter?: (column: Column<TData, unknown>) => boolean\n  /**\n   * The filter function to use for global filtering.\n   * - A `string` referencing a built-in filter function\n   * - A `string` that references a custom filter functions provided via the `tableOptions.filterFns` option\n   * - A custom filter function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#globalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  globalFilterFn?: FilterFnOption<TData>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.globalFilter` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#onglobalfilterchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  onGlobalFilterChange?: OnChangeFn<any>\n}\n\nexport interface GlobalFilterInstance<TData extends RowData> {\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getGlobalAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getglobalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getGlobalFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const GlobalFiltering: TableFeature = {\n  getInitialState: (state): GlobalFilterTableState => {\n    return {\n      globalFilter: undefined,\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GlobalFilterOptions<TData> => {\n    return {\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        const value = table\n          .getCoreRowModel()\n          .flatRows[0]?._getAllCellsByColumnId()\n          [column.id]?.getValue()\n\n        return typeof value === 'string' || typeof value === 'number'\n      },\n    } as GlobalFilterOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getCanGlobalFilter = () => {\n      return (\n        (column.columnDef.enableGlobalFilter ?? true) &&\n        (table.options.enableGlobalFilter ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        (table.options.getColumnCanGlobalFilter?.(column) ?? true) &&\n        !!column.accessorFn\n      )\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.getGlobalAutoFilterFn = () => {\n      return filterFns.includesString\n    }\n\n    table.getGlobalFilterFn = () => {\n      const { globalFilterFn: globalFilterFn } = table.options\n\n      return isFunction(globalFilterFn)\n        ? globalFilterFn\n        : globalFilterFn === 'auto'\n          ? table.getGlobalAutoFilterFn()\n          : table.options.filterFns?.[globalFilterFn as string] ??\n            filterFns[globalFilterFn as BuiltInFilterFn]\n    }\n\n    table.setGlobalFilter = updater => {\n      table.options.onGlobalFilterChange?.(updater)\n    }\n\n    table.resetGlobalFilter = defaultState => {\n      table.setGlobalFilter(\n        defaultState ? undefined : table.initialState.globalFilter\n      )\n    }\n  },\n}\n", "import { RowModel } from '..'\nimport {\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { makeStateUpdater } from '../utils'\n\nexport type ExpandedStateList = Record<string, boolean>\nexport type ExpandedState = true | Record<string, boolean>\nexport interface ExpandedTableState {\n  expanded: ExpandedState\n}\n\nexport interface ExpandedRow {\n  /**\n   * Returns whether the row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanExpand: () => boolean\n  /**\n   * Returns whether all parent rows of the row are expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallparentsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllParentsExpanded: () => boolean\n  /**\n   * Returns whether the row is expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsExpanded: () => boolean\n  /**\n   * Returns a function that can be used to toggle the expanded state of the row. This function can be used to bind to an event handler to a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleExpandedHandler: () => () => void\n  /**\n   * Toggles the expanded state (or sets it if `expanded` is provided) for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleExpanded: (expanded?: boolean) => void\n}\n\nexport interface ExpandedOptions<TData extends RowData> {\n  /**\n   * Enable this setting to automatically reset the expanded state of the table when expanding state changes.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#autoresetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  autoResetExpanded?: boolean\n  /**\n   * Enable/disable expanding for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#enableexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  enableExpanding?: boolean\n  /**\n   * This function is responsible for returning the expanded row model. If this function is not provided, the table will not expand rows. You can use the default exported `getExpandedRowModel` function to get the expanded row model or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row is currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisrowexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsRowExpanded?: (row: Row<TData>) => boolean\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getrowcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getRowCanExpand?: (row: Row<TData>) => boolean\n  /**\n   * Enables manual row expansion. If this is set to `true`, `getExpandedRowModel` will not be used to expand rows and you would be expected to perform the expansion in your own data model. This is useful if you are doing server-side expansion.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#manualexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  manualExpanding?: boolean\n  /**\n   * This function is called when the `expanded` table state changes. If a function is provided, you will be responsible for managing this state on your own. To pass the managed state back to the table, use the `tableOptions.state.expanded` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#onexpandedchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  onExpandedChange?: OnChangeFn<ExpandedState>\n  /**\n   * If `true` expanded rows will be paginated along with the rest of the table (which means expanded rows may span multiple pages). If `false` expanded rows will not be considered for pagination (which means expanded rows will always render on their parents page. This also means more rows will be rendered than the set page size)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#paginateexpandedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  paginateExpandedRows?: boolean\n}\n\nexport interface ExpandedInstance<TData extends RowData> {\n  _autoResetExpanded: () => void\n  _getExpandedRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether there are any rows that can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcansomerowsexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanSomeRowsExpand: () => boolean\n  /**\n   * Returns the maximum depth of the expanded rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandeddepth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedDepth: () => number\n  /**\n   * Returns the row model after expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether all rows are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllRowsExpanded: () => boolean\n  /**\n   * Returns whether there are any rows that are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getissomerowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsSomeRowsExpanded: () => boolean\n  /**\n   * Returns the row model before expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getpreexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getPreExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle the expanded state of all rows. This handler is meant to be used with an `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleallrowsexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleAllRowsExpandedHandler: () => (event: unknown) => void\n  /**\n   * Resets the expanded state of the table to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#resetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  resetExpanded: (defaultState?: boolean) => void\n  /**\n   * Updates the expanded state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#setexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  setExpanded: (updater: Updater<ExpandedState>) => void\n  /**\n   * Toggles the expanded state for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleAllRowsExpanded: (expanded?: boolean) => void\n}\n\n//\n\nexport const RowExpanding: TableFeature = {\n  getInitialState: (state): ExpandedTableState => {\n    return {\n      expanded: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ExpandedOptions<TData> => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetExpanded = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetExpanded ??\n        !table.options.manualExpanding\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetExpanded()\n          queued = false\n        })\n      }\n    }\n    table.setExpanded = updater => table.options.onExpandedChange?.(updater)\n    table.toggleAllRowsExpanded = expanded => {\n      if (expanded ?? !table.getIsAllRowsExpanded()) {\n        table.setExpanded(true)\n      } else {\n        table.setExpanded({})\n      }\n    }\n    table.resetExpanded = defaultState => {\n      table.setExpanded(defaultState ? {} : table.initialState?.expanded ?? {})\n    }\n    table.getCanSomeRowsExpand = () => {\n      return table\n        .getPrePaginationRowModel()\n        .flatRows.some(row => row.getCanExpand())\n    }\n    table.getToggleAllRowsExpandedHandler = () => {\n      return (e: unknown) => {\n        ;(e as any).persist?.()\n        table.toggleAllRowsExpanded()\n      }\n    }\n    table.getIsSomeRowsExpanded = () => {\n      const expanded = table.getState().expanded\n      return expanded === true || Object.values(expanded).some(Boolean)\n    }\n    table.getIsAllRowsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      // If expanded is true, save some cycles and return true\n      if (typeof expanded === 'boolean') {\n        return expanded === true\n      }\n\n      if (!Object.keys(expanded).length) {\n        return false\n      }\n\n      // If any row is not expanded, return false\n      if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n        return false\n      }\n\n      // They must all be expanded :shrug:\n      return true\n    }\n    table.getExpandedDepth = () => {\n      let maxDepth = 0\n\n      const rowIds =\n        table.getState().expanded === true\n          ? Object.keys(table.getRowModel().rowsById)\n          : Object.keys(table.getState().expanded)\n\n      rowIds.forEach(id => {\n        const splitId = id.split('.')\n        maxDepth = Math.max(maxDepth, splitId.length)\n      })\n\n      return maxDepth\n    }\n    table.getPreExpandedRowModel = () => table.getSortedRowModel()\n    table.getExpandedRowModel = () => {\n      if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n        table._getExpandedRowModel = table.options.getExpandedRowModel(table)\n      }\n\n      if (table.options.manualExpanding || !table._getExpandedRowModel) {\n        return table.getPreExpandedRowModel()\n      }\n\n      return table._getExpandedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleExpanded = expanded => {\n      table.setExpanded(old => {\n        const exists = old === true ? true : !!old?.[row.id]\n\n        let oldExpanded: ExpandedStateList = {}\n\n        if (old === true) {\n          Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n            oldExpanded[rowId] = true\n          })\n        } else {\n          oldExpanded = old\n        }\n\n        expanded = expanded ?? !exists\n\n        if (!exists && expanded) {\n          return {\n            ...oldExpanded,\n            [row.id]: true,\n          }\n        }\n\n        if (exists && !expanded) {\n          const { [row.id]: _, ...rest } = oldExpanded\n          return rest\n        }\n\n        return old\n      })\n    }\n    row.getIsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      return !!(\n        table.options.getIsRowExpanded?.(row) ??\n        (expanded === true || expanded?.[row.id])\n      )\n    }\n    row.getCanExpand = () => {\n      return (\n        table.options.getRowCanExpand?.(row) ??\n        ((table.options.enableExpanding ?? true) && !!row.subRows?.length)\n      )\n    }\n    row.getIsAllParentsExpanded = () => {\n      let isFullyExpanded = true\n      let currentRow = row\n\n      while (isFullyExpanded && currentRow.parentId) {\n        currentRow = table.getRow(currentRow.parentId, true)\n        isFullyExpanded = currentRow.getIsExpanded()\n      }\n\n      return isFullyExpanded\n    }\n    row.getToggleExpandedHandler = () => {\n      const canExpand = row.getCanExpand()\n\n      return () => {\n        if (!canExpand) return\n        row.toggleExpanded()\n      }\n    }\n  },\n}\n", "import {\n  OnChangeFn,\n  Table,\n  RowModel,\n  Updater,\n  <PERSON>Data,\n  TableFeature,\n} from '../types'\nimport {\n  functionalUpdate,\n  getMemoOptions,\n  makeStateUpdater,\n  memo,\n} from '../utils'\n\nexport interface PaginationState {\n  pageIndex: number\n  pageSize: number\n}\n\nexport interface PaginationTableState {\n  pagination: PaginationState\n}\n\nexport interface PaginationInitialTableState {\n  pagination?: Partial<PaginationState>\n}\n\nexport interface PaginationOptions {\n  /**\n   * If set to `true`, pagination will be reset to the first page when page-altering state changes eg. `data` is updated, filters change, grouping changes, etc.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#autoresetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  autoResetPageIndex?: boolean\n  /**\n   * Returns the row model after pagination has taken place, but no further.\n   *\n   * Pagination columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Enables manual pagination. If this option is set to `true`, the table will not automatically paginate rows using `getPaginationRowModel()` and instead will expect you to manually paginate the rows before passing them to the table. This is useful if you are doing server-side pagination and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#manualpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  manualPagination?: boolean\n  /**\n   * If this function is provided, it will be called when the pagination state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.pagination` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#onpaginationchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  onPaginationChange?: OnChangeFn<PaginationState>\n  /**\n   * When manually controlling pagination, you can supply a total `pageCount` value to the table if you know it (Or supply a `rowCount` and `pageCount` will be calculated). If you do not know how many pages there are, you can set this to `-1`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#pagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  pageCount?: number\n  /**\n   * When manually controlling pagination, you can supply a total `rowCount` value to the table if you know it. The `pageCount` can be calculated from this value and the `pageSize`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#rowcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  rowCount?: number\n}\n\nexport interface PaginationDefaultOptions {\n  onPaginationChange: OnChangeFn<PaginationState>\n}\n\nexport interface PaginationInstance<TData extends RowData> {\n  _autoResetPageIndex: () => void\n  _getPaginationRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether the table can go to the next page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcannextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanNextPage: () => boolean\n  /**\n   * Returns whether the table can go to the previous page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcanpreviouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanPreviousPage: () => boolean\n  /**\n   * Returns the page count. If manually paginating or controlling the pagination state, this will come directly from the `options.pageCount` table option, otherwise it will be calculated from the table data using the total row count and current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageCount: () => number\n  /**\n   * Returns the row count. If manually paginating or controlling the pagination state, this will come directly from the `options.rowCount` table option, otherwise it will be calculated from the table data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getrowcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getRowCount: () => number\n  /**\n   * Returns an array of page options (zero-index-based) for the current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpageoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageOptions: () => number[]\n  /**\n   * Returns the row model for the table after pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getprepaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPrePaginationRowModel: () => RowModel<TData>\n  /**\n   * Increments the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#nextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  nextPage: () => void\n  /**\n   * Decrements the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#previouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  previousPage: () => void\n  /**\n   * Sets the page index to `0`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#firstpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  firstPage: () => void\n  /**\n   * Sets the page index to the last page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#lastpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  lastPage: () => void\n  /**\n   * Resets the page index to its initial state. If `defaultState` is `true`, the page index will be reset to `0` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageIndex: (defaultState?: boolean) => void\n  /**\n   * Resets the page size to its initial state. If `defaultState` is `true`, the page size will be reset to `10` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageSize: (defaultState?: boolean) => void\n  /**\n   * Resets the **pagination** state to `initialState.pagination`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPagination: (defaultState?: boolean) => void\n  /**\n   * @deprecated The page count no longer exists in the pagination state. Just pass as a table option instead.\n   */\n  setPageCount: (updater: Updater<number>) => void\n  /**\n   * Updates the page index using the provided function or value in the `state.pagination.pageIndex` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageIndex: (updater: Updater<number>) => void\n  /**\n   * Updates the page size using the provided function or value in the `state.pagination.pageSize` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageSize: (updater: Updater<number>) => void\n  /**\n   * Sets or updates the `state.pagination` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPagination: (updater: Updater<PaginationState>) => void\n}\n\n//\n\nconst defaultPageIndex = 0\nconst defaultPageSize = 10\n\nconst getDefaultPaginationState = (): PaginationState => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize,\n})\n\nexport const RowPagination: TableFeature = {\n  getInitialState: (state): PaginationTableState => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...state?.pagination,\n      },\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): PaginationDefaultOptions => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table),\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetPageIndex = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetPageIndex ??\n        !table.options.manualPagination\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetPageIndex()\n          queued = false\n        })\n      }\n    }\n    table.setPagination = updater => {\n      const safeUpdater: Updater<PaginationState> = old => {\n        let newState = functionalUpdate(updater, old)\n\n        return newState\n      }\n\n      return table.options.onPaginationChange?.(safeUpdater)\n    }\n    table.resetPagination = defaultState => {\n      table.setPagination(\n        defaultState\n          ? getDefaultPaginationState()\n          : table.initialState.pagination ?? getDefaultPaginationState()\n      )\n    }\n    table.setPageIndex = updater => {\n      table.setPagination(old => {\n        let pageIndex = functionalUpdate(updater, old.pageIndex)\n\n        const maxPageIndex =\n          typeof table.options.pageCount === 'undefined' ||\n          table.options.pageCount === -1\n            ? Number.MAX_SAFE_INTEGER\n            : table.options.pageCount - 1\n\n        pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex))\n\n        return {\n          ...old,\n          pageIndex,\n        }\n      })\n    }\n    table.resetPageIndex = defaultState => {\n      table.setPageIndex(\n        defaultState\n          ? defaultPageIndex\n          : table.initialState?.pagination?.pageIndex ?? defaultPageIndex\n      )\n    }\n    table.resetPageSize = defaultState => {\n      table.setPageSize(\n        defaultState\n          ? defaultPageSize\n          : table.initialState?.pagination?.pageSize ?? defaultPageSize\n      )\n    }\n    table.setPageSize = updater => {\n      table.setPagination(old => {\n        const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize))\n        const topRowIndex = old.pageSize * old.pageIndex!\n        const pageIndex = Math.floor(topRowIndex / pageSize)\n\n        return {\n          ...old,\n          pageIndex,\n          pageSize,\n        }\n      })\n    }\n    //deprecated\n    table.setPageCount = updater =>\n      table.setPagination(old => {\n        let newPageCount = functionalUpdate(\n          updater,\n          table.options.pageCount ?? -1\n        )\n\n        if (typeof newPageCount === 'number') {\n          newPageCount = Math.max(-1, newPageCount)\n        }\n\n        return {\n          ...old,\n          pageCount: newPageCount,\n        }\n      })\n\n    table.getPageOptions = memo(\n      () => [table.getPageCount()],\n      pageCount => {\n        let pageOptions: number[] = []\n        if (pageCount && pageCount > 0) {\n          pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i)\n        }\n        return pageOptions\n      },\n      getMemoOptions(table.options, 'debugTable', 'getPageOptions')\n    )\n\n    table.getCanPreviousPage = () => table.getState().pagination.pageIndex > 0\n\n    table.getCanNextPage = () => {\n      const { pageIndex } = table.getState().pagination\n\n      const pageCount = table.getPageCount()\n\n      if (pageCount === -1) {\n        return true\n      }\n\n      if (pageCount === 0) {\n        return false\n      }\n\n      return pageIndex < pageCount - 1\n    }\n\n    table.previousPage = () => {\n      return table.setPageIndex(old => old - 1)\n    }\n\n    table.nextPage = () => {\n      return table.setPageIndex(old => {\n        return old + 1\n      })\n    }\n\n    table.firstPage = () => {\n      return table.setPageIndex(0)\n    }\n\n    table.lastPage = () => {\n      return table.setPageIndex(table.getPageCount() - 1)\n    }\n\n    table.getPrePaginationRowModel = () => table.getExpandedRowModel()\n    table.getPaginationRowModel = () => {\n      if (\n        !table._getPaginationRowModel &&\n        table.options.getPaginationRowModel\n      ) {\n        table._getPaginationRowModel =\n          table.options.getPaginationRowModel(table)\n      }\n\n      if (table.options.manualPagination || !table._getPaginationRowModel) {\n        return table.getPrePaginationRowModel()\n      }\n\n      return table._getPaginationRowModel()\n    }\n\n    table.getPageCount = () => {\n      return (\n        table.options.pageCount ??\n        Math.ceil(table.getRowCount() / table.getState().pagination.pageSize)\n      )\n    }\n\n    table.getRowCount = () => {\n      return (\n        table.options.rowCount ?? table.getPrePaginationRowModel().rows.length\n      )\n    }\n  },\n}\n", "import {\n  OnChangeFn,\n  Updater,\n  Table,\n  Row,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type RowPinningPosition = false | 'top' | 'bottom'\n\nexport interface RowPinningState {\n  bottom?: string[]\n  top?: string[]\n}\n\nexport interface RowPinningTableState {\n  rowPinning: RowPinningState\n}\n\nexport interface RowPinningOptions<TData extends RowData> {\n  /**\n   * Enables/disables row pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#enablerowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  enableRowPinning?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * When `false`, pinned rows will not be visible if they are filtered or paginated out of the table. When `true`, pinned rows will always be visible regardless of filtering or pagination. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#keeppinnedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  keepPinnedRows?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowPinning` changes. This overrides the default internal state management, so you will also need to supply `state.rowPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#onrowpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/onrowpinningchange)\n   */\n  onRowPinningChange?: OnChangeFn<RowPinningState>\n}\n\nexport interface RowPinningDefaultOptions {\n  onRowPinningChange: OnChangeFn<RowPinningState>\n}\n\nexport interface RowPinningRow {\n  /**\n   * Returns whether or not the row can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getcanpin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the row. (`'top'`, `'bottom'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getispinned-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getIsPinned: () => RowPinningPosition\n  /**\n   * Returns the numeric pinned index of the row within a pinned row group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getpinnedindex-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a row to the `'top'` or `'bottom'`, or unpins the row to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#pin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  pin: (\n    position: RowPinningPosition,\n    includeLeafRows?: boolean,\n    includeParentRows?: boolean\n  ) => void\n}\n\nexport interface RowPinningInstance<TData extends RowData> {\n  _getPinnedRows: (position: 'top' | 'bottom') => Row<TData>[]\n  /**\n   * Returns all bottom pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getbottomrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getBottomRows: () => Row<TData>[]\n  /**\n   * Returns all rows that are not pinned to the top or bottom.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getcenterrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getCenterRows: () => Row<TData>[]\n  /**\n   * Returns whether or not any rows are pinned. Optionally specify to only check for pinned rows in either the `top` or `bottom` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getissomerowspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getIsSomeRowsPinned: (position?: RowPinningPosition) => boolean\n  /**\n   * Returns all top pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#gettoprows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getTopRows: () => Row<TData>[]\n  /**\n   * Resets the **rowPinning** state to `initialState.rowPinning`, or `true` can be passed to force a default blank state reset to `{ top: [], bottom: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#resetrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  resetRowPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#setrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  setRowPinning: (updater: Updater<RowPinningState>) => void\n}\n\n//\n\nconst getDefaultRowPinningState = (): RowPinningState => ({\n  top: [],\n  bottom: [],\n})\n\nexport const RowPinning: TableFeature = {\n  getInitialState: (state): RowPinningTableState => {\n    return {\n      rowPinning: getDefaultRowPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowPinningDefaultOptions => {\n    return {\n      onRowPinningChange: makeStateUpdater('rowPinning', table),\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.pin = (position, includeLeafRows, includeParentRows) => {\n      const leafRowIds = includeLeafRows\n        ? row.getLeafRows().map(({ id }) => id)\n        : []\n      const parentRowIds = includeParentRows\n        ? row.getParentRows().map(({ id }) => id)\n        : []\n      const rowIds = new Set([...parentRowIds, row.id, ...leafRowIds])\n\n      table.setRowPinning(old => {\n        if (position === 'bottom') {\n          return {\n            top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n            bottom: [\n              ...(old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n          }\n        }\n\n        if (position === 'top') {\n          return {\n            top: [\n              ...(old?.top ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n            bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n          }\n        }\n\n        return {\n          top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n          bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n        }\n      })\n    }\n    row.getCanPin = () => {\n      const { enableRowPinning, enablePinning } = table.options\n      if (typeof enableRowPinning === 'function') {\n        return enableRowPinning(row)\n      }\n      return enableRowPinning ?? enablePinning ?? true\n    }\n    row.getIsPinned = () => {\n      const rowIds = [row.id]\n\n      const { top, bottom } = table.getState().rowPinning\n\n      const isTop = rowIds.some(d => top?.includes(d))\n      const isBottom = rowIds.some(d => bottom?.includes(d))\n\n      return isTop ? 'top' : isBottom ? 'bottom' : false\n    }\n    row.getPinnedIndex = () => {\n      const position = row.getIsPinned()\n      if (!position) return -1\n\n      const visiblePinnedRowIds = table\n        ._getPinnedRows(position)\n        ?.map(({ id }) => id)\n\n      return visiblePinnedRowIds?.indexOf(row.id) ?? -1\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowPinning = updater => table.options.onRowPinningChange?.(updater)\n\n    table.resetRowPinning = defaultState =>\n      table.setRowPinning(\n        defaultState\n          ? getDefaultRowPinningState()\n          : table.initialState?.rowPinning ?? getDefaultRowPinningState()\n      )\n\n    table.getIsSomeRowsPinned = position => {\n      const pinningState = table.getState().rowPinning\n\n      if (!position) {\n        return Boolean(pinningState.top?.length || pinningState.bottom?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table._getPinnedRows = memo(\n      position => [\n        table.getRowModel().rows,\n        table.getState().rowPinning[position!],\n        position,\n      ],\n      (visibleRows, pinnedRowIds, position) => {\n        const rows =\n          table.options.keepPinnedRows ?? true\n            ? //get all rows that are pinned even if they would not be otherwise visible\n              //account for expanded parent rows, but not pagination or filtering\n              (pinnedRowIds ?? []).map(rowId => {\n                const row = table.getRow(rowId, true)\n                return row.getIsAllParentsExpanded() ? row : null\n              })\n            : //else get only visible rows that are pinned\n              (pinnedRowIds ?? []).map(\n                rowId => visibleRows.find(row => row.id === rowId)!\n              )\n\n        return rows\n          .filter(Boolean)\n          .map(d => ({ ...d, position })) as Row<TData>[]\n      },\n      getMemoOptions(table.options, 'debugRows', '_getPinnedRows')\n    )\n\n    table.getTopRows = () => table._getPinnedRows('top')\n\n    table.getBottomRows = () => table._getPinnedRows('bottom')\n\n    table.getCenterRows = memo(\n      () => [\n        table.getRowModel().rows,\n        table.getState().rowPinning.top,\n        table.getState().rowPinning.bottom,\n      ],\n      (allRows, top, bottom) => {\n        const topAndBottom = new Set([...(top ?? []), ...(bottom ?? [])])\n        return allRows.filter(d => !topAndBottom.has(d.id))\n      },\n      getMemoOptions(table.options, 'debugRows', 'getCenterRows')\n    )\n  },\n}\n", "import {\n  OnChangeFn,\n  Table,\n  Row,\n  <PERSON>Model,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type RowSelectionState = Record<string, boolean>\n\nexport interface RowSelectionTableState {\n  rowSelection: RowSelectionState\n}\n\nexport interface RowSelectionOptions<TData extends RowData> {\n  /**\n   * - Enables/disables multiple row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable multiple row selection for that row's children/grandchildren\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablemultirowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableMultiRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * - Enables/disables row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable row selection for that row\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablerowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * Enables/disables automatic sub-row selection when a parent row is selected, or a function that enables/disables automatic sub-row selection for each row.\n   * (Use in combination with expanding or grouping features)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablesubrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableSubRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowSelection` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#onrowselectionchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  onRowSelectionChange?: OnChangeFn<RowSelectionState>\n  // enableGroupingRowSelection?:\n  //   | boolean\n  //   | ((\n  //       row: Row<TData>\n  //     ) => boolean)\n  // isAdditiveSelectEvent?: (e: unknown) => boolean\n  // isInclusiveSelectEvent?: (e: unknown) => boolean\n  // selectRowsFn?: (\n  //   table: Table<TData>,\n  //   rowModel: RowModel<TData>\n  // ) => RowModel<TData>\n}\n\nexport interface RowSelectionRow {\n  /**\n   * Returns whether or not the row can multi-select.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanmultiselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanMultiSelect: () => boolean\n  /**\n   * Returns whether or not the row can be selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelect: () => boolean\n  /**\n   * Returns whether or not the row can select sub rows automatically when the parent row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselectsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelectSubRows: () => boolean\n  /**\n   * Returns whether or not all of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallsubrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllSubRowsSelected: () => boolean\n  /**\n   * Returns whether or not the row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSelected: () => boolean\n  /**\n   * Returns whether or not some of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomeselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeSelected: () => boolean\n  /**\n   * Returns a handler that can be used to toggle the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleSelectedHandler: () => (event: unknown) => void\n  /**\n   * Selects/deselects the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleSelected: (value?: boolean, opts?: { selectChildren?: boolean }) => void\n}\n\nexport interface RowSelectionInstance<TData extends RowData> {\n  /**\n   * Returns the row model of all rows that are selected after filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getfilteredselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getFilteredSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getgroupedselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getGroupedSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether or not all rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllPageRowsSelected: () => boolean\n  /**\n   * Returns whether or not all rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomepagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomePageRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeRowsSelected: () => boolean\n  /**\n   * Returns the core row model of all rows before row selection has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getpreselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getPreSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallpagerowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllPageRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Returns a handler that can be used to toggle all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallrowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Resets the **rowSelection** state to the `initialState.rowSelection`, or `true` can be passed to force a default blank state reset to `{}`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#resetrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  resetRowSelection: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowSelection` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#setrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  setRowSelection: (updater: Updater<RowSelectionState>) => void\n  /**\n   * Selects/deselects all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllPageRowsSelected: (value?: boolean) => void\n  /**\n   * Selects/deselects all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllRowsSelected: (value?: boolean) => void\n}\n\n//\n\nexport const RowSelection: TableFeature = {\n  getInitialState: (state): RowSelectionTableState => {\n    return {\n      rowSelection: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowSelectionOptions<TData> => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true,\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowSelection = updater =>\n      table.options.onRowSelectionChange?.(updater)\n    table.resetRowSelection = defaultState =>\n      table.setRowSelection(\n        defaultState ? {} : table.initialState.rowSelection ?? {}\n      )\n    table.toggleAllRowsSelected = value => {\n      table.setRowSelection(old => {\n        value =\n          typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected()\n\n        const rowSelection = { ...old }\n\n        const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows\n\n        // We don't use `mutateRowIsSelected` here for performance reasons.\n        // All of the rows are flat already, so it wouldn't be worth it\n        if (value) {\n          preGroupedFlatRows.forEach(row => {\n            if (!row.getCanSelect()) {\n              return\n            }\n            rowSelection[row.id] = true\n          })\n        } else {\n          preGroupedFlatRows.forEach(row => {\n            delete rowSelection[row.id]\n          })\n        }\n\n        return rowSelection\n      })\n    }\n    table.toggleAllPageRowsSelected = value =>\n      table.setRowSelection(old => {\n        const resolvedValue =\n          typeof value !== 'undefined'\n            ? value\n            : !table.getIsAllPageRowsSelected()\n\n        const rowSelection: RowSelectionState = { ...old }\n\n        table.getRowModel().rows.forEach(row => {\n          mutateRowIsSelected(rowSelection, row.id, resolvedValue, true, table)\n        })\n\n        return rowSelection\n      })\n\n    // addRowSelectionRange: rowId => {\n    //   const {\n    //     rows,\n    //     rowsById,\n    //     options: { selectGroupingRows, selectSubRows },\n    //   } = table\n\n    //   const findSelectedRow = (rows: Row[]) => {\n    //     let found\n    //     rows.find(d => {\n    //       if (d.getIsSelected()) {\n    //         found = d\n    //         return true\n    //       }\n    //       const subFound = findSelectedRow(d.subRows || [])\n    //       if (subFound) {\n    //         found = subFound\n    //         return true\n    //       }\n    //       return false\n    //     })\n    //     return found\n    //   }\n\n    //   const firstRow = findSelectedRow(rows) || rows[0]\n    //   const lastRow = rowsById[rowId]\n\n    //   let include = false\n    //   const selectedRowIds = {}\n\n    //   const addRow = (row: Row) => {\n    //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n    //       rowsById,\n    //       selectGroupingRows: selectGroupingRows!,\n    //       selectSubRows: selectSubRows!,\n    //     })\n    //   }\n\n    //   table.rows.forEach(row => {\n    //     const isFirstRow = row.id === firstRow.id\n    //     const isLastRow = row.id === lastRow.id\n\n    //     if (isFirstRow || isLastRow) {\n    //       if (!include) {\n    //         include = true\n    //       } else if (include) {\n    //         addRow(row)\n    //         include = false\n    //       }\n    //     }\n\n    //     if (include) {\n    //       addRow(row)\n    //     }\n    //   })\n\n    //   table.setRowSelection(selectedRowIds)\n    // },\n    table.getPreSelectedRowModel = () => table.getCoreRowModel()\n    table.getSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getCoreRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getSelectedRowModel')\n    )\n\n    table.getFilteredSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getFilteredRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFilteredSelectedRowModel')\n    )\n\n    table.getGroupedSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getSortedRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getGroupedSelectedRowModel')\n    )\n\n    ///\n\n    // getGroupingRowCanSelect: rowId => {\n    //   const row = table.getRow(rowId)\n\n    //   if (!row) {\n    //     throw new Error()\n    //   }\n\n    //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n    //     return table.options.enableGroupingRowSelection(row)\n    //   }\n\n    //   return table.options.enableGroupingRowSelection ?? false\n    // },\n\n    table.getIsAllRowsSelected = () => {\n      const preGroupedFlatRows = table.getFilteredRowModel().flatRows\n      const { rowSelection } = table.getState()\n\n      let isAllRowsSelected = Boolean(\n        preGroupedFlatRows.length && Object.keys(rowSelection).length\n      )\n\n      if (isAllRowsSelected) {\n        if (\n          preGroupedFlatRows.some(\n            row => row.getCanSelect() && !rowSelection[row.id]\n          )\n        ) {\n          isAllRowsSelected = false\n        }\n      }\n\n      return isAllRowsSelected\n    }\n\n    table.getIsAllPageRowsSelected = () => {\n      const paginationFlatRows = table\n        .getPaginationRowModel()\n        .flatRows.filter(row => row.getCanSelect())\n      const { rowSelection } = table.getState()\n\n      let isAllPageRowsSelected = !!paginationFlatRows.length\n\n      if (\n        isAllPageRowsSelected &&\n        paginationFlatRows.some(row => !rowSelection[row.id])\n      ) {\n        isAllPageRowsSelected = false\n      }\n\n      return isAllPageRowsSelected\n    }\n\n    table.getIsSomeRowsSelected = () => {\n      const totalSelected = Object.keys(\n        table.getState().rowSelection ?? {}\n      ).length\n      return (\n        totalSelected > 0 &&\n        totalSelected < table.getFilteredRowModel().flatRows.length\n      )\n    }\n\n    table.getIsSomePageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows\n      return table.getIsAllPageRowsSelected()\n        ? false\n        : paginationFlatRows\n            .filter(row => row.getCanSelect())\n            .some(d => d.getIsSelected() || d.getIsSomeSelected())\n    }\n\n    table.getToggleAllRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n\n    table.getToggleAllPageRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllPageRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleSelected = (value, opts) => {\n      const isSelected = row.getIsSelected()\n\n      table.setRowSelection(old => {\n        value = typeof value !== 'undefined' ? value : !isSelected\n\n        if (row.getCanSelect() && isSelected === value) {\n          return old\n        }\n\n        const selectedRowIds = { ...old }\n\n        mutateRowIsSelected(\n          selectedRowIds,\n          row.id,\n          value,\n          opts?.selectChildren ?? true,\n          table\n        )\n\n        return selectedRowIds\n      })\n    }\n    row.getIsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isRowSelected(row, rowSelection)\n    }\n\n    row.getIsSomeSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'some'\n    }\n\n    row.getIsAllSubRowsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'all'\n    }\n\n    row.getCanSelect = () => {\n      if (typeof table.options.enableRowSelection === 'function') {\n        return table.options.enableRowSelection(row)\n      }\n\n      return table.options.enableRowSelection ?? true\n    }\n\n    row.getCanSelectSubRows = () => {\n      if (typeof table.options.enableSubRowSelection === 'function') {\n        return table.options.enableSubRowSelection(row)\n      }\n\n      return table.options.enableSubRowSelection ?? true\n    }\n\n    row.getCanMultiSelect = () => {\n      if (typeof table.options.enableMultiRowSelection === 'function') {\n        return table.options.enableMultiRowSelection(row)\n      }\n\n      return table.options.enableMultiRowSelection ?? true\n    }\n    row.getToggleSelectedHandler = () => {\n      const canSelect = row.getCanSelect()\n\n      return (e: unknown) => {\n        if (!canSelect) return\n        row.toggleSelected(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nconst mutateRowIsSelected = <TData extends RowData>(\n  selectedRowIds: Record<string, boolean>,\n  id: string,\n  value: boolean,\n  includeChildren: boolean,\n  table: Table<TData>\n) => {\n  const row = table.getRow(id, true)\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key])\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true\n    }\n  } else {\n    delete selectedRowIds[id]\n  }\n  // }\n\n  if (includeChildren && row.subRows?.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row =>\n      mutateRowIsSelected(selectedRowIds, row.id, value, includeChildren, table)\n    )\n  }\n}\n\nexport function selectRowsFn<TData extends RowData>(\n  table: Table<TData>,\n  rowModel: RowModel<TData>\n): RowModel<TData> {\n  const rowSelection = table.getState().rowSelection\n\n  const newSelectedFlatRows: Row<TData>[] = []\n  const newSelectedRowsById: Record<string, Row<TData>> = {}\n\n  // Filters top level and nested rows\n  const recurseRows = (rows: Row<TData>[], depth = 0): Row<TData>[] => {\n    return rows\n      .map(row => {\n        const isSelected = isRowSelected(row, rowSelection)\n\n        if (isSelected) {\n          newSelectedFlatRows.push(row)\n          newSelectedRowsById[row.id] = row\n        }\n\n        if (row.subRows?.length) {\n          row = {\n            ...row,\n            subRows: recurseRows(row.subRows, depth + 1),\n          }\n        }\n\n        if (isSelected) {\n          return row\n        }\n      })\n      .filter(Boolean) as Row<TData>[]\n  }\n\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById,\n  }\n}\n\nexport function isRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>\n): boolean {\n  return selection[row.id] ?? false\n}\n\nexport function isSubRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>,\n  table: Table<TData>\n): boolean | 'some' | 'all' {\n  if (!row.subRows?.length) return false\n\n  let allChildrenSelected = true\n  let someSelected = false\n\n  row.subRows.forEach(subRow => {\n    // Bail out early if we know both of these\n    if (someSelected && !allChildrenSelected) {\n      return\n    }\n\n    if (subRow.getCanSelect()) {\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true\n      } else {\n        allChildrenSelected = false\n      }\n    }\n\n    // Check row selection of nested subrows\n    if (subRow.subRows && subRow.subRows.length) {\n      const subRowChildrenSelected = isSubRowSelected(subRow, selection, table)\n      if (subRowChildrenSelected === 'all') {\n        someSelected = true\n      } else if (subRowChildrenSelected === 'some') {\n        someSelected = true\n        allChildrenSelected = false\n      } else {\n        allChildrenSelected = false\n      }\n    }\n  })\n\n  return allChildrenSelected ? 'all' : someSelected ? 'some' : false\n}\n", "import { SortingFn } from './features/RowSorting'\n\nexport const reSplitAlphaNumeric = /([0-9]+)/gm\n\nconst alphanumeric: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\nconst alphanumericCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\nconst datetime: SortingFn<any> = (rowA, rowB, columnId) => {\n  const a = rowA.getValue<Date>(columnId)\n  const b = rowB.getValue<Date>(columnId)\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0\n}\n\nconst basic: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId))\n}\n\n// Utils\n\nfunction compareBasic(a: any, b: any) {\n  return a === b ? 0 : a > b ? 1 : -1\n}\n\nfunction toString(a: any) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return ''\n    }\n    return String(a)\n  }\n  if (typeof a === 'string') {\n    return a\n  }\n  return ''\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr: string, bStr: string) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean)\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean)\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift()!\n    const bb = b.shift()!\n\n    const an = parseInt(aa, 10)\n    const bn = parseInt(bb, 10)\n\n    const combo = [an, bn].sort()\n\n    // Both are string\n    if (isNaN(combo[0]!)) {\n      if (aa > bb) {\n        return 1\n      }\n      if (bb > aa) {\n        return -1\n      }\n      continue\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1]!)) {\n      return isNaN(an) ? -1 : 1\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1\n    }\n    if (bn > an) {\n      return -1\n    }\n  }\n\n  return a.length - b.length\n}\n\n// Exports\n\nexport const sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic,\n}\n\nexport type BuiltInSortingFn = keyof typeof sortingFns\n", "import { RowModel } from '..'\nimport {\n  BuiltInSortingFn,\n  reSplitAlphaNumeric,\n  sortingFns,\n} from '../sortingFns'\n\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  SortingFns,\n  TableFeature,\n} from '../types'\n\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type SortDirection = 'asc' | 'desc'\n\nexport interface ColumnSort {\n  desc: boolean\n  id: string\n}\n\nexport type SortingState = ColumnSort[]\n\nexport interface SortingTableState {\n  sorting: SortingState\n}\n\nexport interface SortingFn<TData extends RowData> {\n  (rowA: Row<TData>, rowB: Row<TData>, columnId: string): number\n}\n\nexport type CustomSortingFns<TData extends RowData> = Record<\n  string,\n  SortingFn<TData>\n>\n\nexport type SortingFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof SortingFns\n  | BuiltInSortingFn\n  | SortingFn<TData>\n\nexport interface SortingColumnDef<TData extends RowData> {\n  /**\n   * Enables/Disables multi-sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Inverts the order of the sorting for this column. This is useful for values that have an inverted best/worst scale where lower numbers are better, eg. a ranking (1st, 2nd, 3rd) or golf-like scoring\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#invertsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  invertSorting?: boolean\n  /**\n   * Set to `true` for sorting toggles on this column to start in the descending direction.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n  /**\n   * The sorting function to use with this column.\n   * - A `string` referencing a built-in sorting function\n   * - A custom sorting function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortingFn?: SortingFnOption<TData>\n  /**\n   * The priority of undefined values when sorting this column.\n   * - `false`\n   *   - Undefined values will be considered tied and need to be sorted by the next column filter or original index (whichever applies)\n   * - `-1`\n   *   - Undefined values will be sorted with higher priority (ascending) (if ascending, undefined will appear on the beginning of the list)\n   * - `1`\n   *   - Undefined values will be sorted with lower priority (descending) (if ascending, undefined will appear on the end of the list)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortundefined)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortUndefined?: false | -1 | 1 | 'first' | 'last'\n}\n\nexport interface SortingColumn<TData extends RowData> {\n  /**\n   * Removes this column from the table's sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#clearsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  clearSorting: () => void\n  /**\n   * Returns a sort direction automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortDir: () => SortDirection\n  /**\n   * Returns a sorting function automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortingFn: () => SortingFn<TData>\n  /**\n   * Returns whether this column can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcanmultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanMultiSort: () => boolean\n  /**\n   * Returns whether this column can be sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcansort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanSort: () => boolean\n  /**\n   * Returns the first direction that should be used when sorting this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getfirstsortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getFirstSortDir: () => SortDirection\n  /**\n   * Returns the current sort direction of this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getissorted)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getIsSorted: () => false | SortDirection\n  /**\n   * Returns the next sorting order.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getnextsortingorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getNextSortingOrder: () => SortDirection | false\n  /**\n   * Returns the index position of this column's sorting within the sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortIndex: () => number\n  /**\n   * Returns the resolved sorting function to be used for this column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortingFn: () => SortingFn<TData>\n  /**\n   * Returns a function that can be used to toggle this column's sorting state. This is useful for attaching a click handler to the column header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#gettogglesortinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getToggleSortingHandler: () => undefined | ((event: unknown) => void)\n  /**\n   * Toggles this columns sorting state. If `desc` is provided, it will force the sort direction to that value. If `isMulti` is provided, it will additivity multi-sort the column (or toggle it if it is already sorted).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#togglesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  toggleSorting: (desc?: boolean, isMulti?: boolean) => void\n}\n\ninterface SortingOptionsBase {\n  /**\n   * Enables/disables the ability to remove multi-sorts\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultiremove)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiRemove?: boolean\n  /**\n   * Enables/Disables multi-sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Enables/Disables the ability to remove sorting for the table.\n   * - If `true` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'none' -> ...\n   * - If `false` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'desc' -> 'asc' -> ...\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesortingremoval)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSortingRemoval?: boolean\n  /**\n   * This function is used to retrieve the sorted row model. If using server-side sorting, this function is not required. To use client-side sorting, pass the exported `getSortedRowModel()` from your adapter to your table or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Pass a custom function that will be used to determine if a multi-sort event should be triggered. It is passed the event from the sort toggle handler and should return `true` if the event should trigger a multi-sort.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#ismultisortevent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  isMultiSortEvent?: (e: unknown) => boolean\n  /**\n   * Enables manual sorting for the table. If this is `true`, you will be expected to sort your data before it is passed to the table. This is useful if you are doing server-side sorting.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#manualsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  manualSorting?: boolean\n  /**\n   * Set a maximum number of columns that can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#maxmultisortcolcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  maxMultiSortColCount?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.sorting` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#onsortingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  onSortingChange?: OnChangeFn<SortingState>\n  /**\n   * If `true`, all sorts will default to descending as their first toggle state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n}\n\ntype ResolvedSortingFns = keyof SortingFns extends never\n  ? {\n      sortingFns?: Record<string, SortingFn<any>>\n    }\n  : {\n      sortingFns: Record<keyof SortingFns, SortingFn<any>>\n    }\n\nexport interface SortingOptions<TData extends RowData>\n  extends SortingOptionsBase,\n    ResolvedSortingFns {}\n\nexport interface SortingInstance<TData extends RowData> {\n  _getSortedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getpresortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getPreSortedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **sorting** state to `initialState.sorting`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#resetsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  resetSorting: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.sorting` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#setsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  setSorting: (updater: Updater<SortingState>) => void\n}\n\n//\n\nexport const RowSorting: TableFeature = {\n  getInitialState: (state): SortingTableState => {\n    return {\n      sorting: [],\n      ...state,\n    }\n  },\n\n  getDefaultColumnDef: <TData extends RowData>(): SortingColumnDef<TData> => {\n    return {\n      sortingFn: 'auto',\n      sortUndefined: 1,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): SortingOptions<TData> => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: (e: unknown) => {\n        return (e as MouseEvent).shiftKey\n      },\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoSortingFn = () => {\n      const firstRows = table.getFilteredRowModel().flatRows.slice(10)\n\n      let isString = false\n\n      for (const row of firstRows) {\n        const value = row?.getValue(column.id)\n\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return sortingFns.datetime\n        }\n\n        if (typeof value === 'string') {\n          isString = true\n\n          if (value.split(reSplitAlphaNumeric).length > 1) {\n            return sortingFns.alphanumeric\n          }\n        }\n      }\n\n      if (isString) {\n        return sortingFns.text\n      }\n\n      return sortingFns.basic\n    }\n    column.getAutoSortDir = () => {\n      const firstRow = table.getFilteredRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return 'asc'\n      }\n\n      return 'desc'\n    }\n    column.getSortingFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.sortingFn)\n        ? column.columnDef.sortingFn\n        : column.columnDef.sortingFn === 'auto'\n          ? column.getAutoSortingFn()\n          : table.options.sortingFns?.[column.columnDef.sortingFn as string] ??\n            sortingFns[column.columnDef.sortingFn as BuiltInSortingFn]\n    }\n    column.toggleSorting = (desc, multi) => {\n      // if (column.columns.length) {\n      //   column.columns.forEach((c, i) => {\n      //     if (c.id) {\n      //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n      //     }\n      //   })\n      //   return\n      // }\n\n      // this needs to be outside of table.setSorting to be in sync with rerender\n      const nextSortingOrder = column.getNextSortingOrder()\n      const hasManualValue = typeof desc !== 'undefined' && desc !== null\n\n      table.setSorting(old => {\n        // Find any existing sorting for this column\n        const existingSorting = old?.find(d => d.id === column.id)\n        const existingIndex = old?.findIndex(d => d.id === column.id)\n\n        let newSorting: SortingState = []\n\n        // What should we do with this sort action?\n        let sortAction: 'add' | 'remove' | 'toggle' | 'replace'\n        let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc'\n\n        // Multi-mode\n        if (old?.length && column.getCanMultiSort() && multi) {\n          if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'add'\n          }\n        } else {\n          // Normal mode\n          if (old?.length && existingIndex !== old.length - 1) {\n            sortAction = 'replace'\n          } else if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'replace'\n          }\n        }\n\n        // Handle toggle states that will remove the sorting\n        if (sortAction === 'toggle') {\n          // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n          if (!hasManualValue) {\n            // Is our intention to remove?\n            if (!nextSortingOrder) {\n              sortAction = 'remove'\n            }\n          }\n        }\n\n        if (sortAction === 'add') {\n          newSorting = [\n            ...old,\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n          // Take latest n columns\n          newSorting.splice(\n            0,\n            newSorting.length -\n              (table.options.maxMultiSortColCount ?? Number.MAX_SAFE_INTEGER)\n          )\n        } else if (sortAction === 'toggle') {\n          // This flips (or sets) the\n          newSorting = old.map(d => {\n            if (d.id === column.id) {\n              return {\n                ...d,\n                desc: nextDesc,\n              }\n            }\n            return d\n          })\n        } else if (sortAction === 'remove') {\n          newSorting = old.filter(d => d.id !== column.id)\n        } else {\n          newSorting = [\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n        }\n\n        return newSorting\n      })\n    }\n\n    column.getFirstSortDir = () => {\n      const sortDescFirst =\n        column.columnDef.sortDescFirst ??\n        table.options.sortDescFirst ??\n        column.getAutoSortDir() === 'desc'\n      return sortDescFirst ? 'desc' : 'asc'\n    }\n\n    column.getNextSortingOrder = (multi?: boolean) => {\n      const firstSortDirection = column.getFirstSortDir()\n      const isSorted = column.getIsSorted()\n\n      if (!isSorted) {\n        return firstSortDirection\n      }\n\n      if (\n        isSorted !== firstSortDirection &&\n        (table.options.enableSortingRemoval ?? true) && // If enableSortRemove, enable in general\n        (multi ? table.options.enableMultiRemove ?? true : true) // If multi, don't allow if enableMultiRemove))\n      ) {\n        return false\n      }\n      return isSorted === 'desc' ? 'asc' : 'desc'\n    }\n\n    column.getCanSort = () => {\n      return (\n        (column.columnDef.enableSorting ?? true) &&\n        (table.options.enableSorting ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getCanMultiSort = () => {\n      return (\n        column.columnDef.enableMultiSort ??\n        table.options.enableMultiSort ??\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsSorted = () => {\n      const columnSort = table.getState().sorting?.find(d => d.id === column.id)\n\n      return !columnSort ? false : columnSort.desc ? 'desc' : 'asc'\n    }\n\n    column.getSortIndex = () =>\n      table.getState().sorting?.findIndex(d => d.id === column.id) ?? -1\n\n    column.clearSorting = () => {\n      //clear sorting for just 1 column\n      table.setSorting(old =>\n        old?.length ? old.filter(d => d.id !== column.id) : []\n      )\n    }\n\n    column.getToggleSortingHandler = () => {\n      const canSort = column.getCanSort()\n\n      return (e: unknown) => {\n        if (!canSort) return\n        ;(e as any).persist?.()\n        column.toggleSorting?.(\n          undefined,\n          column.getCanMultiSort() ? table.options.isMultiSortEvent?.(e) : false\n        )\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setSorting = updater => table.options.onSortingChange?.(updater)\n    table.resetSorting = defaultState => {\n      table.setSorting(defaultState ? [] : table.initialState?.sorting ?? [])\n    }\n    table.getPreSortedRowModel = () => table.getGroupedRowModel()\n    table.getSortedRowModel = () => {\n      if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n        table._getSortedRowModel = table.options.getSortedRowModel(table)\n      }\n\n      if (table.options.manualSorting || !table._getSortedRowModel) {\n        return table.getPreSortedRowModel()\n      }\n\n      return table._getSortedRowModel()\n    }\n  },\n}\n", "import { functionalUpdate, getMemoOptions, memo, RequiredKeys } from '../utils'\n\nimport {\n  Updater,\n  TableOptionsResolved,\n  TableState,\n  Table,\n  InitialTableState,\n  Row,\n  Column,\n  RowModel,\n  ColumnDef,\n  TableOptions,\n  RowData,\n  TableMeta,\n  ColumnDefResolved,\n  GroupColumnDef,\n  TableFeature,\n} from '../types'\n\n//\nimport { createColumn } from './column'\nimport { Headers } from './headers'\n//\n\nimport { ColumnFaceting } from '../features/ColumnFaceting'\nimport { ColumnFiltering } from '../features/ColumnFiltering'\nimport { ColumnGrouping } from '../features/ColumnGrouping'\nimport { ColumnOrdering } from '../features/ColumnOrdering'\nimport { ColumnPinning } from '../features/ColumnPinning'\nimport { ColumnSizing } from '../features/ColumnSizing'\nimport { ColumnVisibility } from '../features/ColumnVisibility'\nimport { GlobalFaceting } from '../features/GlobalFaceting'\nimport { GlobalFiltering } from '../features/GlobalFiltering'\nimport { RowExpanding } from '../features/RowExpanding'\nimport { RowPagination } from '../features/RowPagination'\nimport { RowPinning } from '../features/RowPinning'\nimport { RowSelection } from '../features/RowSelection'\nimport { RowSorting } from '../features/RowSorting'\n\nconst builtInFeatures = [\n  Headers,\n  ColumnVisibility,\n  ColumnOrdering,\n  ColumnPinning,\n  ColumnFaceting,\n  ColumnFiltering,\n  GlobalFaceting, //depends on ColumnFaceting\n  GlobalFiltering, //depends on ColumnFiltering\n  RowSorting,\n  ColumnGrouping, //depends on RowSorting\n  RowExpanding,\n  RowPagination,\n  RowPinning,\n  RowSelection,\n  ColumnSizing,\n] as const\n\n//\n\nexport interface CoreTableState {}\n\nexport interface CoreOptions<TData extends RowData> {\n  /**\n   * An array of extra features that you can add to the table instance.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#_features)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  _features?: TableFeature[]\n  /**\n   * Set this option to override any of the `autoReset...` feature options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#autoresetall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  autoResetAll?: boolean\n  /**\n   * The array of column defs to use for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  columns: ColumnDef<TData, any>[]\n  /**\n   * The data for the table to display. This array should match the type you provided to `table.setRowType<...>`. Columns can access this data via string/index or a functional accessor. When the `data` option changes reference, the table will reprocess the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#data)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  data: TData[]\n  /**\n   * Set this option to `true` to output all debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugAll?: boolean\n  /**\n   * Set this option to `true` to output cell debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcells]\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugCells?: boolean\n  /**\n   * Set this option to `true` to output column debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugColumns?: boolean\n  /**\n   * Set this option to `true` to output header debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugHeaders?: boolean\n  /**\n   * Set this option to `true` to output row debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugRows?: boolean\n  /**\n   * Set this option to `true` to output table debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugtable)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugTable?: boolean\n  /**\n   * Default column options to use for all column defs supplied to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#defaultcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  defaultColumn?: Partial<ColumnDef<TData, unknown>>\n  /**\n   * This required option is a factory for a function that computes and returns the core row model for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: (table: Table<any>) => () => RowModel<any>\n  /**\n   * This optional function is used to derive a unique ID for any given row. If not provided the rows index is used (nested rows join together with `.` using their grandparents' index eg. `index.index.index`). If you need to identify individual rows that are originating from any server-side operations, it's suggested you use this function to return an ID that makes sense regardless of network IO/ambiguity eg. a userId, taskId, database ID field, etc.\n   * @example getRowId: row => row.userId\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowId?: (originalRow: TData, index: number, parent?: Row<TData>) => string\n  /**\n   * This optional function is used to access the sub rows for any given row. If you are using nested rows, you will need to use this function to return the sub rows object (or undefined) from the row.\n   * @example getSubRows: row => row.subRows\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getSubRows?: (originalRow: TData, index: number) => undefined | TData[]\n  /**\n   * Use this option to optionally pass initial state to the table. This state will be used when resetting various table states either automatically by the table (eg. `options.autoResetPageIndex`) or via functions like `table.resetRowSelection()`. Most reset function allow you optionally pass a flag to reset to a blank/default state instead of the initial state.\n   *\n   * Table state will not be reset when this object changes, which also means that the initial state object does not need to be stable.\n   *\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState?: InitialTableState\n  /**\n   * This option is used to optionally implement the merging of table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#mergeoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  mergeOptions?: (\n    defaultOptions: TableOptions<TData>,\n    options: Partial<TableOptions<TData>>\n  ) => TableOptions<TData>\n  /**\n   * You can pass any object to `options.meta` and access it anywhere the `table` is available via `table.options.meta`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#meta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  meta?: TableMeta<TData>\n  /**\n   * The `onStateChange` option can be used to optionally listen to state changes within the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#onstatechange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  onStateChange: (updater: Updater<TableState>) => void\n  /**\n   * Value used when the desired value is not found in the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#renderfallbackvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  renderFallbackValue: any\n  /**\n   * The `state` option can be used to optionally _control_ part or all of the table state. The state you pass here will merge with and overwrite the internal automatically-managed state to produce the final state for the table. You can also listen to state changes via the `onStateChange` option.\n   * > Note: Any state passed in here will override both the internal state and any other `initialState` you provide.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#state)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  state: Partial<TableState>\n}\n\nexport interface CoreInstance<TData extends RowData> {\n  _features: readonly TableFeature[]\n  _getAllFlatColumnsById: () => Record<string, Column<TData, unknown>>\n  _getColumnDefs: () => ColumnDef<TData, unknown>[]\n  _getCoreRowModel?: () => RowModel<TData>\n  _getDefaultColumnDef: () => Partial<ColumnDef<TData, unknown>>\n  _getRowId: (_: TData, index: number, parent?: Row<TData>) => string\n  _queue: (cb: () => void) => void\n  /**\n   * Returns all columns in the table in their normalized and nested hierarchy.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all columns in the table flattened to a single level.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all leaf-node columns in the table flattened to a single level. This does not include parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a single column by its ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getColumn: (columnId: string) => Column<TData, unknown> | undefined\n  /**\n   * Returns the core row model before any processing has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: () => RowModel<TData>\n  /**\n   * Returns the row with the given ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRow: (id: string, searchAll?: boolean) => Row<TData>\n  /**\n   * Returns the final model after all processing from other used features has been applied. This is the row model that is most commonly used for rendering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowModel: () => RowModel<TData>\n  /**\n   * Call this function to get the table's current state. It's recommended to use this function and its state, especially when managing the table state manually. It is the exact same state used internally by the table for every feature and function it provides.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getState: () => TableState\n  /**\n   * This is the resolved initial state of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState: TableState\n  /**\n   * A read-only reference to the table's current options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#options)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  options: RequiredKeys<TableOptionsResolved<TData>, 'state'>\n  /**\n   * Call this function to reset the table state to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#reset)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  reset: () => void\n  /**\n   * This function can be used to update the table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setOptions: (newOptions: Updater<TableOptionsResolved<TData>>) => void\n  /**\n   * Call this function to update the table state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setState: (updater: Updater<TableState>) => void\n}\n\nexport function createTable<TData extends RowData>(\n  options: TableOptionsResolved<TData>\n): Table<TData> {\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    (options.debugAll || options.debugTable)\n  ) {\n    console.info('Creating Table Instance...')\n  }\n\n  const _features = [...builtInFeatures, ...(options._features ?? [])]\n\n  let table = { _features } as unknown as Table<TData>\n\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions?.(table))\n  }, {}) as TableOptionsResolved<TData>\n\n  const mergeOptions = (options: TableOptionsResolved<TData>) => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options)\n    }\n\n    return {\n      ...defaultOptions,\n      ...options,\n    }\n  }\n\n  const coreInitialState: CoreTableState = {}\n\n  let initialState = {\n    ...coreInitialState,\n    ...(options.initialState ?? {}),\n  } as TableState\n\n  table._features.forEach(feature => {\n    initialState = (feature.getInitialState?.(initialState) ??\n      initialState) as TableState\n  })\n\n  const queued: (() => void)[] = []\n  let queuedTimeout = false\n\n  const coreInstance: CoreInstance<TData> = {\n    _features,\n    options: {\n      ...defaultOptions,\n      ...options,\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb)\n\n      if (!queuedTimeout) {\n        queuedTimeout = true\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve()\n          .then(() => {\n            while (queued.length) {\n              queued.shift()!()\n            }\n            queuedTimeout = false\n          })\n          .catch(error =>\n            setTimeout(() => {\n              throw error\n            })\n          )\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState)\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options)\n      table.options = mergeOptions(newOptions) as RequiredKeys<\n        TableOptionsResolved<TData>,\n        'state'\n      >\n    },\n\n    getState: () => {\n      return table.options.state as TableState\n    },\n\n    setState: (updater: Updater<TableState>) => {\n      table.options.onStateChange?.(updater)\n    },\n\n    _getRowId: (row: TData, index: number, parent?: Row<TData>) =>\n      table.options.getRowId?.(row, index, parent) ??\n      `${parent ? [parent.id, index].join('.') : index}`,\n\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table)\n      }\n\n      return table._getCoreRowModel!()\n    },\n\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel()\n    },\n    //in next version, we should just pass in the row model as the optional 2nd arg\n    getRow: (id: string, searchAll?: boolean) => {\n      let row = (\n        searchAll ? table.getPrePaginationRowModel() : table.getRowModel()\n      ).rowsById[id]\n\n      if (!row) {\n        row = table.getCoreRowModel().rowsById[id]\n        if (!row) {\n          if (process.env.NODE_ENV !== 'production') {\n            throw new Error(`getRow could not find row with ID: ${id}`)\n          }\n          throw new Error()\n        }\n      }\n\n      return row\n    },\n    _getDefaultColumnDef: memo(\n      () => [table.options.defaultColumn],\n      defaultColumn => {\n        defaultColumn = (defaultColumn ?? {}) as Partial<\n          ColumnDef<TData, unknown>\n        >\n\n        return {\n          header: props => {\n            const resolvedColumnDef = props.header.column\n              .columnDef as ColumnDefResolved<TData>\n\n            if (resolvedColumnDef.accessorKey) {\n              return resolvedColumnDef.accessorKey\n            }\n\n            if (resolvedColumnDef.accessorFn) {\n              return resolvedColumnDef.id\n            }\n\n            return null\n          },\n          // footer: props => props.header.column.id,\n          cell: props => props.renderValue<any>()?.toString?.() ?? null,\n          ...table._features.reduce((obj, feature) => {\n            return Object.assign(obj, feature.getDefaultColumnDef?.())\n          }, {}),\n          ...defaultColumn,\n        } as Partial<ColumnDef<TData, unknown>>\n      },\n      getMemoOptions(options, 'debugColumns', '_getDefaultColumnDef')\n    ),\n\n    _getColumnDefs: () => table.options.columns,\n\n    getAllColumns: memo(\n      () => [table._getColumnDefs()],\n      columnDefs => {\n        const recurseColumns = (\n          columnDefs: ColumnDef<TData, unknown>[],\n          parent?: Column<TData, unknown>,\n          depth = 0\n        ): Column<TData, unknown>[] => {\n          return columnDefs.map(columnDef => {\n            const column = createColumn(table, columnDef, depth, parent)\n\n            const groupingColumnDef = columnDef as GroupColumnDef<\n              TData,\n              unknown\n            >\n\n            column.columns = groupingColumnDef.columns\n              ? recurseColumns(groupingColumnDef.columns, column, depth + 1)\n              : []\n\n            return column\n          })\n        }\n\n        return recurseColumns(columnDefs)\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllColumns')\n    ),\n\n    getAllFlatColumns: memo(\n      () => [table.getAllColumns()],\n      allColumns => {\n        return allColumns.flatMap(column => {\n          return column.getFlatColumns()\n        })\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllFlatColumns')\n    ),\n\n    _getAllFlatColumnsById: memo(\n      () => [table.getAllFlatColumns()],\n      flatColumns => {\n        return flatColumns.reduce(\n          (acc, column) => {\n            acc[column.id] = column\n            return acc\n          },\n          {} as Record<string, Column<TData, unknown>>\n        )\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllFlatColumnsById')\n    ),\n\n    getAllLeafColumns: memo(\n      () => [table.getAllColumns(), table._getOrderColumnsFn()],\n      (allColumns, orderColumns) => {\n        let leafColumns = allColumns.flatMap(column => column.getLeafColumns())\n        return orderColumns(leafColumns)\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllLeafColumns')\n    ),\n\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId]\n\n      if (process.env.NODE_ENV !== 'production' && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`)\n      }\n\n      return column\n    },\n  }\n\n  Object.assign(table, coreInstance)\n\n  for (let index = 0; index < table._features.length; index++) {\n    const feature = table._features[index]\n    feature?.createTable?.(table)\n  }\n\n  return table\n}\n", "import { createRow } from '../core/row'\nimport { Table, Row, RowModel, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getCoreRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.options.data],\n      (\n        data\n      ): {\n        rows: Row<TData>[]\n        flatRows: Row<TData>[]\n        rowsById: Record<string, Row<TData>>\n      } => {\n        const rowModel: RowModel<TData> = {\n          rows: [],\n          flatRows: [],\n          rowsById: {},\n        }\n\n        const accessRows = (\n          originalRows: TData[],\n          depth = 0,\n          parentRow?: Row<TData>\n        ): Row<TData>[] => {\n          const rows = [] as Row<TData>[]\n\n          for (let i = 0; i < originalRows.length; i++) {\n            // This could be an expensive check at scale, so we should move it somewhere else, but where?\n            // if (!id) {\n            //   if (process.env.NODE_ENV !== 'production') {\n            //     throw new Error(`getRowId expected an ID, but got ${id}`)\n            //   }\n            // }\n\n            // Make the row\n            const row = createRow(\n              table,\n              table._getRowId(originalRows[i]!, i, parentRow),\n              originalRows[i]!,\n              i,\n              depth,\n              undefined,\n              parentRow?.id\n            )\n\n            // Keep track of every row in a flat array\n            rowModel.flatRows.push(row)\n            // Also keep track of every row by its ID\n            rowModel.rowsById[row.id] = row\n            // Push table row into parent\n            rows.push(row)\n\n            // Get the original subrows\n            if (table.options.getSubRows) {\n              row.originalSubRows = table.options.getSubRows(\n                originalRows[i]!,\n                i\n              )\n\n              // Then recursively access them\n              if (row.originalSubRows?.length) {\n                row.subRows = accessRows(row.originalSubRows, depth + 1, row)\n              }\n            }\n          }\n\n          return rows\n        }\n\n        rowModel.rows = accessRows(data)\n\n        return rowModel\n      },\n      getMemoOptions(table.options, 'debugTable', 'getRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getExpandedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().expanded,\n        table.getPreExpandedRowModel(),\n        table.options.paginateExpandedRows,\n      ],\n      (expanded, rowModel, paginateExpandedRows) => {\n        if (\n          !rowModel.rows.length ||\n          (expanded !== true && !Object.keys(expanded ?? {}).length)\n        ) {\n          return rowModel\n        }\n\n        if (!paginateExpandedRows) {\n          // Only expand rows at this point if they are being paginated\n          return rowModel\n        }\n\n        return expandRows(rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getExpandedRowModel')\n    )\n}\n\nexport function expandRows<TData extends RowData>(rowModel: RowModel<TData>) {\n  const expandedRows: Row<TData>[] = []\n\n  const handleRow = (row: Row<TData>) => {\n    expandedRows.push(row)\n\n    if (row.subRows?.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow)\n    }\n  }\n\n  rowModel.rows.forEach(handleRow)\n\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById,\n  }\n}\n", "import { Table, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getFacetedMinMaxValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => undefined | [number, number] {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return undefined\n\n        const firstValue =\n          facetedRowModel.flatRows[0]?.getUniqueValues(columnId)\n\n        if (typeof firstValue === 'undefined') {\n          return undefined\n        }\n\n        let facetedMinMaxValues: [any, any] = [firstValue, firstValue]\n\n        for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n          const values =\n            facetedRowModel.flatRows[i]!.getUniqueValues<number>(columnId)\n\n          for (let j = 0; j < values.length; j++) {\n            const value = values[j]!\n\n            if (value < facetedMinMaxValues[0]) {\n              facetedMinMaxValues[0] = value\n            } else if (value > facetedMinMaxValues[1]) {\n              facetedMinMaxValues[1] = value\n            }\n          }\n        }\n\n        return facetedMinMaxValues\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFacetedMinMaxValues')\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Row, RowModel, Table, RowData } from '../types'\n\nexport function filterRows<TData extends RowData>(\n  rows: Row<TData>[],\n  filterRowImpl: (row: Row<TData>) => any,\n  table: Table<TData>\n) {\n  if (table.options.filterFromLeafRows) {\n    return filterRowModelFromLeafs(rows, filterRowImpl, table)\n  }\n\n  return filterRowModelFromRoot(rows, filterRowImpl, table)\n}\n\nfunction filterRowModelFromLeafs<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => Row<TData>[],\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    const rows: Row<TData>[] = []\n\n    // Filter from children up first\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const newRow = createRow(\n        table,\n        row.id,\n        row.original,\n        row.index,\n        row.depth,\n        undefined,\n        row.parentId\n      )\n      newRow.columnFilters = row.columnFilters\n\n      if (row.subRows?.length && depth < maxDepth) {\n        newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n        row = newRow\n\n        if (filterRow(row) && !newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n\n        if (filterRow(row) || newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n      } else {\n        row = newRow\n        if (filterRow(row)) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n        }\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n\nfunction filterRowModelFromRoot<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => any,\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  // Filters top level and nested rows\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    // Filter from parents downward first\n\n    const rows: Row<TData>[] = []\n\n    // Apply the filter to any subRows\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const pass = filterRow(row)\n\n      if (pass) {\n        if (row.subRows?.length && depth < maxDepth) {\n          const newRow = createRow(\n            table,\n            row.id,\n            row.original,\n            row.index,\n            row.depth,\n            undefined,\n            row.parentId\n          )\n          newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n          row = newRow\n        }\n\n        rows.push(row)\n        newFilteredFlatRows.push(row)\n        newFilteredRowsById[row.id] = row\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFacetedRowModel<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => RowModel<TData> {\n  return (table, columnId) =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n        table.getFilteredRowModel(),\n      ],\n      (preRowModel, columnFilters, globalFilter) => {\n        if (\n          !preRowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          return preRowModel\n        }\n\n        const filterableIds = [\n          ...columnFilters.map(d => d.id).filter(d => d !== columnId),\n          globalFilter ? '__global__' : undefined,\n        ].filter(Boolean) as string[]\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        return filterRows(preRowModel.rows, filterRowsImpl, table)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFacetedRowModel')\n    )\n}\n", "import { Table, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getFacetedUniqueValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => Map<any, number> {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return new Map()\n\n        let facetedUniqueValues = new Map<any, number>()\n\n        for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n          const values =\n            facetedRowModel.flatRows[i]!.getUniqueValues<number>(columnId)\n\n          for (let j = 0; j < values.length; j++) {\n            const value = values[j]!\n\n            if (facetedUniqueValues.has(value)) {\n              facetedUniqueValues.set(\n                value,\n                (facetedUniqueValues.get(value) ?? 0) + 1\n              )\n            } else {\n              facetedUniqueValues.set(value, 1)\n            }\n          }\n        }\n\n        return facetedUniqueValues\n      },\n      getMemoOptions(\n        table.options,\n        'debugTable',\n        `getFacetedUniqueValues_${columnId}`\n      )\n    )\n}\n", "import { ResolvedColumnFilter } from '../features/ColumnFiltering'\nimport { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFilteredRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n      ],\n      (rowModel, columnFilters, globalFilter) => {\n        if (\n          !rowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          for (let i = 0; i < rowModel.flatRows.length; i++) {\n            rowModel.flatRows[i]!.columnFilters = {}\n            rowModel.flatRows[i]!.columnFiltersMeta = {}\n          }\n          return rowModel\n        }\n\n        const resolvedColumnFilters: ResolvedColumnFilter<TData>[] = []\n        const resolvedGlobalFilters: ResolvedColumnFilter<TData>[] = []\n\n        ;(columnFilters ?? []).forEach(d => {\n          const column = table.getColumn(d.id)\n\n          if (!column) {\n            return\n          }\n\n          const filterFn = column.getFilterFn()\n\n          if (!filterFn) {\n            if (process.env.NODE_ENV !== 'production') {\n              console.warn(\n                `Could not find a valid 'column.filterFn' for column with the ID: ${column.id}.`\n              )\n            }\n            return\n          }\n\n          resolvedColumnFilters.push({\n            id: d.id,\n            filterFn,\n            resolvedValue: filterFn.resolveFilterValue?.(d.value) ?? d.value,\n          })\n        })\n\n        const filterableIds = columnFilters.map(d => d.id)\n\n        const globalFilterFn = table.getGlobalFilterFn()\n\n        const globallyFilterableColumns = table\n          .getAllLeafColumns()\n          .filter(column => column.getCanGlobalFilter())\n\n        if (\n          globalFilter &&\n          globalFilterFn &&\n          globallyFilterableColumns.length\n        ) {\n          filterableIds.push('__global__')\n\n          globallyFilterableColumns.forEach(column => {\n            resolvedGlobalFilters.push({\n              id: column.id,\n              filterFn: globalFilterFn,\n              resolvedValue:\n                globalFilterFn.resolveFilterValue?.(globalFilter) ??\n                globalFilter,\n            })\n          })\n        }\n\n        let currentColumnFilter\n        let currentGlobalFilter\n\n        // Flag the prefiltered row model with each filter state\n        for (let j = 0; j < rowModel.flatRows.length; j++) {\n          const row = rowModel.flatRows[j]!\n\n          row.columnFilters = {}\n\n          if (resolvedColumnFilters.length) {\n            for (let i = 0; i < resolvedColumnFilters.length; i++) {\n              currentColumnFilter = resolvedColumnFilters[i]!\n              const id = currentColumnFilter.id\n\n              // Tag the row with the column filter state\n              row.columnFilters[id] = currentColumnFilter.filterFn(\n                row,\n                id,\n                currentColumnFilter.resolvedValue,\n                filterMeta => {\n                  row.columnFiltersMeta[id] = filterMeta\n                }\n              )\n            }\n          }\n\n          if (resolvedGlobalFilters.length) {\n            for (let i = 0; i < resolvedGlobalFilters.length; i++) {\n              currentGlobalFilter = resolvedGlobalFilters[i]!\n              const id = currentGlobalFilter.id\n              // Tag the row with the first truthy global filter state\n              if (\n                currentGlobalFilter.filterFn(\n                  row,\n                  id,\n                  currentGlobalFilter.resolvedValue,\n                  filterMeta => {\n                    row.columnFiltersMeta[id] = filterMeta\n                  }\n                )\n              ) {\n                row.columnFilters.__global__ = true\n                break\n              }\n            }\n\n            if (row.columnFilters.__global__ !== true) {\n              row.columnFilters.__global__ = false\n            }\n          }\n        }\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        // Filter final rows using all of the active filters\n        return filterRows(rowModel.rows, filterRowsImpl, table)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFilteredRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Table, Row, RowModel, RowData } from '../types'\nimport { flattenBy, getMemoOptions, memo } from '../utils'\n\nexport function getGroupedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().grouping, table.getPreGroupedRowModel()],\n      (grouping, rowModel) => {\n        if (!rowModel.rows.length || !grouping.length) {\n          return rowModel\n        }\n\n        // Filter the grouping list down to columns that exist\n        const existingGrouping = grouping.filter(columnId =>\n          table.getColumn(columnId)\n        )\n\n        const groupedFlatRows: Row<TData>[] = []\n        const groupedRowsById: Record<string, Row<TData>> = {}\n        // const onlyGroupedFlatRows: Row[] = [];\n        // const onlyGroupedRowsById: Record<RowId, Row> = {};\n        // const nonGroupedFlatRows: Row[] = [];\n        // const nonGroupedRowsById: Record<RowId, Row> = {};\n\n        // Recursively group the data\n        const groupUpRecursively = (\n          rows: Row<TData>[],\n          depth = 0,\n          parentId?: string\n        ) => {\n          // Grouping depth has been been met\n          // Stop grouping and simply rewrite thd depth and row relationships\n          if (depth >= existingGrouping.length) {\n            return rows.map(row => {\n              row.depth = depth\n\n              groupedFlatRows.push(row)\n              groupedRowsById[row.id] = row\n\n              if (row.subRows) {\n                row.subRows = groupUpRecursively(row.subRows, depth + 1, row.id)\n              }\n\n              return row\n            })\n          }\n\n          const columnId: string = existingGrouping[depth]!\n\n          // Group the rows together for this level\n          const rowGroupsMap = groupBy(rows, columnId)\n\n          // Peform aggregations for each group\n          const aggregatedGroupedRows = Array.from(rowGroupsMap.entries()).map(\n            ([groupingValue, groupedRows], index) => {\n              let id = `${columnId}:${groupingValue}`\n              id = parentId ? `${parentId}>${id}` : id\n\n              // First, Recurse to group sub rows before aggregation\n              const subRows = groupUpRecursively(groupedRows, depth + 1, id)\n\n              // Flatten the leaf rows of the rows in this group\n              const leafRows = depth\n                ? flattenBy(groupedRows, row => row.subRows)\n                : groupedRows\n\n              const row = createRow(\n                table,\n                id,\n                leafRows[0]!.original,\n                index,\n                depth,\n                undefined,\n                parentId\n              )\n\n              Object.assign(row, {\n                groupingColumnId: columnId,\n                groupingValue,\n                subRows,\n                leafRows,\n                getValue: (columnId: string) => {\n                  // Don't aggregate columns that are in the grouping\n                  if (existingGrouping.includes(columnId)) {\n                    if (row._valuesCache.hasOwnProperty(columnId)) {\n                      return row._valuesCache[columnId]\n                    }\n\n                    if (groupedRows[0]) {\n                      row._valuesCache[columnId] =\n                        groupedRows[0].getValue(columnId) ?? undefined\n                    }\n\n                    return row._valuesCache[columnId]\n                  }\n\n                  if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n                    return row._groupingValuesCache[columnId]\n                  }\n\n                  // Aggregate the values\n                  const column = table.getColumn(columnId)\n                  const aggregateFn = column?.getAggregationFn()\n\n                  if (aggregateFn) {\n                    row._groupingValuesCache[columnId] = aggregateFn(\n                      columnId,\n                      leafRows,\n                      groupedRows\n                    )\n\n                    return row._groupingValuesCache[columnId]\n                  }\n                },\n              })\n\n              subRows.forEach(subRow => {\n                groupedFlatRows.push(subRow)\n                groupedRowsById[subRow.id] = subRow\n                // if (subRow.getIsGrouped?.()) {\n                //   onlyGroupedFlatRows.push(subRow);\n                //   onlyGroupedRowsById[subRow.id] = subRow;\n                // } else {\n                //   nonGroupedFlatRows.push(subRow);\n                //   nonGroupedRowsById[subRow.id] = subRow;\n                // }\n              })\n\n              return row\n            }\n          )\n\n          return aggregatedGroupedRows\n        }\n\n        const groupedRows = groupUpRecursively(rowModel.rows, 0)\n\n        groupedRows.forEach(subRow => {\n          groupedFlatRows.push(subRow)\n          groupedRowsById[subRow.id] = subRow\n          // if (subRow.getIsGrouped?.()) {\n          //   onlyGroupedFlatRows.push(subRow);\n          //   onlyGroupedRowsById[subRow.id] = subRow;\n          // } else {\n          //   nonGroupedFlatRows.push(subRow);\n          //   nonGroupedRowsById[subRow.id] = subRow;\n          // }\n        })\n\n        return {\n          rows: groupedRows,\n          flatRows: groupedFlatRows,\n          rowsById: groupedRowsById,\n        }\n      },\n      getMemoOptions(table.options, 'debugTable', 'getGroupedRowModel', () => {\n        table._queue(() => {\n          table._autoResetExpanded()\n          table._autoResetPageIndex()\n        })\n      })\n    )\n}\n\nfunction groupBy<TData extends RowData>(rows: Row<TData>[], columnId: string) {\n  const groupMap = new Map<any, Row<TData>[]>()\n\n  return rows.reduce((map, row) => {\n    const resKey = `${row.getGroupingValue(columnId)}`\n    const previous = map.get(resKey)\n    if (!previous) {\n      map.set(resKey, [row])\n    } else {\n      previous.push(row)\n    }\n    return map\n  }, groupMap)\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { expandRows } from './getExpandedRowModel'\n\nexport function getPaginationRowModel<TData extends RowData>(opts?: {\n  initialSync: boolean\n}): (table: Table<TData>) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().pagination,\n        table.getPrePaginationRowModel(),\n        table.options.paginateExpandedRows\n          ? undefined\n          : table.getState().expanded,\n      ],\n      (pagination, rowModel) => {\n        if (!rowModel.rows.length) {\n          return rowModel\n        }\n\n        const { pageSize, pageIndex } = pagination\n        let { rows, flatRows, rowsById } = rowModel\n        const pageStart = pageSize * pageIndex\n        const pageEnd = pageStart + pageSize\n\n        rows = rows.slice(pageStart, pageEnd)\n\n        let paginatedRowModel: RowModel<TData>\n\n        if (!table.options.paginateExpandedRows) {\n          paginatedRowModel = expandRows({\n            rows,\n            flatRows,\n            rowsById,\n          })\n        } else {\n          paginatedRowModel = {\n            rows,\n            flatRows,\n            rowsById,\n          }\n        }\n\n        paginatedRowModel.flatRows = []\n\n        const handleRow = (row: Row<TData>) => {\n          paginatedRowModel.flatRows.push(row)\n          if (row.subRows.length) {\n            row.subRows.forEach(handleRow)\n          }\n        }\n\n        paginatedRowModel.rows.forEach(handleRow)\n\n        return paginatedRowModel\n      },\n      getMemoOptions(table.options, 'debugTable', 'getPaginationRowModel')\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { SortingFn } from '../features/RowSorting'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getSortedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().sorting, table.getPreSortedRowModel()],\n      (sorting, rowModel) => {\n        if (!rowModel.rows.length || !sorting?.length) {\n          return rowModel\n        }\n\n        const sortingState = table.getState().sorting\n\n        const sortedFlatRows: Row<TData>[] = []\n\n        // Filter out sortings that correspond to non existing columns\n        const availableSorting = sortingState.filter(\n          sort => table.getColumn(sort.id)?.getCanSort()\n        )\n\n        const columnInfoById: Record<\n          string,\n          {\n            sortUndefined?: false | -1 | 1 | 'first' | 'last'\n            invertSorting?: boolean\n            sortingFn: SortingFn<TData>\n          }\n        > = {}\n\n        availableSorting.forEach(sortEntry => {\n          const column = table.getColumn(sortEntry.id)\n          if (!column) return\n\n          columnInfoById[sortEntry.id] = {\n            sortUndefined: column.columnDef.sortUndefined,\n            invertSorting: column.columnDef.invertSorting,\n            sortingFn: column.getSortingFn(),\n          }\n        })\n\n        const sortData = (rows: Row<TData>[]) => {\n          // This will also perform a stable sorting using the row index\n          // if needed.\n          const sortedData = rows.map(row => ({ ...row }))\n\n          sortedData.sort((rowA, rowB) => {\n            for (let i = 0; i < availableSorting.length; i += 1) {\n              const sortEntry = availableSorting[i]!\n              const columnInfo = columnInfoById[sortEntry.id]!\n              const sortUndefined = columnInfo.sortUndefined\n              const isDesc = sortEntry?.desc ?? false\n\n              let sortInt = 0\n\n              // All sorting ints should always return in ascending order\n              if (sortUndefined) {\n                const aValue = rowA.getValue(sortEntry.id)\n                const bValue = rowB.getValue(sortEntry.id)\n\n                const aUndefined = aValue === undefined\n                const bUndefined = bValue === undefined\n\n                if (aUndefined || bUndefined) {\n                  if (sortUndefined === 'first') return aUndefined ? -1 : 1\n                  if (sortUndefined === 'last') return aUndefined ? 1 : -1\n                  sortInt =\n                    aUndefined && bUndefined\n                      ? 0\n                      : aUndefined\n                        ? sortUndefined\n                        : -sortUndefined\n                }\n              }\n\n              if (sortInt === 0) {\n                sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id)\n              }\n\n              // If sorting is non-zero, take care of desc and inversion\n              if (sortInt !== 0) {\n                if (isDesc) {\n                  sortInt *= -1\n                }\n\n                if (columnInfo.invertSorting) {\n                  sortInt *= -1\n                }\n\n                return sortInt\n              }\n            }\n\n            return rowA.index - rowB.index\n          })\n\n          // If there are sub-rows, sort them\n          sortedData.forEach(row => {\n            sortedFlatRows.push(row)\n            if (row.subRows?.length) {\n              row.subRows = sortData(row.subRows)\n            }\n          })\n\n          return sortedData\n        }\n\n        return {\n          rows: sortData(rowModel.rows),\n          flatRows: sortedFlatRows,\n          rowsById: rowModel.rowsById,\n        }\n      },\n      getMemoOptions(table.options, 'debugTable', 'getSortedRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n"], "names": ["createColumnHelper", "accessor", "column", "accessorFn", "accessorKey", "display", "group", "functionalUpdate", "updater", "input", "noop", "makeStateUpdater", "key", "instance", "setState", "old", "isFunction", "d", "Function", "isNumberArray", "Array", "isArray", "every", "val", "flattenBy", "arr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat", "recurse", "subArr", "for<PERSON>ach", "item", "push", "children", "length", "memo", "getDeps", "fn", "opts", "deps", "result", "depArgs", "depTime", "debug", "Date", "now", "newDeps", "depsChanged", "some", "dep", "index", "resultTime", "onChange", "depEndTime", "Math", "round", "resultEndTime", "resultFpsPercentage", "pad", "str", "num", "String", "console", "info", "max", "min", "getMemoOptions", "tableOptions", "debugLevel", "_tableOptions$debugAl", "debugAll", "process", "env", "NODE_ENV", "createCell", "table", "row", "columnId", "getRenderValue", "_cell$getValue", "cell", "getValue", "options", "renderFallbackValue", "id", "renderValue", "getContext", "_features", "feature", "createColumn", "columnDef", "depth", "parent", "_ref", "_resolvedColumnDef$id", "defaultColumn", "_getDefaultColumnDef", "resolvedColumnDef", "replace", "undefined", "header", "includes", "originalRow", "split", "_result", "warn", "Error", "columns", "getFlatColumns", "_column$columns", "flatMap", "getLeafColumns", "_getOrderColumnsFn", "orderColumns", "_column$columns2", "leafColumns", "createHeader", "_options$id", "isPlaceholder", "placeholderId", "subHeaders", "colSpan", "rowSpan", "headerGroup", "getLeafHeaders", "leafHeaders", "recurse<PERSON><PERSON><PERSON>", "h", "map", "Headers", "createTable", "getHeaderGroups", "getAllColumns", "getVisibleLeafColumns", "getState", "columnPinning", "left", "right", "allColumns", "_left$map$filter", "_right$map$filter", "leftColumns", "find", "filter", "Boolean", "rightColumns", "centerColumns", "headerGroups", "buildHeaderGroups", "getCenterHeaderGroups", "getLeftHeaderGroups", "_left$map$filter2", "orderedLeafColumns", "getRightHeaderGroups", "_right$map$filter2", "getFooterGroups", "reverse", "getLeftFooterGroups", "getCenterFooterGroups", "getRightFooterGroups", "getFlatHeaders", "headers", "getLeftFlatHeaders", "getCenterFlatHeaders", "getRightFlatHeaders", "getCenterLeafHeaders", "flatHeaders", "_header$subHeaders", "getLeftLeafHeaders", "_header$subHeaders2", "getRightLeafHeaders", "_header$subHeaders3", "center", "_left$0$headers", "_left$", "_center$0$headers", "_center$", "_right$0$headers", "_right$", "columnsToGroup", "headerFamily", "_headerGroups$0$heade", "_headerGroups$", "max<PERSON><PERSON><PERSON>", "findMaxDepth", "getIsVisible", "createHeaderGroup", "headersToGroup", "join", "pendingParentHeaders", "headerToGroup", "latestPendingParentHeader", "isLeafHeader", "bottomHeaders", "recurseHeadersForSpans", "filteredHeaders", "childRowSpans", "childColSpan", "childRowSpan", "minChildRowSpan", "createRow", "original", "rowIndex", "subRows", "parentId", "_valuesCache", "_uniqueValuesCache", "hasOwnProperty", "getColumn", "getUniqueValues", "_row$getValue", "getLeafRows", "getParentRow", "getRow", "getParentRows", "parentRows", "currentRow", "parentRow", "getAllCells", "getAllLeafColumns", "_getAllCellsByColumnId", "allCells", "reduce", "acc", "i", "ColumnFaceting", "_getFacetedRowModel", "getFacetedRowModel", "getPreFilteredRowModel", "_getFacetedUniqueValues", "getFacetedUniqueValues", "Map", "_getFacetedMinMaxValues", "getFacetedMinMaxValues", "includesString", "filterValue", "search", "toLowerCase", "toString", "autoRemove", "<PERSON><PERSON><PERSON><PERSON>", "includesStringSensitive", "_row$getValue2", "equalsString", "_row$getValue3", "arrIncludes", "_row$getValue4", "arrIncludesAll", "_row$getValue5", "arrIncludesSome", "_row$getValue6", "equals", "weakEquals", "inNumberRange", "rowValue", "resolveFilterValue", "unsafeMin", "unsafeMax", "parsedMin", "parseFloat", "parsedMax", "Number", "isNaN", "Infinity", "temp", "filterFns", "ColumnFiltering", "getDefaultColumnDef", "filterFn", "getInitialState", "state", "columnFilters", "getDefaultOptions", "onColumnFiltersChange", "filterFromLeafRows", "maxLeafRowFilterDepth", "getAutoFilterFn", "firstRow", "getCoreRowModel", "flatRows", "value", "getFilterFn", "_table$options$filter", "_table$options$filter2", "getCanFilter", "_column$columnDef$ena", "_table$options$enable", "_table$options$enable2", "enableColumnFilter", "enableColumnFilters", "enableFilters", "getIsFiltered", "getFilterIndex", "getFilterValue", "_table$getState$colum", "_table$getState$colum2", "_table$getState$colum3", "findIndex", "setFilterValue", "setColumnFilters", "previousFilter", "newFilter", "shouldAutoRemoveFilter", "_old$filter", "newFilterObj", "_old$map", "_table", "columnFiltersMeta", "updateFn", "_functionalUpdate", "resetColumnFilters", "defaultState", "_table$initialState$c", "_table$initialState", "initialState", "getFilteredRowModel", "_getFilteredRowModel", "manualFiltering", "sum", "_leafRows", "childRows", "next", "nextValue", "extent", "mean", "leafRows", "count", "median", "values", "mid", "floor", "nums", "sort", "a", "b", "unique", "from", "Set", "uniqueCount", "size", "_columnId", "aggregationFns", "ColumnGrouping", "aggregatedCell", "props", "_toString", "_props$getValue", "aggregationFn", "grouping", "onGroupingChange", "groupedColumnMode", "toggleGrouping", "setGrouping", "getCanGroup", "enableGrouping", "getGroupingValue", "getIsGrouped", "_table$getState$group", "getGroupedIndex", "_table$getState$group2", "indexOf", "getToggleGroupingHandler", "canGroup", "getAutoAggregationFn", "Object", "prototype", "call", "getAggregationFn", "_table$options$aggreg", "_table$options$aggreg2", "resetGrouping", "_table$initialState$g", "getPreGroupedRowModel", "getGroupedRowModel", "_getGroupedRowModel", "manualGrouping", "groupingColumnId", "_groupingValuesCache", "getIsPlaceholder", "getIsAggregated", "_row$subRows", "nonGroupingColumns", "col", "groupingColumns", "g", "ColumnOrdering", "columnOrder", "onColumnOrderChange", "getIndex", "position", "_getVisibleLeafColumns", "getIsFirstColumn", "_columns$", "getIsLastColumn", "_columns", "setColumnOrder", "resetColumnOrder", "orderedColumns", "columnOrderCopy", "columnsCopy", "targetColumnId", "shift", "foundIndex", "splice", "getDefaultColumnPinningState", "ColumnPinning", "onColumnPinningChange", "pin", "columnIds", "setColumnPinning", "_old$left3", "_old$right3", "_old$left", "_old$right", "_old$left2", "_old$right2", "getCanPin", "_d$columnDef$enablePi", "enablePinning", "enableColumnPinning", "getIsPinned", "leafColumnIds", "isLeft", "isRight", "getPinnedIndex", "getCenterVisibleCells", "_getAllVisibleCells", "leftAndRight", "getLeftVisibleCells", "cells", "getRightVisibleCells", "resetColumnPinning", "getIsSomeColumnsPinned", "_pinningState$positio", "pinningState", "_pinningState$left", "_pinningState$right", "getLeftLeafColumns", "getRightLeafColumns", "getCenterLeafColumns", "defaultColumnSizing", "minSize", "maxSize", "MAX_SAFE_INTEGER", "getDefaultColumnSizingInfoState", "startOffset", "startSize", "deltaOffset", "deltaPercentage", "isResizingColumn", "columnSizingStart", "ColumnSizing", "columnSizing", "columnSizingInfo", "columnResizeMode", "columnResizeDirection", "onColumnSizingChange", "onColumnSizingInfoChange", "getSize", "_column$columnDef$min", "_column$columnDef$max", "columnSize", "getStart", "slice", "getAfter", "resetSize", "setColumnSizing", "_ref2", "_", "rest", "getCanResize", "enableResizing", "enableColumnResizing", "getIsResizing", "_header$column$getSiz", "prevSiblingHeader", "getResizeHandler", "_contextDocument", "canResize", "e", "persist", "isTouchStartEvent", "touches", "clientX", "newColumnSizing", "updateOffset", "eventType", "clientXPos", "setColumnSizingInfo", "_old$startOffset", "_old$startSize", "deltaDirection", "_ref3", "headerSize", "onMove", "onEnd", "contextDocument", "document", "mouseEvents", "<PERSON><PERSON><PERSON><PERSON>", "up<PERSON><PERSON><PERSON>", "removeEventListener", "touchEvents", "cancelable", "preventDefault", "stopPropagation", "_e$touches$", "passiveIfSupported", "passiveEventSupported", "passive", "addEventListener", "resetColumnSizing", "resetHeaderSizeInfo", "_table$initialState$c2", "getTotalSize", "_table$getHeaderGroup", "_table$getHeaderGroup2", "getLeftTotalSize", "_table$getLeftHeaderG", "_table$getLeftHeaderG2", "getCenterTotalSize", "_table$getCenterHeade", "_table$getCenterHeade2", "getRightTotalSize", "_table$getRightHeader", "_table$getRightHeader2", "passiveSupported", "supported", "window", "err", "type", "ColumnVisibility", "columnVisibility", "onColumnVisibilityChange", "toggleVisibility", "getCanHide", "setColumnVisibility", "childColumns", "c", "enableHiding", "getToggleVisibilityHandler", "target", "checked", "getVisibleCells", "makeVisibleColumnsMethod", "getColumns", "getVisibleFlatColumns", "getAllFlatColumns", "getLeftVisibleLeafColumns", "getRightVisibleLeafColumns", "getCenterVisibleLeafColumns", "resetColumnVisibility", "toggleAllColumnsVisible", "_value", "getIsAllColumnsVisible", "obj", "getIsSomeColumnsVisible", "getToggleAllColumnsVisibilityHandler", "_target", "GlobalFaceting", "_getGlobalFacetedRowModel", "getGlobalFacetedRowModel", "_getGlobalFacetedUniqueValues", "getGlobalFacetedUniqueValues", "_getGlobalFacetedMinMaxValues", "getGlobalFacetedMinMaxValues", "GlobalFiltering", "globalFilter", "onGlobalFilterChange", "globalFilterFn", "getColumnCanGlobalFilter", "_table$getCoreRowMode", "getCanGlobalFilter", "_table$options$getCol", "enableGlobalFilter", "getGlobalAutoFilterFn", "getGlobalFilterFn", "setGlobalFilter", "resetGlobalFilter", "RowExpanding", "expanded", "onExpandedChange", "paginateExpandedRows", "registered", "queued", "_autoResetExpanded", "_table$options$autoRe", "_queue", "autoResetAll", "autoResetExpanded", "manualExpanding", "resetExpanded", "setExpanded", "toggleAllRowsExpanded", "getIsAllRowsExpanded", "_table$initialState$e", "getCanSomeRowsExpand", "getPrePaginationRowModel", "getCanExpand", "getToggleAllRowsExpandedHandler", "getIsSomeRowsExpanded", "keys", "getRowModel", "getIsExpanded", "getExpandedDepth", "rowIds", "rowsById", "splitId", "getPreExpandedRowModel", "getSortedRowModel", "getExpandedRowModel", "_getExpandedRowModel", "toggleExpanded", "_expanded", "exists", "oldExpanded", "rowId", "_table$options$getIsR", "getIsRowExpanded", "_table$options$getRow", "getRowCanExpand", "enableExpanding", "getIsAllParentsExpanded", "isFullyExpanded", "getToggleExpandedHandler", "canExpand", "defaultPageIndex", "defaultPageSize", "getDefaultPaginationState", "pageIndex", "pageSize", "RowPagination", "pagination", "onPaginationChange", "_autoResetPageIndex", "autoResetPageIndex", "manualPagination", "resetPageIndex", "setPagination", "safeUpdater", "newState", "resetPagination", "_table$initialState$p", "setPageIndex", "maxPageIndex", "pageCount", "_table$initialState$p2", "resetPageSize", "_table$initialState$p3", "_table$initialState2", "setPageSize", "topRowIndex", "setPageCount", "_table$options$pageCo", "newPageCount", "getPageOptions", "getPageCount", "pageOptions", "fill", "getCanPreviousPage", "getCanNextPage", "previousPage", "nextPage", "firstPage", "lastPage", "getPaginationRowModel", "_getPaginationRowModel", "_table$options$pageCo2", "ceil", "getRowCount", "_table$options$rowCou", "rowCount", "rows", "getDefaultRowPinningState", "top", "bottom", "RowPinning", "rowPinning", "onRowPinningChange", "includeLeafRows", "includeParentRows", "leafRowIds", "parentRowIds", "setRowPinning", "_old$top3", "_old$bottom3", "_old$top", "_old$bottom", "has", "_old$top2", "_old$bottom2", "enableRowPinning", "isTop", "isBottom", "_table$_getPinnedRows", "_visiblePinnedRowIds$", "visiblePinnedRowIds", "_getPinnedRows", "_ref4", "resetRowPinning", "_table$initialState$r", "getIsSomeRowsPinned", "_pinningState$top", "_pinningState$bottom", "visibleRows", "pinnedRowIds", "_table$options$keepPi", "keepPinnedRows", "getTopRows", "getBottomRows", "getCenterRows", "allRows", "topAndBottom", "RowSelection", "rowSelection", "onRowSelectionChange", "enableRowSelection", "enableMultiRowSelection", "enableSubRowSelection", "setRowSelection", "resetRowSelection", "toggleAllRowsSelected", "getIsAllRowsSelected", "preGroupedFlatRows", "getCanSelect", "toggleAllPageRowsSelected", "resolvedValue", "getIsAllPageRowsSelected", "mutateRowIsSelected", "getPreSelectedRowModel", "getSelectedRowModel", "rowModel", "selectRowsFn", "getFilteredSelectedRowModel", "getGroupedSelectedRowModel", "isAllRowsSelected", "paginationFlatRows", "isAllPageRowsSelected", "getIsSomeRowsSelected", "_table$getState$rowSe", "totalSelected", "getIsSomePageRowsSelected", "getIsSelected", "getIsSomeSelected", "getToggleAllRowsSelectedHandler", "getToggleAllPageRowsSelectedHandler", "toggleSelected", "isSelected", "_opts$selectChildren", "selectedRowIds", "select<PERSON><PERSON><PERSON><PERSON>", "isRowSelected", "isSubRowSelected", "getIsAllSubRowsSelected", "getCanSelectSubRows", "getCanMultiSelect", "_table$options$enable3", "getToggleSelectedHandler", "canSelect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newSelectedFlatRows", "newSelectedRowsById", "recurseRows", "_row$subRows2", "selection", "_selection$row$id", "_row$subRows3", "allChildrenSelected", "someSelected", "subRow", "subRowChildrenSelected", "reSplitAlphaNumeric", "alphanumeric", "rowA", "rowB", "compareAlphanumeric", "alphanumericCaseSensitive", "text", "compareBasic", "textCaseSensitive", "datetime", "basic", "aStr", "bStr", "aa", "bb", "an", "parseInt", "bn", "combo", "sortingFns", "RowSorting", "sorting", "sortingFn", "sortUndefined", "onSortingChange", "isMultiSortEvent", "shift<PERSON>ey", "getAutoSortingFn", "firstRows", "isString", "getAutoSortDir", "getSortingFn", "_table$options$sortin", "_table$options$sortin2", "toggleSorting", "desc", "multi", "nextSortingOrder", "getNextSortingOrder", "hasManual<PERSON><PERSON>ue", "setSorting", "existingSorting", "existingIndex", "newSorting", "sortAction", "nextDesc", "getCanMultiSort", "_table$options$maxMul", "maxMultiSortColCount", "getFirstSortDir", "_column$columnDef$sor", "sortDescFirst", "firstSortDirection", "isSorted", "getIsSorted", "enableSortingRemoval", "enableMultiRemove", "getCanSort", "enableSorting", "_column$columnDef$ena2", "enableMultiSort", "_table$getState$sorti", "columnSort", "getSortIndex", "_table$getState$sorti2", "_table$getState$sorti3", "clearSorting", "getToggleSortingHandler", "canSort", "resetSorting", "_table$initialState$s", "getPreSortedRowModel", "_getSortedRowModel", "manualSorting", "builtInFeatures", "_options$_features", "_options$initialState", "debugTable", "defaultOptions", "assign", "mergeOptions", "coreInitialState", "_feature$getInitialSt", "queuedTimeout", "coreInstance", "cb", "Promise", "resolve", "then", "catch", "error", "setTimeout", "reset", "setOptions", "newOptions", "onStateChange", "_getRowId", "getRowId", "_getCoreRowModel", "searchAll", "_defaultColumn", "_props$renderValue$to", "_props$renderValue", "_getColumnDefs", "columnDefs", "recurseColumns", "groupingColumnDef", "_getAllFlatColumnsById", "flatColumns", "data", "accessRows", "originalRows", "getSubRows", "_row$originalSubRows", "originalSubRows", "expandRows", "expandedRows", "handleRow", "_table$getColumn", "facetedRowModel", "_facetedRowModel$flat", "firstValue", "facetedMinMaxValues", "j", "filterRows", "filterRowImpl", "filterRowModelFromLeafs", "filterRowModelFromRoot", "rowsToFilter", "filterRow", "_table$options$maxLea", "newFilteredFlatRows", "newFilteredRowsById", "recurseFilterRows", "newRow", "_table$options$maxLea2", "pass", "preRowModel", "filterableIds", "filterRowsImpl", "facetedUniqueValues", "_facetedUniqueValues$", "set", "get", "resolvedColumnFilters", "resolvedGlobalFilters", "_filterFn$resolveFilt", "globallyFilterableColumns", "_globalFilterFn$resol", "currentColumnFilter", "currentGlobalFilter", "filterMeta", "__global__", "existingGrouping", "groupedFlatRows", "groupedRowsById", "groupUpRecursively", "rowGroupsMap", "groupBy", "aggregatedGroupedRows", "entries", "groupingValue", "groupedRows", "_groupedRows$0$getVal", "aggregateFn", "groupMap", "res<PERSON>ey", "previous", "pageStart", "pageEnd", "paginatedRowModel", "sortingState", "sortedFlatRows", "availableSorting", "columnInfoById", "sortEntry", "invertSorting", "sortData", "sortedData", "_sortEntry$desc", "columnInfo", "isDesc", "sortInt", "aValue", "bValue", "aUndefined", "bUndefined"], "mappings": ";;;;;;;;;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAsBO,SAASA,kBAAkBA,GAET;EACvB,OAAO;AACLC,IAAAA,QAAQ,EAAEA,CAACA,QAAQ,EAAEC,MAAM,KAAK;AAC9B,MAAA,OAAO,OAAOD,QAAQ,KAAK,UAAU,GAChC;AACC,QAAA,GAAGC,MAAM;AACTC,QAAAA,UAAU,EAAEF,QAAAA;AACd,OAAC,GACD;AACE,QAAA,GAAGC,MAAM;AACTE,QAAAA,WAAW,EAAEH,QAAAA;OACd,CAAA;KACN;IACDI,OAAO,EAAEH,MAAM,IAAIA,MAAM;IACzBI,KAAK,EAAEJ,MAAM,IAAIA,MAAAA;GAClB,CAAA;AACH;;AC9DA;;AAOA;;AA0CA;;AAEO,SAASK,gBAAgBA,CAAIC,OAAmB,EAAEC,KAAQ,EAAK;EACpE,OAAO,OAAOD,OAAO,KAAK,UAAU,GAC/BA,OAAO,CAAqBC,KAAK,CAAC,GACnCD,OAAO,CAAA;AACb,CAAA;AAEO,SAASE,IAAIA,GAAG;AACrB;AAAA,CAAA;AAGK,SAASC,gBAAgBA,CAC9BC,GAAM,EACNC,QAAiB,EACjB;AACA,EAAA,OAAQL,OAA+B,IAAK;AACxCK,IAAAA,QAAQ,CAASC,QAAQ,CAAeC,GAAgB,IAAK;MAC7D,OAAO;AACL,QAAA,GAAGA,GAAG;QACN,CAACH,GAAG,GAAGL,gBAAgB,CAACC,OAAO,EAAGO,GAAG,CAASH,GAAG,CAAC,CAAA;OACnD,CAAA;AACH,KAAC,CAAC,CAAA;GACH,CAAA;AACH,CAAA;AAIO,SAASI,UAAUA,CAAwBC,CAAM,EAAU;EAChE,OAAOA,CAAC,YAAYC,QAAQ,CAAA;AAC9B,CAAA;AAEO,SAASC,aAAaA,CAACF,CAAM,EAAiB;AACnD,EAAA,OAAOG,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,IAAIA,CAAC,CAACK,KAAK,CAACC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,CAAC,CAAA;AACpE,CAAA;AAEO,SAASC,SAASA,CACvBC,GAAY,EACZC,WAAqC,EACrC;EACA,MAAMC,IAAa,GAAG,EAAE,CAAA;EAExB,MAAMC,OAAO,GAAIC,MAAe,IAAK;AACnCA,IAAAA,MAAM,CAACC,OAAO,CAACC,IAAI,IAAI;AACrBJ,MAAAA,IAAI,CAACK,IAAI,CAACD,IAAI,CAAC,CAAA;AACf,MAAA,MAAME,QAAQ,GAAGP,WAAW,CAACK,IAAI,CAAC,CAAA;AAClC,MAAA,IAAIE,QAAQ,IAAA,IAAA,IAARA,QAAQ,CAAEC,MAAM,EAAE;QACpBN,OAAO,CAACK,QAAQ,CAAC,CAAA;AACnB,OAAA;AACF,KAAC,CAAC,CAAA;GACH,CAAA;EAEDL,OAAO,CAACH,GAAG,CAAC,CAAA;AAEZ,EAAA,OAAOE,IAAI,CAAA;AACb,CAAA;AAEO,SAASQ,IAAIA,CAClBC,OAA2C,EAC3CC,EAA6C,EAC7CC,IAIC,EACgC;EACjC,IAAIC,IAAW,GAAG,EAAE,CAAA;AACpB,EAAA,IAAIC,MAA2B,CAAA;AAE/B,EAAA,OAAOC,OAAO,IAAI;AAChB,IAAA,IAAIC,OAAe,CAAA;AACnB,IAAA,IAAIJ,IAAI,CAAC1B,GAAG,IAAI0B,IAAI,CAACK,KAAK,EAAED,OAAO,GAAGE,IAAI,CAACC,GAAG,EAAE,CAAA;AAEhD,IAAA,MAAMC,OAAO,GAAGV,OAAO,CAACK,OAAO,CAAC,CAAA;IAEhC,MAAMM,WAAW,GACfD,OAAO,CAACZ,MAAM,KAAKK,IAAI,CAACL,MAAM,IAC9BY,OAAO,CAACE,IAAI,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAKX,IAAI,CAACW,KAAK,CAAC,KAAKD,GAAG,CAAC,CAAA;IAEhE,IAAI,CAACF,WAAW,EAAE;AAChB,MAAA,OAAOP,MAAM,CAAA;AACf,KAAA;AAEAD,IAAAA,IAAI,GAAGO,OAAO,CAAA;AAEd,IAAA,IAAIK,UAAkB,CAAA;AACtB,IAAA,IAAIb,IAAI,CAAC1B,GAAG,IAAI0B,IAAI,CAACK,KAAK,EAAEQ,UAAU,GAAGP,IAAI,CAACC,GAAG,EAAE,CAAA;AAEnDL,IAAAA,MAAM,GAAGH,EAAE,CAAC,GAAGS,OAAO,CAAC,CAAA;IACvBR,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEc,QAAQ,IAAA,IAAA,IAAdd,IAAI,CAAEc,QAAQ,CAAGZ,MAAM,CAAC,CAAA;AAExB,IAAA,IAAIF,IAAI,CAAC1B,GAAG,IAAI0B,IAAI,CAACK,KAAK,EAAE;AAC1B,MAAA,IAAIL,IAAI,IAAJA,IAAAA,IAAAA,IAAI,CAAEK,KAAK,EAAE,EAAE;AACjB,QAAA,MAAMU,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACX,IAAI,CAACC,GAAG,EAAE,GAAGH,OAAQ,IAAI,GAAG,CAAC,GAAG,GAAG,CAAA;AAClE,QAAA,MAAMc,aAAa,GAAGF,IAAI,CAACC,KAAK,CAAC,CAACX,IAAI,CAACC,GAAG,EAAE,GAAGM,UAAW,IAAI,GAAG,CAAC,GAAG,GAAG,CAAA;AACxE,QAAA,MAAMM,mBAAmB,GAAGD,aAAa,GAAG,EAAE,CAAA;AAE9C,QAAA,MAAME,GAAG,GAAGA,CAACC,GAAoB,EAAEC,GAAW,KAAK;AACjDD,UAAAA,GAAG,GAAGE,MAAM,CAACF,GAAG,CAAC,CAAA;AACjB,UAAA,OAAOA,GAAG,CAACzB,MAAM,GAAG0B,GAAG,EAAE;YACvBD,GAAG,GAAG,GAAG,GAAGA,GAAG,CAAA;AACjB,WAAA;AACA,UAAA,OAAOA,GAAG,CAAA;SACX,CAAA;AAEDG,QAAAA,OAAO,CAACC,IAAI,CACT,OAAML,GAAG,CAACF,aAAa,EAAE,CAAC,CAAE,CAAA,EAAA,EAAIE,GAAG,CAACL,UAAU,EAAE,CAAC,CAAE,KAAI,EACvD,CAAA;AACX;AACA;AACA,uBAAyBC,EAAAA,IAAI,CAACU,GAAG,CACnB,CAAC,EACDV,IAAI,CAACW,GAAG,CAAC,GAAG,GAAG,GAAG,GAAGR,mBAAmB,EAAE,GAAG,CAC/C,CAAE,CAAA,cAAA,CAAe,EACnBnB,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAE1B,GACR,CAAC,CAAA;AACH,OAAA;AACF,KAAA;AAEA,IAAA,OAAO4B,MAAM,CAAA;GACd,CAAA;AACH,CAAA;AAEO,SAAS0B,cAAcA,CAC5BC,YAAgD,EAChDC,UAMkB,EAClBxD,GAAW,EACXwC,QAAgC,EAChC;EACA,OAAO;AACLT,IAAAA,KAAK,EAAEA,MAAA;AAAA,MAAA,IAAA0B,qBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,qBAAA,GAAMF,YAAY,IAAA,IAAA,GAAA,KAAA,CAAA,GAAZA,YAAY,CAAEG,QAAQ,KAAA,IAAA,GAAAD,qBAAA,GAAIF,YAAY,CAACC,UAAU,CAAC,CAAA;AAAA,KAAA;IAC/DxD,GAAG,EAAE2D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI7D,GAAG;AAClDwC,IAAAA,QAAAA;GACD,CAAA;AACH;;ACtKO,SAASsB,UAAUA,CACxBC,KAAmB,EACnBC,GAAe,EACf1E,MAA6B,EAC7B2E,QAAgB,EACK;EACrB,MAAMC,cAAc,GAAGA,MAAA;AAAA,IAAA,IAAAC,cAAA,CAAA;AAAA,IAAA,OAAA,CAAAA,cAAA,GACrBC,IAAI,CAACC,QAAQ,EAAE,KAAAF,IAAAA,GAAAA,cAAA,GAAIJ,KAAK,CAACO,OAAO,CAACC,mBAAmB,CAAA;AAAA,GAAA,CAAA;AAEtD,EAAA,MAAMH,IAA6B,GAAG;IACpCI,EAAE,EAAG,GAAER,GAAG,CAACQ,EAAG,CAAGlF,CAAAA,EAAAA,MAAM,CAACkF,EAAG,CAAC,CAAA;IAC5BR,GAAG;IACH1E,MAAM;IACN+E,QAAQ,EAAEA,MAAML,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC;AACtCQ,IAAAA,WAAW,EAAEP,cAAc;IAC3BQ,UAAU,EAAEnD,IAAI,CACd,MAAM,CAACwC,KAAK,EAAEzE,MAAM,EAAE0E,GAAG,EAAEI,IAAI,CAAC,EAChC,CAACL,KAAK,EAAEzE,MAAM,EAAE0E,GAAG,EAAEI,IAAI,MAAM;MAC7BL,KAAK;MACLzE,MAAM;MACN0E,GAAG;AACHI,MAAAA,IAAI,EAAEA,IAA2B;MACjCC,QAAQ,EAAED,IAAI,CAACC,QAAQ;MACvBI,WAAW,EAAEL,IAAI,CAACK,WAAAA;KACnB,CAAC,EACFnB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,iBAAiB,CAC/D,CAAA;GACD,CAAA;AAEDP,EAAAA,KAAK,CAACY,SAAS,CAACzD,OAAO,CAAC0D,OAAO,IAAI;AACjCA,IAAAA,OAAO,CAACd,UAAU,IAAlBc,IAAAA,IAAAA,OAAO,CAACd,UAAU,CAChBM,IAAI,EACJ9E,MAAM,EACN0E,GAAG,EACHD,KACF,CAAC,CAAA;GACF,EAAE,EAAE,CAAC,CAAA;AAEN,EAAA,OAAOK,IAAI,CAAA;AACb;;AC1BO,SAASS,YAAYA,CAC1Bd,KAAmB,EACnBe,SAAmC,EACnCC,KAAa,EACbC,MAA8B,EACP;EAAA,IAAAC,IAAA,EAAAC,qBAAA,CAAA;AACvB,EAAA,MAAMC,aAAa,GAAGpB,KAAK,CAACqB,oBAAoB,EAAE,CAAA;AAElD,EAAA,MAAMC,iBAAiB,GAAG;AACxB,IAAA,GAAGF,aAAa;IAChB,GAAGL,SAAAA;GACwB,CAAA;AAE7B,EAAA,MAAMtF,WAAW,GAAG6F,iBAAiB,CAAC7F,WAAW,CAAA;AAEjD,EAAA,IAAIgF,EAAE,GAAAS,CAAAA,IAAA,GAAAC,CAAAA,qBAAA,GACJG,iBAAiB,CAACb,EAAE,KAAA,IAAA,GAAAU,qBAAA,GACnB1F,WAAW,GAAGA,WAAW,CAAC8F,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAGC,SAAS,KAAAN,IAAAA,GAAAA,IAAA,GACvD,OAAOI,iBAAiB,CAACG,MAAM,KAAK,QAAQ,GACzCH,iBAAiB,CAACG,MAAM,GACxBD,SAAU,CAAA;AAEhB,EAAA,IAAIhG,UAAyC,CAAA;EAE7C,IAAI8F,iBAAiB,CAAC9F,UAAU,EAAE;IAChCA,UAAU,GAAG8F,iBAAiB,CAAC9F,UAAU,CAAA;GAC1C,MAAM,IAAIC,WAAW,EAAE;AACtB;AACA,IAAA,IAAIA,WAAW,CAACiG,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7BlG,UAAU,GAAImG,WAAkB,IAAK;QACnC,IAAI9D,MAAM,GAAG8D,WAAkC,CAAA;QAE/C,KAAK,MAAM1F,GAAG,IAAIR,WAAW,CAACmG,KAAK,CAAC,GAAG,CAAC,EAAE;AAAA,UAAA,IAAAC,OAAA,CAAA;UACxChE,MAAM,GAAA,CAAAgE,OAAA,GAAGhE,MAAM,qBAANgE,OAAA,CAAS5F,GAAG,CAAC,CAAA;UACtB,IAAI2D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIjC,MAAM,KAAK2D,SAAS,EAAE;YACjErC,OAAO,CAAC2C,IAAI,CACT,CAAA,CAAA,EAAG7F,GAAI,CAA0BR,wBAAAA,EAAAA,WAAY,uBAChD,CAAC,CAAA;AACH,WAAA;AACF,SAAA;AAEA,QAAA,OAAOoC,MAAM,CAAA;OACd,CAAA;AACH,KAAC,MAAM;MACLrC,UAAU,GAAImG,WAAkB,IAC7BA,WAAW,CAASL,iBAAiB,CAAC7F,WAAW,CAAC,CAAA;AACvD,KAAA;AACF,GAAA;EAEA,IAAI,CAACgF,EAAE,EAAE;AACP,IAAA,IAAIb,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAM,IAAIiC,KAAK,CACbT,iBAAiB,CAAC9F,UAAU,GACvB,CAAA,8CAAA,CAA+C,GAC/C,CAAA,oDAAA,CACP,CAAC,CAAA;AACH,KAAA;IACA,MAAM,IAAIuG,KAAK,EAAE,CAAA;AACnB,GAAA;AAEA,EAAA,IAAIxG,MAA8B,GAAG;AACnCkF,IAAAA,EAAE,EAAG,CAAEvB,EAAAA,MAAM,CAACuB,EAAE,CAAE,CAAC,CAAA;IACnBjF,UAAU;AACVyF,IAAAA,MAAM,EAAEA,MAAa;IACrBD,KAAK;AACLD,IAAAA,SAAS,EAAEO,iBAA0C;AACrDU,IAAAA,OAAO,EAAE,EAAE;IACXC,cAAc,EAAEzE,IAAI,CAClB,MAAM,CAAC,IAAI,CAAC,EACZ,MAAM;AAAA,MAAA,IAAA0E,eAAA,CAAA;MACJ,OAAO,CACL3G,MAAM,EACN,IAAA,CAAA2G,eAAA,GAAG3G,MAAM,CAACyG,OAAO,KAAdE,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,eAAA,CAAgBC,OAAO,CAAC7F,CAAC,IAAIA,CAAC,CAAC2F,cAAc,EAAE,CAAC,EACpD,CAAA;KACF,EACD1C,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,uBAAuB,CACvE,CAAC;AACD6B,IAAAA,cAAc,EAAE5E,IAAI,CAClB,MAAM,CAACwC,KAAK,CAACqC,kBAAkB,EAAE,CAAC,EAClCC,YAAY,IAAI;AAAA,MAAA,IAAAC,gBAAA,CAAA;MACd,IAAAA,CAAAA,gBAAA,GAAIhH,MAAM,CAACyG,OAAO,KAAdO,IAAAA,IAAAA,gBAAA,CAAgBhF,MAAM,EAAE;AAC1B,QAAA,IAAIiF,WAAW,GAAGjH,MAAM,CAACyG,OAAO,CAACG,OAAO,CAAC5G,MAAM,IAC7CA,MAAM,CAAC6G,cAAc,EACvB,CAAC,CAAA;QAED,OAAOE,YAAY,CAACE,WAAW,CAAC,CAAA;AAClC,OAAA;MAEA,OAAO,CAACjH,MAAM,CAA0B,CAAA;KACzC,EACDgE,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,uBAAuB,CACvE,CAAA;GACD,CAAA;AAED,EAAA,KAAK,MAAMM,OAAO,IAAIb,KAAK,CAACY,SAAS,EAAE;IACrCC,OAAO,CAACC,YAAY,IAAA,IAAA,IAApBD,OAAO,CAACC,YAAY,CAAGvF,MAAM,EAA2ByE,KAAK,CAAC,CAAA;AAChE,GAAA;;AAEA;AACA,EAAA,OAAOzE,MAAM,CAAA;AACf;;AC1JA,MAAMyC,KAAK,GAAG,cAAc,CAAA;AAwM5B;;AAEA,SAASyE,YAAYA,CACnBzC,KAAmB,EACnBzE,MAA6B,EAC7BgF,OAMC,EACsB;AAAA,EAAA,IAAAmC,WAAA,CAAA;AACvB,EAAA,MAAMjC,EAAE,GAAA,CAAAiC,WAAA,GAAGnC,OAAO,CAACE,EAAE,KAAA,IAAA,GAAAiC,WAAA,GAAInH,MAAM,CAACkF,EAAE,CAAA;AAElC,EAAA,IAAIgB,MAAiC,GAAG;IACtChB,EAAE;IACFlF,MAAM;IACNgD,KAAK,EAAEgC,OAAO,CAAChC,KAAK;AACpBoE,IAAAA,aAAa,EAAE,CAAC,CAACpC,OAAO,CAACoC,aAAa;IACtCC,aAAa,EAAErC,OAAO,CAACqC,aAAa;IACpC5B,KAAK,EAAET,OAAO,CAACS,KAAK;AACpB6B,IAAAA,UAAU,EAAE,EAAE;AACdC,IAAAA,OAAO,EAAE,CAAC;AACVC,IAAAA,OAAO,EAAE,CAAC;AACVC,IAAAA,WAAW,EAAE,IAAK;IAClBC,cAAc,EAAEA,MAAgC;MAC9C,MAAMC,WAAqC,GAAG,EAAE,CAAA;MAEhD,MAAMC,aAAa,GAAIC,CAAyB,IAAK;QACnD,IAAIA,CAAC,CAACP,UAAU,IAAIO,CAAC,CAACP,UAAU,CAACtF,MAAM,EAAE;AACvC6F,UAAAA,CAAC,CAACP,UAAU,CAACQ,GAAG,CAACF,aAAa,CAAC,CAAA;AACjC,SAAA;AACAD,QAAAA,WAAW,CAAC7F,IAAI,CAAC+F,CAA2B,CAAC,CAAA;OAC9C,CAAA;MAEDD,aAAa,CAAC1B,MAAM,CAAC,CAAA;AAErB,MAAA,OAAOyB,WAAW,CAAA;KACnB;IACDvC,UAAU,EAAEA,OAAO;MACjBX,KAAK;AACLyB,MAAAA,MAAM,EAAEA,MAA+B;AACvClG,MAAAA,MAAAA;KACD,CAAA;GACF,CAAA;AAEDyE,EAAAA,KAAK,CAACY,SAAS,CAACzD,OAAO,CAAC0D,OAAO,IAAI;IACjCA,OAAO,CAAC4B,YAAY,IAAA,IAAA,IAApB5B,OAAO,CAAC4B,YAAY,CAAGhB,MAAM,EAA2BzB,KAAK,CAAC,CAAA;AAChE,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOyB,MAAM,CAAA;AACf,CAAA;AAEO,MAAM6B,OAAqB,GAAG;EACnCC,WAAW,EAA0BvD,KAAmB,IAAW;AACjE;;IAEAA,KAAK,CAACwD,eAAe,GAAGhG,IAAI,CAC1B,MAAM,CACJwC,KAAK,CAACyD,aAAa,EAAE,EACrBzD,KAAK,CAAC0D,qBAAqB,EAAE,EAC7B1D,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,EACnC7D,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACC,UAAU,EAAEvB,WAAW,EAAEqB,IAAI,EAAEC,KAAK,KAAK;MAAA,IAAAE,gBAAA,EAAAC,iBAAA,CAAA;AACxC,MAAA,MAAMC,WAAW,GAAA,CAAAF,gBAAA,GACfH,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CACAR,GAAG,CAACnD,QAAQ,IAAIsC,WAAW,CAAC2B,IAAI,CAAC7H,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKP,QAAQ,CAAE,CAAC,CAC3DkE,MAAM,CAACC,OAAO,CAAC,KAAAL,IAAAA,GAAAA,gBAAA,GAAI,EAAE,CAAA;AAE1B,MAAA,MAAMM,YAAY,GAAA,CAAAL,iBAAA,GAChBH,KAAK,IAALA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CACDT,GAAG,CAACnD,QAAQ,IAAIsC,WAAW,CAAC2B,IAAI,CAAC7H,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKP,QAAQ,CAAE,CAAC,CAC3DkE,MAAM,CAACC,OAAO,CAAC,KAAAJ,IAAAA,GAAAA,iBAAA,GAAI,EAAE,CAAA;AAE1B,MAAA,MAAMM,aAAa,GAAG/B,WAAW,CAAC4B,MAAM,CACtC7I,MAAM,IAAI,EAACsI,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEnC,QAAQ,CAACnG,MAAM,CAACkF,EAAE,CAAC,CAAA,IAAI,EAACqD,KAAK,YAALA,KAAK,CAAEpC,QAAQ,CAACnG,MAAM,CAACkF,EAAE,CAAC,CACrE,CAAC,CAAA;AAED,MAAA,MAAM+D,YAAY,GAAGC,iBAAiB,CACpCV,UAAU,EACV,CAAC,GAAGG,WAAW,EAAE,GAAGK,aAAa,EAAE,GAAGD,YAAY,CAAC,EACnDtE,KACF,CAAC,CAAA;AAED,MAAA,OAAOwE,YAAY,CAAA;KACpB,EACDjF,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,iBAAiB,CACxD,CAAC,CAAA;IAEDgC,KAAK,CAAC0E,qBAAqB,GAAGlH,IAAI,CAChC,MAAM,CACJwC,KAAK,CAACyD,aAAa,EAAE,EACrBzD,KAAK,CAAC0D,qBAAqB,EAAE,EAC7B1D,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,EACnC7D,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACC,UAAU,EAAEvB,WAAW,EAAEqB,IAAI,EAAEC,KAAK,KAAK;AACxCtB,MAAAA,WAAW,GAAGA,WAAW,CAAC4B,MAAM,CAC9B7I,MAAM,IAAI,EAACsI,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEnC,QAAQ,CAACnG,MAAM,CAACkF,EAAE,CAAC,CAAA,IAAI,EAACqD,KAAK,YAALA,KAAK,CAAEpC,QAAQ,CAACnG,MAAM,CAACkF,EAAE,CAAC,CACrE,CAAC,CAAA;MACD,OAAOgE,iBAAiB,CAACV,UAAU,EAAEvB,WAAW,EAAExC,KAAK,EAAE,QAAQ,CAAC,CAAA;KACnE,EACDT,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,uBAAuB,CAC9D,CAAC,CAAA;AAEDgC,IAAAA,KAAK,CAAC2E,mBAAmB,GAAGnH,IAAI,CAC9B,MAAM,CACJwC,KAAK,CAACyD,aAAa,EAAE,EACrBzD,KAAK,CAAC0D,qBAAqB,EAAE,EAC7B1D,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,CACpC,EACD,CAACE,UAAU,EAAEvB,WAAW,EAAEqB,IAAI,KAAK;AAAA,MAAA,IAAAe,iBAAA,CAAA;AACjC,MAAA,MAAMC,kBAAkB,GAAA,CAAAD,iBAAA,GACtBf,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CACAR,GAAG,CAACnD,QAAQ,IAAIsC,WAAW,CAAC2B,IAAI,CAAC7H,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKP,QAAQ,CAAE,CAAC,CAC3DkE,MAAM,CAACC,OAAO,CAAC,KAAAO,IAAAA,GAAAA,iBAAA,GAAI,EAAE,CAAA;MAE1B,OAAOH,iBAAiB,CAACV,UAAU,EAAEc,kBAAkB,EAAE7E,KAAK,EAAE,MAAM,CAAC,CAAA;KACxE,EACDT,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;AAEDgC,IAAAA,KAAK,CAAC8E,oBAAoB,GAAGtH,IAAI,CAC/B,MAAM,CACJwC,KAAK,CAACyD,aAAa,EAAE,EACrBzD,KAAK,CAAC0D,qBAAqB,EAAE,EAC7B1D,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACC,UAAU,EAAEvB,WAAW,EAAEsB,KAAK,KAAK;AAAA,MAAA,IAAAiB,kBAAA,CAAA;AAClC,MAAA,MAAMF,kBAAkB,GAAA,CAAAE,kBAAA,GACtBjB,KAAK,IAALA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CACDT,GAAG,CAACnD,QAAQ,IAAIsC,WAAW,CAAC2B,IAAI,CAAC7H,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKP,QAAQ,CAAE,CAAC,CAC3DkE,MAAM,CAACC,OAAO,CAAC,KAAAU,IAAAA,GAAAA,kBAAA,GAAI,EAAE,CAAA;MAE1B,OAAON,iBAAiB,CAACV,UAAU,EAAEc,kBAAkB,EAAE7E,KAAK,EAAE,OAAO,CAAC,CAAA;KACzE,EACDT,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;;AAED;;AAEAgC,IAAAA,KAAK,CAACgF,eAAe,GAAGxH,IAAI,CAC1B,MAAM,CAACwC,KAAK,CAACwD,eAAe,EAAE,CAAC,EAC/BgB,YAAY,IAAI;AACd,MAAA,OAAO,CAAC,GAAGA,YAAY,CAAC,CAACS,OAAO,EAAE,CAAA;KACnC,EACD1F,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,iBAAiB,CACxD,CAAC,CAAA;AAEDgC,IAAAA,KAAK,CAACkF,mBAAmB,GAAG1H,IAAI,CAC9B,MAAM,CAACwC,KAAK,CAAC2E,mBAAmB,EAAE,CAAC,EACnCH,YAAY,IAAI;AACd,MAAA,OAAO,CAAC,GAAGA,YAAY,CAAC,CAACS,OAAO,EAAE,CAAA;KACnC,EACD1F,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;AAEDgC,IAAAA,KAAK,CAACmF,qBAAqB,GAAG3H,IAAI,CAChC,MAAM,CAACwC,KAAK,CAAC0E,qBAAqB,EAAE,CAAC,EACrCF,YAAY,IAAI;AACd,MAAA,OAAO,CAAC,GAAGA,YAAY,CAAC,CAACS,OAAO,EAAE,CAAA;KACnC,EACD1F,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,uBAAuB,CAC9D,CAAC,CAAA;AAEDgC,IAAAA,KAAK,CAACoF,oBAAoB,GAAG5H,IAAI,CAC/B,MAAM,CAACwC,KAAK,CAAC8E,oBAAoB,EAAE,CAAC,EACpCN,YAAY,IAAI;AACd,MAAA,OAAO,CAAC,GAAGA,YAAY,CAAC,CAACS,OAAO,EAAE,CAAA;KACnC,EACD1F,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;;AAED;;AAEAgC,IAAAA,KAAK,CAACqF,cAAc,GAAG7H,IAAI,CACzB,MAAM,CAACwC,KAAK,CAACwD,eAAe,EAAE,CAAC,EAC/BgB,YAAY,IAAI;AACd,MAAA,OAAOA,YAAY,CAChBnB,GAAG,CAACL,WAAW,IAAI;QAClB,OAAOA,WAAW,CAACsC,OAAO,CAAA;AAC5B,OAAC,CAAC,CACDtI,IAAI,EAAE,CAAA;KACV,EACDuC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,gBAAgB,CACvD,CAAC,CAAA;AAEDgC,IAAAA,KAAK,CAACuF,kBAAkB,GAAG/H,IAAI,CAC7B,MAAM,CAACwC,KAAK,CAAC2E,mBAAmB,EAAE,CAAC,EACnCd,IAAI,IAAI;AACN,MAAA,OAAOA,IAAI,CACRR,GAAG,CAACL,WAAW,IAAI;QAClB,OAAOA,WAAW,CAACsC,OAAO,CAAA;AAC5B,OAAC,CAAC,CACDtI,IAAI,EAAE,CAAA;KACV,EACDuC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,oBAAoB,CAC3D,CAAC,CAAA;AAEDgC,IAAAA,KAAK,CAACwF,oBAAoB,GAAGhI,IAAI,CAC/B,MAAM,CAACwC,KAAK,CAAC0E,qBAAqB,EAAE,CAAC,EACrCb,IAAI,IAAI;AACN,MAAA,OAAOA,IAAI,CACRR,GAAG,CAACL,WAAW,IAAI;QAClB,OAAOA,WAAW,CAACsC,OAAO,CAAA;AAC5B,OAAC,CAAC,CACDtI,IAAI,EAAE,CAAA;KACV,EACDuC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;AAEDgC,IAAAA,KAAK,CAACyF,mBAAmB,GAAGjI,IAAI,CAC9B,MAAM,CAACwC,KAAK,CAAC8E,oBAAoB,EAAE,CAAC,EACpCjB,IAAI,IAAI;AACN,MAAA,OAAOA,IAAI,CACRR,GAAG,CAACL,WAAW,IAAI;QAClB,OAAOA,WAAW,CAACsC,OAAO,CAAA;AAC5B,OAAC,CAAC,CACDtI,IAAI,EAAE,CAAA;KACV,EACDuC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;;AAED;;AAEAgC,IAAAA,KAAK,CAAC0F,oBAAoB,GAAGlI,IAAI,CAC/B,MAAM,CAACwC,KAAK,CAACwF,oBAAoB,EAAE,CAAC,EACpCG,WAAW,IAAI;AACb,MAAA,OAAOA,WAAW,CAACvB,MAAM,CAAC3C,MAAM,IAAA;AAAA,QAAA,IAAAmE,kBAAA,CAAA;QAAA,OAAI,EAAA,CAAAA,kBAAA,GAACnE,MAAM,CAACoB,UAAU,KAAA,IAAA,IAAjB+C,kBAAA,CAAmBrI,MAAM,CAAA,CAAA;OAAC,CAAA,CAAA;KAChE,EACDgC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;AAEDgC,IAAAA,KAAK,CAAC6F,kBAAkB,GAAGrI,IAAI,CAC7B,MAAM,CAACwC,KAAK,CAACuF,kBAAkB,EAAE,CAAC,EAClCI,WAAW,IAAI;AACb,MAAA,OAAOA,WAAW,CAACvB,MAAM,CAAC3C,MAAM,IAAA;AAAA,QAAA,IAAAqE,mBAAA,CAAA;QAAA,OAAI,EAAA,CAAAA,mBAAA,GAACrE,MAAM,CAACoB,UAAU,KAAA,IAAA,IAAjBiD,mBAAA,CAAmBvI,MAAM,CAAA,CAAA;OAAC,CAAA,CAAA;KAChE,EACDgC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,oBAAoB,CAC3D,CAAC,CAAA;AAEDgC,IAAAA,KAAK,CAAC+F,mBAAmB,GAAGvI,IAAI,CAC9B,MAAM,CAACwC,KAAK,CAACyF,mBAAmB,EAAE,CAAC,EACnCE,WAAW,IAAI;AACb,MAAA,OAAOA,WAAW,CAACvB,MAAM,CAAC3C,MAAM,IAAA;AAAA,QAAA,IAAAuE,mBAAA,CAAA;QAAA,OAAI,EAAA,CAAAA,mBAAA,GAACvE,MAAM,CAACoB,UAAU,KAAA,IAAA,IAAjBmD,mBAAA,CAAmBzI,MAAM,CAAA,CAAA;OAAC,CAAA,CAAA;KAChE,EACDgC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;AAEDgC,IAAAA,KAAK,CAACiD,cAAc,GAAGzF,IAAI,CACzB,MAAM,CACJwC,KAAK,CAAC2E,mBAAmB,EAAE,EAC3B3E,KAAK,CAAC0E,qBAAqB,EAAE,EAC7B1E,KAAK,CAAC8E,oBAAoB,EAAE,CAC7B,EACD,CAACjB,IAAI,EAAEoC,MAAM,EAAEnC,KAAK,KAAK;MAAA,IAAAoC,eAAA,EAAAC,MAAA,EAAAC,iBAAA,EAAAC,QAAA,EAAAC,gBAAA,EAAAC,OAAA,CAAA;AACvB,MAAA,OAAO,CACL,IAAA,CAAAL,eAAA,GAAA,CAAAC,MAAA,GAAItC,IAAI,CAAC,CAAC,CAAC,KAAPsC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAA,CAASb,OAAO,KAAAY,IAAAA,GAAAA,eAAA,GAAI,EAAE,GAC1B,IAAAE,CAAAA,iBAAA,GAAAC,CAAAA,QAAA,GAAIJ,MAAM,CAAC,CAAC,CAAC,KAATI,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAA,CAAWf,OAAO,KAAAc,IAAAA,GAAAA,iBAAA,GAAI,EAAE,GAC5B,IAAAE,CAAAA,gBAAA,GAAAC,CAAAA,OAAA,GAAIzC,KAAK,CAAC,CAAC,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAARyC,OAAA,CAAUjB,OAAO,KAAA,IAAA,GAAAgB,gBAAA,GAAI,EAAE,EAC5B,CACEjD,GAAG,CAAC5B,MAAM,IAAI;AACb,QAAA,OAAOA,MAAM,CAACwB,cAAc,EAAE,CAAA;AAChC,OAAC,CAAC,CACDjG,IAAI,EAAE,CAAA;KACV,EACDuC,cAAc,CAACS,KAAK,CAACO,OAAO,EAAEvC,KAAK,EAAE,gBAAgB,CACvD,CAAC,CAAA;AACH,GAAA;AACF,EAAC;AAEM,SAASyG,iBAAiBA,CAC/BV,UAAoC,EACpCyC,cAAwC,EACxCxG,KAAmB,EACnByG,YAA0C,EAC1C;EAAA,IAAAC,qBAAA,EAAAC,cAAA,CAAA;AACA;AACA;AACA;AACA;AACA;;EAEA,IAAIC,QAAQ,GAAG,CAAC,CAAA;AAEhB,EAAA,MAAMC,YAAY,GAAG,UAAC7E,OAAiC,EAAEhB,KAAK,EAAS;AAAA,IAAA,IAAdA,KAAK,KAAA,KAAA,CAAA,EAAA;AAALA,MAAAA,KAAK,GAAG,CAAC,CAAA;AAAA,KAAA;IAChE4F,QAAQ,GAAGjI,IAAI,CAACU,GAAG,CAACuH,QAAQ,EAAE5F,KAAK,CAAC,CAAA;AAEpCgB,IAAAA,OAAO,CACJoC,MAAM,CAAC7I,MAAM,IAAIA,MAAM,CAACuL,YAAY,EAAE,CAAC,CACvC3J,OAAO,CAAC5B,MAAM,IAAI;AAAA,MAAA,IAAA2G,eAAA,CAAA;MACjB,IAAAA,CAAAA,eAAA,GAAI3G,MAAM,CAACyG,OAAO,KAAdE,IAAAA,IAAAA,eAAA,CAAgB3E,MAAM,EAAE;QAC1BsJ,YAAY,CAACtL,MAAM,CAACyG,OAAO,EAAEhB,KAAK,GAAG,CAAC,CAAC,CAAA;AACzC,OAAA;KACD,EAAE,CAAC,CAAC,CAAA;GACR,CAAA;EAED6F,YAAY,CAAC9C,UAAU,CAAC,CAAA;EAExB,IAAIS,YAAkC,GAAG,EAAE,CAAA;AAE3C,EAAA,MAAMuC,iBAAiB,GAAGA,CACxBC,cAAwC,EACxChG,KAAa,KACV;AACH;AACA,IAAA,MAAMgC,WAA+B,GAAG;MACtChC,KAAK;AACLP,MAAAA,EAAE,EAAE,CAACgG,YAAY,EAAG,CAAA,EAAEzF,KAAM,CAAC,CAAA,CAAC,CAACoD,MAAM,CAACC,OAAO,CAAC,CAAC4C,IAAI,CAAC,GAAG,CAAC;AACxD3B,MAAAA,OAAO,EAAE,EAAA;KACV,CAAA;;AAED;IACA,MAAM4B,oBAA8C,GAAG,EAAE,CAAA;;AAEzD;AACAF,IAAAA,cAAc,CAAC7J,OAAO,CAACgK,aAAa,IAAI;AACtC;;AAEA,MAAA,MAAMC,yBAAyB,GAAG,CAAC,GAAGF,oBAAoB,CAAC,CAACjC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAA;MAExE,MAAMoC,YAAY,GAAGF,aAAa,CAAC5L,MAAM,CAACyF,KAAK,KAAKgC,WAAW,CAAChC,KAAK,CAAA;AAErE,MAAA,IAAIzF,MAA8B,CAAA;MAClC,IAAIoH,aAAa,GAAG,KAAK,CAAA;AAEzB,MAAA,IAAI0E,YAAY,IAAIF,aAAa,CAAC5L,MAAM,CAAC0F,MAAM,EAAE;AAC/C;AACA1F,QAAAA,MAAM,GAAG4L,aAAa,CAAC5L,MAAM,CAAC0F,MAAM,CAAA;AACtC,OAAC,MAAM;AACL;QACA1F,MAAM,GAAG4L,aAAa,CAAC5L,MAAM,CAAA;AAC7BoH,QAAAA,aAAa,GAAG,IAAI,CAAA;AACtB,OAAA;MAEA,IACEyE,yBAAyB,IACzB,CAAAA,yBAAyB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAzBA,yBAAyB,CAAE7L,MAAM,MAAKA,MAAM,EAC5C;AACA;AACA6L,QAAAA,yBAAyB,CAACvE,UAAU,CAACxF,IAAI,CAAC8J,aAAa,CAAC,CAAA;AAC1D,OAAC,MAAM;AACL;AACA,QAAA,MAAM1F,MAAM,GAAGgB,YAAY,CAACzC,KAAK,EAAEzE,MAAM,EAAE;UACzCkF,EAAE,EAAE,CAACgG,YAAY,EAAEzF,KAAK,EAAEzF,MAAM,CAACkF,EAAE,EAAE0G,aAAa,IAAA,IAAA,GAAA,KAAA,CAAA,GAAbA,aAAa,CAAE1G,EAAE,CAAC,CACpD2D,MAAM,CAACC,OAAO,CAAC,CACf4C,IAAI,CAAC,GAAG,CAAC;UACZtE,aAAa;UACbC,aAAa,EAAED,aAAa,GACvB,CAAA,EAAEuE,oBAAoB,CAAC9C,MAAM,CAAC9H,CAAC,IAAIA,CAAC,CAACf,MAAM,KAAKA,MAAM,CAAC,CAACgC,MAAO,CAAA,CAAC,GACjEiE,SAAS;UACbR,KAAK;UACLzC,KAAK,EAAE2I,oBAAoB,CAAC3J,MAAAA;AAC9B,SAAC,CAAC,CAAA;;AAEF;AACAkE,QAAAA,MAAM,CAACoB,UAAU,CAACxF,IAAI,CAAC8J,aAAa,CAAC,CAAA;AACrC;AACA;AACAD,QAAAA,oBAAoB,CAAC7J,IAAI,CAACoE,MAAM,CAAC,CAAA;AACnC,OAAA;AAEAuB,MAAAA,WAAW,CAACsC,OAAO,CAACjI,IAAI,CAAC8J,aAAa,CAAC,CAAA;MACvCA,aAAa,CAACnE,WAAW,GAAGA,WAAW,CAAA;AACzC,KAAC,CAAC,CAAA;AAEFwB,IAAAA,YAAY,CAACnH,IAAI,CAAC2F,WAAW,CAAC,CAAA;IAE9B,IAAIhC,KAAK,GAAG,CAAC,EAAE;AACb+F,MAAAA,iBAAiB,CAACG,oBAAoB,EAAElG,KAAK,GAAG,CAAC,CAAC,CAAA;AACpD,KAAA;GACD,CAAA;AAED,EAAA,MAAMsG,aAAa,GAAGd,cAAc,CAACnD,GAAG,CAAC,CAAC9H,MAAM,EAAEgD,KAAK,KACrDkE,YAAY,CAACzC,KAAK,EAAEzE,MAAM,EAAE;AAC1ByF,IAAAA,KAAK,EAAE4F,QAAQ;AACfrI,IAAAA,KAAAA;AACF,GAAC,CACH,CAAC,CAAA;AAEDwI,EAAAA,iBAAiB,CAACO,aAAa,EAAEV,QAAQ,GAAG,CAAC,CAAC,CAAA;EAE9CpC,YAAY,CAACS,OAAO,EAAE,CAAA;;AAEtB;AACA;AACA;;EAEA,MAAMsC,sBAAsB,GAC1BjC,OAAiC,IACU;AAC3C,IAAA,MAAMkC,eAAe,GAAGlC,OAAO,CAAClB,MAAM,CAAC3C,MAAM,IAC3CA,MAAM,CAAClG,MAAM,CAACuL,YAAY,EAC5B,CAAC,CAAA;AAED,IAAA,OAAOU,eAAe,CAACnE,GAAG,CAAC5B,MAAM,IAAI;MACnC,IAAIqB,OAAO,GAAG,CAAC,CAAA;MACf,IAAIC,OAAO,GAAG,CAAC,CAAA;AACf,MAAA,IAAI0E,aAAa,GAAG,CAAC,CAAC,CAAC,CAAA;MAEvB,IAAIhG,MAAM,CAACoB,UAAU,IAAIpB,MAAM,CAACoB,UAAU,CAACtF,MAAM,EAAE;AACjDkK,QAAAA,aAAa,GAAG,EAAE,CAAA;QAElBF,sBAAsB,CAAC9F,MAAM,CAACoB,UAAU,CAAC,CAAC1F,OAAO,CAC/C+D,IAAA,IAAsD;UAAA,IAArD;AAAE4B,YAAAA,OAAO,EAAE4E,YAAY;AAAE3E,YAAAA,OAAO,EAAE4E,YAAAA;AAAa,WAAC,GAAAzG,IAAA,CAAA;AAC/C4B,UAAAA,OAAO,IAAI4E,YAAY,CAAA;AACvBD,UAAAA,aAAa,CAACpK,IAAI,CAACsK,YAAY,CAAC,CAAA;AAClC,SACF,CAAC,CAAA;AACH,OAAC,MAAM;AACL7E,QAAAA,OAAO,GAAG,CAAC,CAAA;AACb,OAAA;MAEA,MAAM8E,eAAe,GAAGjJ,IAAI,CAACW,GAAG,CAAC,GAAGmI,aAAa,CAAC,CAAA;MAClD1E,OAAO,GAAGA,OAAO,GAAG6E,eAAe,CAAA;MAEnCnG,MAAM,CAACqB,OAAO,GAAGA,OAAO,CAAA;MACxBrB,MAAM,CAACsB,OAAO,GAAGA,OAAO,CAAA;MAExB,OAAO;QAAED,OAAO;AAAEC,QAAAA,OAAAA;OAAS,CAAA;AAC7B,KAAC,CAAC,CAAA;GACH,CAAA;AAEDwE,EAAAA,sBAAsB,EAAAb,qBAAA,GAAA,CAAAC,cAAA,GAACnC,YAAY,CAAC,CAAC,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAfmC,cAAA,CAAiBrB,OAAO,YAAAoB,qBAAA,GAAI,EAAE,CAAC,CAAA;AAEtD,EAAA,OAAOlC,YAAY,CAAA;AACrB;;MChiBaqD,SAAS,GAAGA,CACvB7H,KAAmB,EACnBS,EAAU,EACVqH,QAAe,EACfC,QAAgB,EAChB/G,KAAa,EACbgH,OAAsB,EACtBC,QAAiB,KACF;AACf,EAAA,IAAIhI,GAAmB,GAAG;IACxBQ,EAAE;AACFlC,IAAAA,KAAK,EAAEwJ,QAAQ;IACfD,QAAQ;IACR9G,KAAK;IACLiH,QAAQ;IACRC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtB7H,QAAQ,EAAEJ,QAAQ,IAAI;MACpB,IAAID,GAAG,CAACiI,YAAY,CAACE,cAAc,CAAClI,QAAQ,CAAC,EAAE;AAC7C,QAAA,OAAOD,GAAG,CAACiI,YAAY,CAAChI,QAAQ,CAAC,CAAA;AACnC,OAAA;AAEA,MAAA,MAAM3E,MAAM,GAAGyE,KAAK,CAACqI,SAAS,CAACnI,QAAQ,CAAC,CAAA;AAExC,MAAA,IAAI,EAAC3E,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEC,UAAU,CAAE,EAAA;AACvB,QAAA,OAAOgG,SAAS,CAAA;AAClB,OAAA;AAEAvB,MAAAA,GAAG,CAACiI,YAAY,CAAChI,QAAQ,CAAC,GAAG3E,MAAM,CAACC,UAAU,CAC5CyE,GAAG,CAAC6H,QAAQ,EACZC,QACF,CAAC,CAAA;AAED,MAAA,OAAO9H,GAAG,CAACiI,YAAY,CAAChI,QAAQ,CAAC,CAAA;KAClC;IACDoI,eAAe,EAAEpI,QAAQ,IAAI;MAC3B,IAAID,GAAG,CAACkI,kBAAkB,CAACC,cAAc,CAAClI,QAAQ,CAAC,EAAE;AACnD,QAAA,OAAOD,GAAG,CAACkI,kBAAkB,CAACjI,QAAQ,CAAC,CAAA;AACzC,OAAA;AAEA,MAAA,MAAM3E,MAAM,GAAGyE,KAAK,CAACqI,SAAS,CAACnI,QAAQ,CAAC,CAAA;AAExC,MAAA,IAAI,EAAC3E,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEC,UAAU,CAAE,EAAA;AACvB,QAAA,OAAOgG,SAAS,CAAA;AAClB,OAAA;AAEA,MAAA,IAAI,CAACjG,MAAM,CAACwF,SAAS,CAACuH,eAAe,EAAE;AACrCrI,QAAAA,GAAG,CAACkI,kBAAkB,CAACjI,QAAQ,CAAC,GAAG,CAACD,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAA;AAC3D,QAAA,OAAOD,GAAG,CAACkI,kBAAkB,CAACjI,QAAQ,CAAC,CAAA;AACzC,OAAA;AAEAD,MAAAA,GAAG,CAACkI,kBAAkB,CAACjI,QAAQ,CAAC,GAAG3E,MAAM,CAACwF,SAAS,CAACuH,eAAe,CACjErI,GAAG,CAAC6H,QAAQ,EACZC,QACF,CAAC,CAAA;AAED,MAAA,OAAO9H,GAAG,CAACkI,kBAAkB,CAACjI,QAAQ,CAAC,CAAA;KACxC;AACDQ,IAAAA,WAAW,EAAER,QAAQ,IAAA;AAAA,MAAA,IAAAqI,aAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,aAAA,GACnBtI,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,KAAA,IAAA,GAAAqI,aAAA,GAAIvI,KAAK,CAACO,OAAO,CAACC,mBAAmB,CAAA;AAAA,KAAA;AAC7DwH,IAAAA,OAAO,EAAEA,OAAO,IAAPA,IAAAA,GAAAA,OAAO,GAAI,EAAE;AACtBQ,IAAAA,WAAW,EAAEA,MAAM3L,SAAS,CAACoD,GAAG,CAAC+H,OAAO,EAAE1L,CAAC,IAAIA,CAAC,CAAC0L,OAAO,CAAC;AACzDS,IAAAA,YAAY,EAAEA,MACZxI,GAAG,CAACgI,QAAQ,GAAGjI,KAAK,CAAC0I,MAAM,CAACzI,GAAG,CAACgI,QAAQ,EAAE,IAAI,CAAC,GAAGzG,SAAS;IAC7DmH,aAAa,EAAEA,MAAM;MACnB,IAAIC,UAAwB,GAAG,EAAE,CAAA;MACjC,IAAIC,UAAU,GAAG5I,GAAG,CAAA;AACpB,MAAA,OAAO,IAAI,EAAE;AACX,QAAA,MAAM6I,SAAS,GAAGD,UAAU,CAACJ,YAAY,EAAE,CAAA;QAC3C,IAAI,CAACK,SAAS,EAAE,MAAA;AAChBF,QAAAA,UAAU,CAACvL,IAAI,CAACyL,SAAS,CAAC,CAAA;AAC1BD,QAAAA,UAAU,GAAGC,SAAS,CAAA;AACxB,OAAA;AACA,MAAA,OAAOF,UAAU,CAAC3D,OAAO,EAAE,CAAA;KAC5B;AACD8D,IAAAA,WAAW,EAAEvL,IAAI,CACf,MAAM,CAACwC,KAAK,CAACgJ,iBAAiB,EAAE,CAAC,EACjCxG,WAAW,IAAI;AACb,MAAA,OAAOA,WAAW,CAACa,GAAG,CAAC9H,MAAM,IAAI;QAC/B,OAAOwE,UAAU,CAACC,KAAK,EAAEC,GAAG,EAAgB1E,MAAM,EAAEA,MAAM,CAACkF,EAAE,CAAC,CAAA;AAChE,OAAC,CAAC,CAAA;KACH,EACDlB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,aAAa,CAC1D,CAAC;AAED0I,IAAAA,sBAAsB,EAAEzL,IAAI,CAC1B,MAAM,CAACyC,GAAG,CAAC8I,WAAW,EAAE,CAAC,EACzBG,QAAQ,IAAI;MACV,OAAOA,QAAQ,CAACC,MAAM,CACpB,CAACC,GAAG,EAAE/I,IAAI,KAAK;QACb+I,GAAG,CAAC/I,IAAI,CAAC9E,MAAM,CAACkF,EAAE,CAAC,GAAGJ,IAAI,CAAA;AAC1B,QAAA,OAAO+I,GAAG,CAAA;OACX,EACD,EACF,CAAC,CAAA;KACF,EACD7J,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,uBAAuB,CACpE,CAAA;GACD,CAAA;AAED,EAAA,KAAK,IAAI8I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrJ,KAAK,CAACY,SAAS,CAACrD,MAAM,EAAE8L,CAAC,EAAE,EAAE;AAC/C,IAAA,MAAMxI,OAAO,GAAGb,KAAK,CAACY,SAAS,CAACyI,CAAC,CAAC,CAAA;AAClCxI,IAAAA,OAAO,IAAPA,IAAAA,IAAAA,OAAO,CAAEgH,SAAS,IAAlBhH,IAAAA,IAAAA,OAAO,CAAEgH,SAAS,CAAG5H,GAAG,EAAgBD,KAAK,CAAC,CAAA;AAChD,GAAA;AAEA,EAAA,OAAOC,GAAG,CAAA;AACZ;;AC3JA;;AAEO,MAAMqJ,cAA4B,GAAG;AAC1CxI,EAAAA,YAAY,EAAEA,CACZvF,MAA8B,EAC9ByE,KAAmB,KACV;IACTzE,MAAM,CAACgO,mBAAmB,GACxBvJ,KAAK,CAACO,OAAO,CAACiJ,kBAAkB,IAChCxJ,KAAK,CAACO,OAAO,CAACiJ,kBAAkB,CAACxJ,KAAK,EAAEzE,MAAM,CAACkF,EAAE,CAAC,CAAA;IACpDlF,MAAM,CAACiO,kBAAkB,GAAG,MAAM;AAChC,MAAA,IAAI,CAACjO,MAAM,CAACgO,mBAAmB,EAAE;AAC/B,QAAA,OAAOvJ,KAAK,CAACyJ,sBAAsB,EAAE,CAAA;AACvC,OAAA;AAEA,MAAA,OAAOlO,MAAM,CAACgO,mBAAmB,EAAE,CAAA;KACpC,CAAA;IACDhO,MAAM,CAACmO,uBAAuB,GAC5B1J,KAAK,CAACO,OAAO,CAACoJ,sBAAsB,IACpC3J,KAAK,CAACO,OAAO,CAACoJ,sBAAsB,CAAC3J,KAAK,EAAEzE,MAAM,CAACkF,EAAE,CAAC,CAAA;IACxDlF,MAAM,CAACoO,sBAAsB,GAAG,MAAM;AACpC,MAAA,IAAI,CAACpO,MAAM,CAACmO,uBAAuB,EAAE;QACnC,OAAO,IAAIE,GAAG,EAAE,CAAA;AAClB,OAAA;AAEA,MAAA,OAAOrO,MAAM,CAACmO,uBAAuB,EAAE,CAAA;KACxC,CAAA;IACDnO,MAAM,CAACsO,uBAAuB,GAC5B7J,KAAK,CAACO,OAAO,CAACuJ,sBAAsB,IACpC9J,KAAK,CAACO,OAAO,CAACuJ,sBAAsB,CAAC9J,KAAK,EAAEzE,MAAM,CAACkF,EAAE,CAAC,CAAA;IACxDlF,MAAM,CAACuO,sBAAsB,GAAG,MAAM;AACpC,MAAA,IAAI,CAACvO,MAAM,CAACsO,uBAAuB,EAAE;AACnC,QAAA,OAAOrI,SAAS,CAAA;AAClB,OAAA;AAEA,MAAA,OAAOjG,MAAM,CAACsO,uBAAuB,EAAE,CAAA;KACxC,CAAA;AACH,GAAA;AACF;;ACjFA,MAAME,cAA6B,GAAGA,CACpC9J,GAAG,EACHC,QAAgB,EAChB8J,WAAmB,KAChB;AAAA,EAAA,IAAAzB,aAAA,CAAA;AACH,EAAA,MAAM0B,MAAM,GAAGD,WAAW,CAACE,WAAW,EAAE,CAAA;AACxC,EAAA,OAAO7F,OAAO,CAAA,CAAAkE,aAAA,GACZtI,GAAG,CACAK,QAAQ,CAAgBJ,QAAQ,CAAC,KAAA,IAAA,IAAA,CAAAqI,aAAA,GADpCA,aAAA,CAEI4B,QAAQ,EAAE,KAAA5B,IAAAA,IAAAA,CAAAA,aAAA,GAFdA,aAAA,CAGI2B,WAAW,EAAE,KAAA,IAAA,GAAA,KAAA,CAAA,GAHjB3B,aAAA,CAII7G,QAAQ,CAACuI,MAAM,CACrB,CAAC,CAAA;AACH,CAAC,CAAA;AAEDF,cAAc,CAACK,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAA;AAEzD,MAAM0N,uBAAsC,GAAGA,CAC7CrK,GAAG,EACHC,QAAgB,EAChB8J,WAAmB,KAChB;AAAA,EAAA,IAAAO,cAAA,CAAA;EACH,OAAOlG,OAAO,CAAAkG,CAAAA,cAAA,GACZtK,GAAG,CAACK,QAAQ,CAAgBJ,QAAQ,CAAC,KAAAqK,IAAAA,IAAAA,CAAAA,cAAA,GAArCA,cAAA,CAAuCJ,QAAQ,EAAE,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjDI,cAAA,CAAmD7I,QAAQ,CAACsI,WAAW,CACzE,CAAC,CAAA;AACH,CAAC,CAAA;AAEDM,uBAAuB,CAACF,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAA;AAElE,MAAM4N,YAA2B,GAAGA,CAClCvK,GAAG,EACHC,QAAgB,EAChB8J,WAAmB,KAChB;AAAA,EAAA,IAAAS,cAAA,CAAA;AACH,EAAA,OACE,CAAAA,CAAAA,cAAA,GAAAxK,GAAG,CAACK,QAAQ,CAAgBJ,QAAQ,CAAC,KAAA,IAAA,IAAA,CAAAuK,cAAA,GAArCA,cAAA,CAAuCN,QAAQ,EAAE,KAAjDM,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,cAAA,CAAmDP,WAAW,EAAE,OAChEF,WAAW,IAAXA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,WAAW,CAAEE,WAAW,EAAE,CAAA,CAAA;AAE9B,CAAC,CAAA;AAEDM,YAAY,CAACJ,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAA;AAEvD,MAAM8N,WAA0B,GAAGA,CACjCzK,GAAG,EACHC,QAAgB,EAChB8J,WAAoB,KACjB;AAAA,EAAA,IAAAW,cAAA,CAAA;AACH,EAAA,OAAA,CAAAA,cAAA,GAAO1K,GAAG,CAACK,QAAQ,CAAYJ,QAAQ,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjCyK,cAAA,CAAmCjJ,QAAQ,CAACsI,WAAW,CAAC,CAAA;AACjE,CAAC,CAAA;AAEDU,WAAW,CAACN,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,IAAI,EAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEW,MAAM,CAAA,CAAA;AAEtE,MAAMqN,cAA6B,GAAGA,CACpC3K,GAAG,EACHC,QAAgB,EAChB8J,WAAsB,KACnB;AACH,EAAA,OAAO,CAACA,WAAW,CAAC3L,IAAI,CACtBzB,GAAG,IAAA;AAAA,IAAA,IAAAiO,cAAA,CAAA;AAAA,IAAA,OAAI,EAAAA,CAAAA,cAAA,GAAC5K,GAAG,CAACK,QAAQ,CAAYJ,QAAQ,CAAC,aAAjC2K,cAAA,CAAmCnJ,QAAQ,CAAC9E,GAAG,CAAC,CAAA,CAAA;AAAA,GAC1D,CAAC,CAAA;AACH,CAAC,CAAA;AAEDgO,cAAc,CAACR,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,IAAI,EAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEW,MAAM,CAAA,CAAA;AAEzE,MAAMuN,eAA8B,GAAGA,CACrC7K,GAAG,EACHC,QAAgB,EAChB8J,WAAsB,KACnB;AACH,EAAA,OAAOA,WAAW,CAAC3L,IAAI,CACrBzB,GAAG,IAAA;AAAA,IAAA,IAAAmO,cAAA,CAAA;AAAA,IAAA,OAAA,CAAAA,cAAA,GAAI9K,GAAG,CAACK,QAAQ,CAAYJ,QAAQ,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjC6K,cAAA,CAAmCrJ,QAAQ,CAAC9E,GAAG,CAAC,CAAA;AAAA,GACzD,CAAC,CAAA;AACH,CAAC,CAAA;AAEDkO,eAAe,CAACV,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,IAAI,EAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEW,MAAM,CAAA,CAAA;AAE1E,MAAMyN,MAAqB,GAAGA,CAAC/K,GAAG,EAAEC,QAAgB,EAAE8J,WAAoB,KAAK;AAC7E,EAAA,OAAO/J,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,KAAK8J,WAAW,CAAA;AAC/C,CAAC,CAAA;AAEDgB,MAAM,CAACZ,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAA;AAEjD,MAAMqO,UAAyB,GAAGA,CAChChL,GAAG,EACHC,QAAgB,EAChB8J,WAAoB,KACjB;AACH,EAAA,OAAO/J,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,IAAI8J,WAAW,CAAA;AAC9C,CAAC,CAAA;AAEDiB,UAAU,CAACb,UAAU,GAAIxN,GAAQ,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAA;AAErD,MAAMsO,aAA4B,GAAGA,CACnCjL,GAAG,EACHC,QAAgB,EAChB8J,WAA6B,KAC1B;AACH,EAAA,IAAI,CAAC1K,GAAG,EAAED,GAAG,CAAC,GAAG2K,WAAW,CAAA;AAE5B,EAAA,MAAMmB,QAAQ,GAAGlL,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;AAC/C,EAAA,OAAOiL,QAAQ,IAAI7L,GAAG,IAAI6L,QAAQ,IAAI9L,GAAG,CAAA;AAC3C,CAAC,CAAA;AAED6L,aAAa,CAACE,kBAAkB,GAAIxO,GAAe,IAAK;AACtD,EAAA,IAAI,CAACyO,SAAS,EAAEC,SAAS,CAAC,GAAG1O,GAAG,CAAA;AAEhC,EAAA,IAAI2O,SAAS,GACX,OAAOF,SAAS,KAAK,QAAQ,GAAGG,UAAU,CAACH,SAAmB,CAAC,GAAGA,SAAS,CAAA;AAC7E,EAAA,IAAII,SAAS,GACX,OAAOH,SAAS,KAAK,QAAQ,GAAGE,UAAU,CAACF,SAAmB,CAAC,GAAGA,SAAS,CAAA;AAE7E,EAAA,IAAIhM,GAAG,GACL+L,SAAS,KAAK,IAAI,IAAIK,MAAM,CAACC,KAAK,CAACJ,SAAS,CAAC,GAAG,CAACK,QAAQ,GAAGL,SAAS,CAAA;AACvE,EAAA,IAAIlM,GAAG,GAAGiM,SAAS,KAAK,IAAI,IAAII,MAAM,CAACC,KAAK,CAACF,SAAS,CAAC,GAAGG,QAAQ,GAAGH,SAAS,CAAA;EAE9E,IAAInM,GAAG,GAAGD,GAAG,EAAE;IACb,MAAMwM,IAAI,GAAGvM,GAAG,CAAA;AAChBA,IAAAA,GAAG,GAAGD,GAAG,CAAA;AACTA,IAAAA,GAAG,GAAGwM,IAAI,CAAA;AACZ,GAAA;AAEA,EAAA,OAAO,CAACvM,GAAG,EAAED,GAAG,CAAC,CAAA;AACnB,CAAC,CAAA;AAED6L,aAAa,CAACd,UAAU,GAAIxN,GAAQ,IAClCyN,UAAU,CAACzN,GAAG,CAAC,IAAKyN,UAAU,CAACzN,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIyN,UAAU,CAACzN,GAAG,CAAC,CAAC,CAAC,CAAE,CAAA;;AAE/D;;AAEO,MAAMkP,SAAS,GAAG;EACvB/B,cAAc;EACdO,uBAAuB;EACvBE,YAAY;EACZE,WAAW;EACXE,cAAc;EACdE,eAAe;EACfE,MAAM;EACNC,UAAU;AACVC,EAAAA,aAAAA;AACF,EAAC;AAID;;AAEA,SAASb,UAAUA,CAACzN,GAAQ,EAAE;EAC5B,OAAOA,GAAG,KAAK4E,SAAS,IAAI5E,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,EAAE,CAAA;AACxD;;AC2FA;;AAEO,MAAMmP,eAA6B,GAAG;EAC3CC,mBAAmB,EAAEA,MAEiB;IACpC,OAAO;AACLC,MAAAA,QAAQ,EAAE,MAAA;KACX,CAAA;GACF;EAEDC,eAAe,EAAGC,KAAK,IAA8B;IACnD,OAAO;AACLC,MAAAA,aAAa,EAAE,EAAE;MACjB,GAAGD,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfrM,KAAmB,IACa;IAChC,OAAO;AACLsM,MAAAA,qBAAqB,EAAEtQ,gBAAgB,CAAC,eAAe,EAAEgE,KAAK,CAAC;AAC/DuM,MAAAA,kBAAkB,EAAE,KAAK;AACzBC,MAAAA,qBAAqB,EAAE,GAAA;KACxB,CAAA;GACF;AAED1L,EAAAA,YAAY,EAAEA,CACZvF,MAA8B,EAC9ByE,KAAmB,KACV;IACTzE,MAAM,CAACkR,eAAe,GAAG,MAAM;MAC7B,MAAMC,QAAQ,GAAG1M,KAAK,CAAC2M,eAAe,EAAE,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAA;MAEpD,MAAMC,KAAK,GAAGH,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAEpM,QAAQ,CAAC/E,MAAM,CAACkF,EAAE,CAAC,CAAA;AAE3C,MAAA,IAAI,OAAOoM,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOf,SAAS,CAAC/B,cAAc,CAAA;AACjC,OAAA;AAEA,MAAA,IAAI,OAAO8C,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOf,SAAS,CAACZ,aAAa,CAAA;AAChC,OAAA;AAEA,MAAA,IAAI,OAAO2B,KAAK,KAAK,SAAS,EAAE;QAC9B,OAAOf,SAAS,CAACd,MAAM,CAAA;AACzB,OAAA;MAEA,IAAI6B,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC/C,OAAOf,SAAS,CAACd,MAAM,CAAA;AACzB,OAAA;AAEA,MAAA,IAAIvO,KAAK,CAACC,OAAO,CAACmQ,KAAK,CAAC,EAAE;QACxB,OAAOf,SAAS,CAACpB,WAAW,CAAA;AAC9B,OAAA;MAEA,OAAOoB,SAAS,CAACb,UAAU,CAAA;KAC5B,CAAA;IACD1P,MAAM,CAACuR,WAAW,GAAG,MAAM;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;AACzB,MAAA,OAAO3Q,UAAU,CAACd,MAAM,CAACwF,SAAS,CAACkL,QAAQ,CAAC,GACxC1Q,MAAM,CAACwF,SAAS,CAACkL,QAAQ,GACzB1Q,MAAM,CAACwF,SAAS,CAACkL,QAAQ,KAAK,MAAM,GAClC1Q,MAAM,CAACkR,eAAe,EAAE;AACxB,MAAA,CAAAM,qBAAA,GAAA,CAAAC,sBAAA,GACAhN,KAAK,CAACO,OAAO,CAACuL,SAAS,KAAA,IAAA,GAAA,KAAA,CAAA,GAAvBkB,sBAAA,CAA0BzR,MAAM,CAACwF,SAAS,CAACkL,QAAQ,CAAW,KAAAc,IAAAA,GAAAA,qBAAA,GAC9DjB,SAAS,CAACvQ,MAAM,CAACwF,SAAS,CAACkL,QAAQ,CAAoB,CAAA;KAC9D,CAAA;IACD1Q,MAAM,CAAC0R,YAAY,GAAG,MAAM;AAAA,MAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,CAAA;AAC1B,MAAA,OACE,EAAAF,qBAAA,GAAC3R,MAAM,CAACwF,SAAS,CAACsM,kBAAkB,KAAA,IAAA,GAAAH,qBAAA,GAAI,IAAI,OAAAC,qBAAA,GAC3CnN,KAAK,CAACO,OAAO,CAAC+M,mBAAmB,KAAA,IAAA,GAAAH,qBAAA,GAAI,IAAI,CAAC,KAAAC,CAAAA,sBAAA,GAC1CpN,KAAK,CAACO,OAAO,CAACgN,aAAa,YAAAH,sBAAA,GAAI,IAAI,CAAC,IACrC,CAAC,CAAC7R,MAAM,CAACC,UAAU,CAAA;KAEtB,CAAA;IAEDD,MAAM,CAACiS,aAAa,GAAG,MAAMjS,MAAM,CAACkS,cAAc,EAAE,GAAG,CAAC,CAAC,CAAA;IAEzDlS,MAAM,CAACmS,cAAc,GAAG,MAAA;AAAA,MAAA,IAAAC,qBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,qBAAA,GACtB3N,KAAK,CAAC2D,QAAQ,EAAE,CAACyI,aAAa,KAAA,IAAA,IAAA,CAAAuB,qBAAA,GAA9BA,qBAAA,CAAgCxJ,IAAI,CAAC7H,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,KAA7DkN,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAA+Dd,KAAK,CAAA;AAAA,KAAA,CAAA;IAEtEtR,MAAM,CAACkS,cAAc,GAAG,MAAA;MAAA,IAAAG,sBAAA,EAAAC,sBAAA,CAAA;AAAA,MAAA,OAAA,CAAAD,sBAAA,GAAA,CAAAC,sBAAA,GACtB7N,KAAK,CAAC2D,QAAQ,EAAE,CAACyI,aAAa,KAAA,IAAA,GAAA,KAAA,CAAA,GAA9ByB,sBAAA,CAAgCC,SAAS,CAACxR,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,KAAA,IAAA,GAAAmN,sBAAA,GAAI,CAAC,CAAC,CAAA;AAAA,KAAA,CAAA;AAE1ErS,IAAAA,MAAM,CAACwS,cAAc,GAAGlB,KAAK,IAAI;AAC/B7M,MAAAA,KAAK,CAACgO,gBAAgB,CAAC5R,GAAG,IAAI;AAC5B,QAAA,MAAM6P,QAAQ,GAAG1Q,MAAM,CAACuR,WAAW,EAAE,CAAA;AACrC,QAAA,MAAMmB,cAAc,GAAG7R,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE+H,IAAI,CAAC7H,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,CAAA;AAEzD,QAAA,MAAMyN,SAAS,GAAGtS,gBAAgB,CAChCiR,KAAK,EACLoB,cAAc,GAAGA,cAAc,CAACpB,KAAK,GAAGrL,SAC1C,CAAC,CAAA;;AAED;QACA,IACE2M,sBAAsB,CAAClC,QAAQ,EAAqBiC,SAAS,EAAE3S,MAAM,CAAC,EACtE;AAAA,UAAA,IAAA6S,WAAA,CAAA;UACA,OAAAA,CAAAA,WAAA,GAAOhS,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEgI,MAAM,CAAC9H,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,KAAA,IAAA,GAAA2N,WAAA,GAAI,EAAE,CAAA;AACnD,SAAA;AAEA,QAAA,MAAMC,YAAY,GAAG;UAAE5N,EAAE,EAAElF,MAAM,CAACkF,EAAE;AAAEoM,UAAAA,KAAK,EAAEqB,SAAAA;SAAW,CAAA;AAExD,QAAA,IAAID,cAAc,EAAE;AAAA,UAAA,IAAAK,QAAA,CAAA;UAClB,OAAAA,CAAAA,QAAA,GACElS,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEiH,GAAG,CAAC/G,CAAC,IAAI;AACZ,YAAA,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,EAAE;AACtB,cAAA,OAAO4N,YAAY,CAAA;AACrB,aAAA;AACA,YAAA,OAAO/R,CAAC,CAAA;AACV,WAAC,CAAC,KAAA,IAAA,GAAAgS,QAAA,GAAI,EAAE,CAAA;AAEZ,SAAA;AAEA,QAAA,IAAIlS,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEmB,MAAM,EAAE;AACf,UAAA,OAAO,CAAC,GAAGnB,GAAG,EAAEiS,YAAY,CAAC,CAAA;AAC/B,SAAA;QAEA,OAAO,CAACA,YAAY,CAAC,CAAA;AACvB,OAAC,CAAC,CAAA;KACH,CAAA;GACF;AAEDxG,EAAAA,SAAS,EAAEA,CACT5H,GAAe,EACfsO,MAAoB,KACX;AACTtO,IAAAA,GAAG,CAACmM,aAAa,GAAG,EAAE,CAAA;AACtBnM,IAAAA,GAAG,CAACuO,iBAAiB,GAAG,EAAE,CAAA;GAC3B;EAEDjL,WAAW,EAA0BvD,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACgO,gBAAgB,GAAInS,OAAoC,IAAK;AACjE,MAAA,MAAM2G,WAAW,GAAGxC,KAAK,CAACgJ,iBAAiB,EAAE,CAAA;MAE7C,MAAMyF,QAAQ,GAAIrS,GAAuB,IAAK;AAAA,QAAA,IAAAsS,iBAAA,CAAA;AAC5C,QAAA,OAAA,CAAAA,iBAAA,GAAO9S,gBAAgB,CAACC,OAAO,EAAEO,GAAG,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAA9BsS,iBAAA,CAAgCtK,MAAM,CAACA,MAAM,IAAI;AACtD,UAAA,MAAM7I,MAAM,GAAGiH,WAAW,CAAC2B,IAAI,CAAC7H,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAK2D,MAAM,CAAC3D,EAAE,CAAC,CAAA;AAExD,UAAA,IAAIlF,MAAM,EAAE;AACV,YAAA,MAAM0Q,QAAQ,GAAG1Q,MAAM,CAACuR,WAAW,EAAE,CAAA;YAErC,IAAIqB,sBAAsB,CAAClC,QAAQ,EAAE7H,MAAM,CAACyI,KAAK,EAAEtR,MAAM,CAAC,EAAE;AAC1D,cAAA,OAAO,KAAK,CAAA;AACd,aAAA;AACF,WAAA;AAEA,UAAA,OAAO,IAAI,CAAA;AACb,SAAC,CAAC,CAAA;OACH,CAAA;AAEDyE,MAAAA,KAAK,CAACO,OAAO,CAAC+L,qBAAqB,IAAnCtM,IAAAA,IAAAA,KAAK,CAACO,OAAO,CAAC+L,qBAAqB,CAAGmC,QAAQ,CAAC,CAAA;KAChD,CAAA;AAEDzO,IAAAA,KAAK,CAAC2O,kBAAkB,GAAGC,YAAY,IAAI;MAAA,IAAAC,qBAAA,EAAAC,mBAAA,CAAA;MACzC9O,KAAK,CAACgO,gBAAgB,CACpBY,YAAY,GAAG,EAAE,GAAA,CAAAC,qBAAA,GAAA,CAAAC,mBAAA,GAAG9O,KAAK,CAAC+O,YAAY,qBAAlBD,mBAAA,CAAoB1C,aAAa,KAAAyC,IAAAA,GAAAA,qBAAA,GAAI,EAC3D,CAAC,CAAA;KACF,CAAA;IAED7O,KAAK,CAACyJ,sBAAsB,GAAG,MAAMzJ,KAAK,CAAC2M,eAAe,EAAE,CAAA;IAC5D3M,KAAK,CAACgP,mBAAmB,GAAG,MAAM;MAChC,IAAI,CAAChP,KAAK,CAACiP,oBAAoB,IAAIjP,KAAK,CAACO,OAAO,CAACyO,mBAAmB,EAAE;QACpEhP,KAAK,CAACiP,oBAAoB,GAAGjP,KAAK,CAACO,OAAO,CAACyO,mBAAmB,CAAChP,KAAK,CAAC,CAAA;AACvE,OAAA;MAEA,IAAIA,KAAK,CAACO,OAAO,CAAC2O,eAAe,IAAI,CAAClP,KAAK,CAACiP,oBAAoB,EAAE;AAChE,QAAA,OAAOjP,KAAK,CAACyJ,sBAAsB,EAAE,CAAA;AACvC,OAAA;AAEA,MAAA,OAAOzJ,KAAK,CAACiP,oBAAoB,EAAE,CAAA;KACpC,CAAA;AACH,GAAA;AACF,EAAC;AAEM,SAASd,sBAAsBA,CACpClC,QAA0B,EAC1BY,KAAW,EACXtR,MAA+B,EAC/B;AACA,EAAA,OACE,CAAC0Q,QAAQ,IAAIA,QAAQ,CAAC7B,UAAU,GAC5B6B,QAAQ,CAAC7B,UAAU,CAACyC,KAAK,EAAEtR,MAAM,CAAC,GAClC,KAAK,KACT,OAAOsR,KAAK,KAAK,WAAW,IAC3B,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAM,CAAA;AAEzC;;ACzaA,MAAMsC,GAAuB,GAAGA,CAACjP,QAAQ,EAAEkP,SAAS,EAAEC,SAAS,KAAK;AAClE;AACA;EACA,OAAOA,SAAS,CAAClG,MAAM,CAAC,CAACgG,GAAG,EAAEG,IAAI,KAAK;AACrC,IAAA,MAAMC,SAAS,GAAGD,IAAI,CAAChP,QAAQ,CAACJ,QAAQ,CAAC,CAAA;IACzC,OAAOiP,GAAG,IAAI,OAAOI,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,CAAC,CAAC,CAAA;GAC7D,EAAE,CAAC,CAAC,CAAA;AACP,CAAC,CAAA;AAED,MAAMjQ,GAAuB,GAAGA,CAACY,QAAQ,EAAEkP,SAAS,EAAEC,SAAS,KAAK;AAClE,EAAA,IAAI/P,GAAuB,CAAA;AAE3B+P,EAAAA,SAAS,CAAClS,OAAO,CAAC8C,GAAG,IAAI;AACvB,IAAA,MAAM4M,KAAK,GAAG5M,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;AAE5C,IAAA,IACE2M,KAAK,IAAI,IAAI,KACZvN,GAAG,GAAIuN,KAAK,IAAKvN,GAAG,KAAKkC,SAAS,IAAIqL,KAAK,IAAIA,KAAM,CAAC,EACvD;AACAvN,MAAAA,GAAG,GAAGuN,KAAK,CAAA;AACb,KAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOvN,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,MAAMD,GAAuB,GAAGA,CAACa,QAAQ,EAAEkP,SAAS,EAAEC,SAAS,KAAK;AAClE,EAAA,IAAIhQ,GAAuB,CAAA;AAE3BgQ,EAAAA,SAAS,CAAClS,OAAO,CAAC8C,GAAG,IAAI;AACvB,IAAA,MAAM4M,KAAK,GAAG5M,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;AAC5C,IAAA,IACE2M,KAAK,IAAI,IAAI,KACZxN,GAAG,GAAIwN,KAAK,IAAKxN,GAAG,KAAKmC,SAAS,IAAIqL,KAAK,IAAIA,KAAM,CAAC,EACvD;AACAxN,MAAAA,GAAG,GAAGwN,KAAK,CAAA;AACb,KAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOxN,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,MAAMmQ,MAA0B,GAAGA,CAACtP,QAAQ,EAAEkP,SAAS,EAAEC,SAAS,KAAK;AACrE,EAAA,IAAI/P,GAAuB,CAAA;AAC3B,EAAA,IAAID,GAAuB,CAAA;AAE3BgQ,EAAAA,SAAS,CAAClS,OAAO,CAAC8C,GAAG,IAAI;AACvB,IAAA,MAAM4M,KAAK,GAAG5M,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;IAC5C,IAAI2M,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIvN,GAAG,KAAKkC,SAAS,EAAE;QACrB,IAAIqL,KAAK,IAAIA,KAAK,EAAEvN,GAAG,GAAGD,GAAG,GAAGwN,KAAK,CAAA;AACvC,OAAC,MAAM;AACL,QAAA,IAAIvN,GAAG,GAAGuN,KAAK,EAAEvN,GAAG,GAAGuN,KAAK,CAAA;AAC5B,QAAA,IAAIxN,GAAG,GAAIwN,KAAK,EAAExN,GAAG,GAAGwN,KAAK,CAAA;AAC/B,OAAA;AACF,KAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,OAAO,CAACvN,GAAG,EAAED,GAAG,CAAC,CAAA;AACnB,CAAC,CAAA;AAED,MAAMoQ,IAAwB,GAAGA,CAACvP,QAAQ,EAAEwP,QAAQ,KAAK;EACvD,IAAIC,KAAK,GAAG,CAAC,CAAA;EACb,IAAIR,GAAG,GAAG,CAAC,CAAA;AAEXO,EAAAA,QAAQ,CAACvS,OAAO,CAAC8C,GAAG,IAAI;AACtB,IAAA,IAAI4M,KAAK,GAAG5M,GAAG,CAACK,QAAQ,CAASJ,QAAQ,CAAC,CAAA;IAC1C,IAAI2M,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;AAC9C,MAAA,EAAE8C,KAAK,EAAGR,GAAG,IAAItC,KAAM,CAAA;AACzB,KAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,IAAI8C,KAAK,EAAE,OAAOR,GAAG,GAAGQ,KAAK,CAAA;AAE7B,EAAA,OAAA;AACF,CAAC,CAAA;AAED,MAAMC,MAA0B,GAAGA,CAAC1P,QAAQ,EAAEwP,QAAQ,KAAK;AACzD,EAAA,IAAI,CAACA,QAAQ,CAACnS,MAAM,EAAE;AACpB,IAAA,OAAA;AACF,GAAA;AAEA,EAAA,MAAMsS,MAAM,GAAGH,QAAQ,CAACrM,GAAG,CAACpD,GAAG,IAAIA,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAA;AAC1D,EAAA,IAAI,CAAC1D,aAAa,CAACqT,MAAM,CAAC,EAAE;AAC1B,IAAA,OAAA;AACF,GAAA;AACA,EAAA,IAAIA,MAAM,CAACtS,MAAM,KAAK,CAAC,EAAE;IACvB,OAAOsS,MAAM,CAAC,CAAC,CAAC,CAAA;AAClB,GAAA;EAEA,MAAMC,GAAG,GAAGnR,IAAI,CAACoR,KAAK,CAACF,MAAM,CAACtS,MAAM,GAAG,CAAC,CAAC,CAAA;AACzC,EAAA,MAAMyS,IAAI,GAAGH,MAAM,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CAAA;EACzC,OAAON,MAAM,CAACtS,MAAM,GAAG,CAAC,KAAK,CAAC,GAAGyS,IAAI,CAACF,GAAG,CAAC,GAAG,CAACE,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC,GAAIE,IAAI,CAACF,GAAG,CAAE,IAAI,CAAC,CAAA;AAChF,CAAC,CAAA;AAED,MAAMM,MAA0B,GAAGA,CAAClQ,QAAQ,EAAEwP,QAAQ,KAAK;EACzD,OAAOjT,KAAK,CAAC4T,IAAI,CAAC,IAAIC,GAAG,CAACZ,QAAQ,CAACrM,GAAG,CAAC/G,CAAC,IAAIA,CAAC,CAACgE,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAAC2P,MAAM,EAAE,CAAC,CAAA;AAC9E,CAAC,CAAA;AAED,MAAMU,WAA+B,GAAGA,CAACrQ,QAAQ,EAAEwP,QAAQ,KAAK;AAC9D,EAAA,OAAO,IAAIY,GAAG,CAACZ,QAAQ,CAACrM,GAAG,CAAC/G,CAAC,IAAIA,CAAC,CAACgE,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAACsQ,IAAI,CAAA;AAC9D,CAAC,CAAA;AAED,MAAMb,KAAyB,GAAGA,CAACc,SAAS,EAAEf,QAAQ,KAAK;EACzD,OAAOA,QAAQ,CAACnS,MAAM,CAAA;AACxB,CAAC,CAAA;AAEM,MAAMmT,cAAc,GAAG;EAC5BvB,GAAG;EACH7P,GAAG;EACHD,GAAG;EACHmQ,MAAM;EACNC,IAAI;EACJG,MAAM;EACNQ,MAAM;EACNG,WAAW;AACXZ,EAAAA,KAAAA;AACF;;ACuHA;;AAEO,MAAMgB,cAA4B,GAAG;EAC1C3E,mBAAmB,EAAEA,MAGhB;IACH,OAAO;AACL4E,MAAAA,cAAc,EAAEC,KAAK,IAAA;QAAA,IAAAC,SAAA,EAAAC,eAAA,CAAA;QAAA,OAAAD,CAAAA,SAAA,IAAAC,eAAA,GAAKF,KAAK,CAACvQ,QAAQ,EAAE,KAAjByQ,IAAAA,IAAAA,eAAA,CAA2B5G,QAAQ,IAAA,IAAA,GAAA,KAAA,CAAA,GAAnC4G,eAAA,CAA2B5G,QAAQ,EAAI,KAAA,IAAA,GAAA2G,SAAA,GAAI,IAAI,CAAA;AAAA,OAAA;AACxEE,MAAAA,aAAa,EAAE,MAAA;KAChB,CAAA;GACF;EAED9E,eAAe,EAAGC,KAAK,IAAyB;IAC9C,OAAO;AACL8E,MAAAA,QAAQ,EAAE,EAAE;MACZ,GAAG9E,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfrM,KAAmB,IACC;IACpB,OAAO;AACLkR,MAAAA,gBAAgB,EAAElV,gBAAgB,CAAC,UAAU,EAAEgE,KAAK,CAAC;AACrDmR,MAAAA,iBAAiB,EAAE,SAAA;KACpB,CAAA;GACF;AAEDrQ,EAAAA,YAAY,EAAEA,CACZvF,MAA6B,EAC7ByE,KAAmB,KACV;IACTzE,MAAM,CAAC6V,cAAc,GAAG,MAAM;AAC5BpR,MAAAA,KAAK,CAACqR,WAAW,CAACjV,GAAG,IAAI;AACvB;QACA,IAAIA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEsF,QAAQ,CAACnG,MAAM,CAACkF,EAAE,CAAC,EAAE;UAC5B,OAAOrE,GAAG,CAACgI,MAAM,CAAC9H,CAAC,IAAIA,CAAC,KAAKf,MAAM,CAACkF,EAAE,CAAC,CAAA;AACzC,SAAA;AAEA,QAAA,OAAO,CAAC,IAAIrE,GAAG,IAAA,IAAA,GAAHA,GAAG,GAAI,EAAE,GAAGb,MAAM,CAACkF,EAAE,CAAC,CAAA;AACpC,OAAC,CAAC,CAAA;KACH,CAAA;IAEDlF,MAAM,CAAC+V,WAAW,GAAG,MAAM;MAAA,IAAApE,qBAAA,EAAAC,qBAAA,CAAA;AACzB,MAAA,OACE,EAAAD,qBAAA,GAAC3R,MAAM,CAACwF,SAAS,CAACwQ,cAAc,KAAArE,IAAAA,GAAAA,qBAAA,GAAI,IAAI,MAAA,CAAAC,qBAAA,GACvCnN,KAAK,CAACO,OAAO,CAACgR,cAAc,KAAA,IAAA,GAAApE,qBAAA,GAAI,IAAI,CAAC,KACrC,CAAC,CAAC5R,MAAM,CAACC,UAAU,IAAI,CAAC,CAACD,MAAM,CAACwF,SAAS,CAACyQ,gBAAgB,CAAC,CAAA;KAE/D,CAAA;IAEDjW,MAAM,CAACkW,YAAY,GAAG,MAAM;AAAA,MAAA,IAAAC,qBAAA,CAAA;AAC1B,MAAA,OAAA,CAAAA,qBAAA,GAAO1R,KAAK,CAAC2D,QAAQ,EAAE,CAACsN,QAAQ,KAAA,IAAA,GAAA,KAAA,CAAA,GAAzBS,qBAAA,CAA2BhQ,QAAQ,CAACnG,MAAM,CAACkF,EAAE,CAAC,CAAA;KACtD,CAAA;IAEDlF,MAAM,CAACoW,eAAe,GAAG,MAAA;AAAA,MAAA,IAAAC,sBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,sBAAA,GAAM5R,KAAK,CAAC2D,QAAQ,EAAE,CAACsN,QAAQ,KAAA,IAAA,GAAA,KAAA,CAAA,GAAzBW,sBAAA,CAA2BC,OAAO,CAACtW,MAAM,CAACkF,EAAE,CAAC,CAAA;AAAA,KAAA,CAAA;IAE5ElF,MAAM,CAACuW,wBAAwB,GAAG,MAAM;AACtC,MAAA,MAAMC,QAAQ,GAAGxW,MAAM,CAAC+V,WAAW,EAAE,CAAA;AAErC,MAAA,OAAO,MAAM;QACX,IAAI,CAACS,QAAQ,EAAE,OAAA;QACfxW,MAAM,CAAC6V,cAAc,EAAE,CAAA;OACxB,CAAA;KACF,CAAA;IACD7V,MAAM,CAACyW,oBAAoB,GAAG,MAAM;MAClC,MAAMtF,QAAQ,GAAG1M,KAAK,CAAC2M,eAAe,EAAE,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAA;MAEpD,MAAMC,KAAK,GAAGH,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAEpM,QAAQ,CAAC/E,MAAM,CAACkF,EAAE,CAAC,CAAA;AAE3C,MAAA,IAAI,OAAOoM,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO6D,cAAc,CAACvB,GAAG,CAAA;AAC3B,OAAA;AAEA,MAAA,IAAI8C,MAAM,CAACC,SAAS,CAAC/H,QAAQ,CAACgI,IAAI,CAACtF,KAAK,CAAC,KAAK,eAAe,EAAE;QAC7D,OAAO6D,cAAc,CAAClB,MAAM,CAAA;AAC9B,OAAA;KACD,CAAA;IACDjU,MAAM,CAAC6W,gBAAgB,GAAG,MAAM;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAC9B,IAAI,CAAC/W,MAAM,EAAE;QACX,MAAM,IAAIwG,KAAK,EAAE,CAAA;AACnB,OAAA;MAEA,OAAO1F,UAAU,CAACd,MAAM,CAACwF,SAAS,CAACiQ,aAAa,CAAC,GAC7CzV,MAAM,CAACwF,SAAS,CAACiQ,aAAa,GAC9BzV,MAAM,CAACwF,SAAS,CAACiQ,aAAa,KAAK,MAAM,GACvCzV,MAAM,CAACyW,oBAAoB,EAAE,IAAAK,qBAAA,GAAA,CAAAC,sBAAA,GAC7BtS,KAAK,CAACO,OAAO,CAACmQ,cAAc,KAAA,IAAA,GAAA,KAAA,CAAA,GAA5B4B,sBAAA,CACE/W,MAAM,CAACwF,SAAS,CAACiQ,aAAa,CAC/B,KAAAqB,IAAAA,GAAAA,qBAAA,GACD3B,cAAc,CACZnV,MAAM,CAACwF,SAAS,CAACiQ,aAAa,CAC/B,CAAA;KACR,CAAA;GACF;EAEDzN,WAAW,EAA0BvD,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACqR,WAAW,GAAGxV,OAAO,IAAImE,KAAK,CAACO,OAAO,CAAC2Q,gBAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAA9BlR,KAAK,CAACO,OAAO,CAAC2Q,gBAAgB,CAAGrV,OAAO,CAAC,CAAA;AAExEmE,IAAAA,KAAK,CAACuS,aAAa,GAAG3D,YAAY,IAAI;MAAA,IAAA4D,qBAAA,EAAA1D,mBAAA,CAAA;MACpC9O,KAAK,CAACqR,WAAW,CAACzC,YAAY,GAAG,EAAE,GAAA,CAAA4D,qBAAA,GAAA,CAAA1D,mBAAA,GAAG9O,KAAK,CAAC+O,YAAY,qBAAlBD,mBAAA,CAAoBmC,QAAQ,KAAAuB,IAAAA,GAAAA,qBAAA,GAAI,EAAE,CAAC,CAAA;KAC1E,CAAA;IAEDxS,KAAK,CAACyS,qBAAqB,GAAG,MAAMzS,KAAK,CAACgP,mBAAmB,EAAE,CAAA;IAC/DhP,KAAK,CAAC0S,kBAAkB,GAAG,MAAM;MAC/B,IAAI,CAAC1S,KAAK,CAAC2S,mBAAmB,IAAI3S,KAAK,CAACO,OAAO,CAACmS,kBAAkB,EAAE;QAClE1S,KAAK,CAAC2S,mBAAmB,GAAG3S,KAAK,CAACO,OAAO,CAACmS,kBAAkB,CAAC1S,KAAK,CAAC,CAAA;AACrE,OAAA;MAEA,IAAIA,KAAK,CAACO,OAAO,CAACqS,cAAc,IAAI,CAAC5S,KAAK,CAAC2S,mBAAmB,EAAE;AAC9D,QAAA,OAAO3S,KAAK,CAACyS,qBAAqB,EAAE,CAAA;AACtC,OAAA;AAEA,MAAA,OAAOzS,KAAK,CAAC2S,mBAAmB,EAAE,CAAA;KACnC,CAAA;GACF;AAED9K,EAAAA,SAAS,EAAEA,CACT5H,GAAe,EACfD,KAAmB,KACV;IACTC,GAAG,CAACwR,YAAY,GAAG,MAAM,CAAC,CAACxR,GAAG,CAAC4S,gBAAgB,CAAA;AAC/C5S,IAAAA,GAAG,CAACuR,gBAAgB,GAAGtR,QAAQ,IAAI;MACjC,IAAID,GAAG,CAAC6S,oBAAoB,CAAC1K,cAAc,CAAClI,QAAQ,CAAC,EAAE;AACrD,QAAA,OAAOD,GAAG,CAAC6S,oBAAoB,CAAC5S,QAAQ,CAAC,CAAA;AAC3C,OAAA;AAEA,MAAA,MAAM3E,MAAM,GAAGyE,KAAK,CAACqI,SAAS,CAACnI,QAAQ,CAAC,CAAA;MAExC,IAAI,EAAC3E,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEwF,SAAS,CAACyQ,gBAAgB,CAAE,EAAA;AACvC,QAAA,OAAOvR,GAAG,CAACK,QAAQ,CAACJ,QAAQ,CAAC,CAAA;AAC/B,OAAA;AAEAD,MAAAA,GAAG,CAAC6S,oBAAoB,CAAC5S,QAAQ,CAAC,GAAG3E,MAAM,CAACwF,SAAS,CAACyQ,gBAAgB,CACpEvR,GAAG,CAAC6H,QACN,CAAC,CAAA;AAED,MAAA,OAAO7H,GAAG,CAAC6S,oBAAoB,CAAC5S,QAAQ,CAAC,CAAA;KAC1C,CAAA;AACDD,IAAAA,GAAG,CAAC6S,oBAAoB,GAAG,EAAE,CAAA;GAC9B;EAED/S,UAAU,EAAEA,CACVM,IAAyB,EACzB9E,MAA6B,EAC7B0E,GAAe,EACfD,KAAmB,KACV;AAITK,IAAAA,IAAI,CAACoR,YAAY,GAAG,MAClBlW,MAAM,CAACkW,YAAY,EAAE,IAAIlW,MAAM,CAACkF,EAAE,KAAKR,GAAG,CAAC4S,gBAAgB,CAAA;AAC7DxS,IAAAA,IAAI,CAAC0S,gBAAgB,GAAG,MAAM,CAAC1S,IAAI,CAACoR,YAAY,EAAE,IAAIlW,MAAM,CAACkW,YAAY,EAAE,CAAA;IAC3EpR,IAAI,CAAC2S,eAAe,GAAG,MAAA;AAAA,MAAA,IAAAC,YAAA,CAAA;MAAA,OACrB,CAAC5S,IAAI,CAACoR,YAAY,EAAE,IAAI,CAACpR,IAAI,CAAC0S,gBAAgB,EAAE,IAAI,CAAC,EAAAE,CAAAA,YAAA,GAAChT,GAAG,CAAC+H,OAAO,KAAA,IAAA,IAAXiL,YAAA,CAAa1V,MAAM,CAAA,CAAA;AAAA,KAAA,CAAA;AAC7E,GAAA;AACF,EAAC;AAEM,SAAS+E,YAAYA,CAC1BE,WAAqC,EACrCyO,QAAkB,EAClBE,iBAAsC,EACtC;EACA,IAAI,EAACF,QAAQ,IAARA,IAAAA,IAAAA,QAAQ,CAAE1T,MAAM,CAAA,IAAI,CAAC4T,iBAAiB,EAAE;AAC3C,IAAA,OAAO3O,WAAW,CAAA;AACpB,GAAA;AAEA,EAAA,MAAM0Q,kBAAkB,GAAG1Q,WAAW,CAAC4B,MAAM,CAC3C+O,GAAG,IAAI,CAAClC,QAAQ,CAACvP,QAAQ,CAACyR,GAAG,CAAC1S,EAAE,CAClC,CAAC,CAAA;EAED,IAAI0Q,iBAAiB,KAAK,QAAQ,EAAE;AAClC,IAAA,OAAO+B,kBAAkB,CAAA;AAC3B,GAAA;EAEA,MAAME,eAAe,GAAGnC,QAAQ,CAC7B5N,GAAG,CAACgQ,CAAC,IAAI7Q,WAAW,CAAC2B,IAAI,CAACgP,GAAG,IAAIA,GAAG,CAAC1S,EAAE,KAAK4S,CAAC,CAAE,CAAC,CAChDjP,MAAM,CAACC,OAAO,CAAC,CAAA;AAElB,EAAA,OAAO,CAAC,GAAG+O,eAAe,EAAE,GAAGF,kBAAkB,CAAC,CAAA;AACpD;;AC7VA;;AAEO,MAAMI,cAA4B,GAAG;EAC1CpH,eAAe,EAAGC,KAAK,IAA4B;IACjD,OAAO;AACLoH,MAAAA,WAAW,EAAE,EAAE;MACf,GAAGpH,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfrM,KAAmB,IACW;IAC9B,OAAO;AACLwT,MAAAA,mBAAmB,EAAExX,gBAAgB,CAAC,aAAa,EAAEgE,KAAK,CAAA;KAC3D,CAAA;GACF;AAEDc,EAAAA,YAAY,EAAEA,CACZvF,MAA8B,EAC9ByE,KAAmB,KACV;AACTzE,IAAAA,MAAM,CAACkY,QAAQ,GAAGjW,IAAI,CACpBkW,QAAQ,IAAI,CAACC,sBAAsB,CAAC3T,KAAK,EAAE0T,QAAQ,CAAC,CAAC,EACrD1R,OAAO,IAAIA,OAAO,CAAC8L,SAAS,CAACxR,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,EACrDlB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,UAAU,CAC1D,CAAC,CAAA;AACDhF,IAAAA,MAAM,CAACqY,gBAAgB,GAAGF,QAAQ,IAAI;AAAA,MAAA,IAAAG,SAAA,CAAA;AACpC,MAAA,MAAM7R,OAAO,GAAG2R,sBAAsB,CAAC3T,KAAK,EAAE0T,QAAQ,CAAC,CAAA;AACvD,MAAA,OAAO,CAAAG,CAAAA,SAAA,GAAA7R,OAAO,CAAC,CAAC,CAAC,KAAV6R,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,SAAA,CAAYpT,EAAE,MAAKlF,MAAM,CAACkF,EAAE,CAAA;KACpC,CAAA;AACDlF,IAAAA,MAAM,CAACuY,eAAe,GAAGJ,QAAQ,IAAI;AAAA,MAAA,IAAAK,QAAA,CAAA;AACnC,MAAA,MAAM/R,OAAO,GAAG2R,sBAAsB,CAAC3T,KAAK,EAAE0T,QAAQ,CAAC,CAAA;AACvD,MAAA,OAAO,EAAAK,QAAA,GAAA/R,OAAO,CAACA,OAAO,CAACzE,MAAM,GAAG,CAAC,CAAC,qBAA3BwW,QAAA,CAA6BtT,EAAE,MAAKlF,MAAM,CAACkF,EAAE,CAAA;KACrD,CAAA;GACF;EAED8C,WAAW,EAA0BvD,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACgU,cAAc,GAAGnY,OAAO,IAC5BmE,KAAK,CAACO,OAAO,CAACiT,mBAAmB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAjCxT,KAAK,CAACO,OAAO,CAACiT,mBAAmB,CAAG3X,OAAO,CAAC,CAAA;AAC9CmE,IAAAA,KAAK,CAACiU,gBAAgB,GAAGrF,YAAY,IAAI;AAAA,MAAA,IAAAC,qBAAA,CAAA;AACvC7O,MAAAA,KAAK,CAACgU,cAAc,CAClBpF,YAAY,GAAG,EAAE,IAAAC,qBAAA,GAAG7O,KAAK,CAAC+O,YAAY,CAACwE,WAAW,YAAA1E,qBAAA,GAAI,EACxD,CAAC,CAAA;KACF,CAAA;AACD7O,IAAAA,KAAK,CAACqC,kBAAkB,GAAG7E,IAAI,CAC7B,MAAM,CACJwC,KAAK,CAAC2D,QAAQ,EAAE,CAAC4P,WAAW,EAC5BvT,KAAK,CAAC2D,QAAQ,EAAE,CAACsN,QAAQ,EACzBjR,KAAK,CAACO,OAAO,CAAC4Q,iBAAiB,CAChC,EACD,CAACoC,WAAW,EAAEtC,QAAQ,EAAEE,iBAAiB,KACtCnP,OAAiC,IAAK;AACrC;AACA;MACA,IAAIkS,cAAwC,GAAG,EAAE,CAAA;;AAEjD;AACA,MAAA,IAAI,EAACX,WAAW,IAAA,IAAA,IAAXA,WAAW,CAAEhW,MAAM,CAAE,EAAA;AACxB2W,QAAAA,cAAc,GAAGlS,OAAO,CAAA;AAC1B,OAAC,MAAM;AACL,QAAA,MAAMmS,eAAe,GAAG,CAAC,GAAGZ,WAAW,CAAC,CAAA;;AAExC;AACA,QAAA,MAAMa,WAAW,GAAG,CAAC,GAAGpS,OAAO,CAAC,CAAA;;AAEhC;;AAEA;AACA,QAAA,OAAOoS,WAAW,CAAC7W,MAAM,IAAI4W,eAAe,CAAC5W,MAAM,EAAE;AACnD,UAAA,MAAM8W,cAAc,GAAGF,eAAe,CAACG,KAAK,EAAE,CAAA;AAC9C,UAAA,MAAMC,UAAU,GAAGH,WAAW,CAACtG,SAAS,CACtCxR,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAK4T,cAChB,CAAC,CAAA;AACD,UAAA,IAAIE,UAAU,GAAG,CAAC,CAAC,EAAE;AACnBL,YAAAA,cAAc,CAAC7W,IAAI,CAAC+W,WAAW,CAACI,MAAM,CAACD,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC,CAAA;AAC5D,WAAA;AACF,SAAA;;AAEA;AACAL,QAAAA,cAAc,GAAG,CAAC,GAAGA,cAAc,EAAE,GAAGE,WAAW,CAAC,CAAA;AACtD,OAAA;AAEA,MAAA,OAAO9R,YAAY,CAAC4R,cAAc,EAAEjD,QAAQ,EAAEE,iBAAiB,CAAC,CAAA;KACjE,EACH5R,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,oBAAoB,CAClE,CAAC,CAAA;AACH,GAAA;AACF;;ACfA;;AAEA,MAAMkU,4BAA4B,GAAGA,OAA2B;AAC9D5Q,EAAAA,IAAI,EAAE,EAAE;AACRC,EAAAA,KAAK,EAAE,EAAA;AACT,CAAC,CAAC,CAAA;AAEK,MAAM4Q,aAA2B,GAAG;EACzCxI,eAAe,EAAGC,KAAK,IAA8B;IACnD,OAAO;MACLvI,aAAa,EAAE6Q,4BAA4B,EAAE;MAC7C,GAAGtI,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfrM,KAAmB,IACa;IAChC,OAAO;AACL2U,MAAAA,qBAAqB,EAAE3Y,gBAAgB,CAAC,eAAe,EAAEgE,KAAK,CAAA;KAC/D,CAAA;GACF;AAEDc,EAAAA,YAAY,EAAEA,CACZvF,MAA6B,EAC7ByE,KAAmB,KACV;AACTzE,IAAAA,MAAM,CAACqZ,GAAG,GAAGlB,QAAQ,IAAI;MACvB,MAAMmB,SAAS,GAAGtZ,MAAM,CACrB6G,cAAc,EAAE,CAChBiB,GAAG,CAAC/G,CAAC,IAAIA,CAAC,CAACmE,EAAE,CAAC,CACd2D,MAAM,CAACC,OAAO,CAAa,CAAA;AAE9BrE,MAAAA,KAAK,CAAC8U,gBAAgB,CAAC1Y,GAAG,IAAI;QAAA,IAAA2Y,UAAA,EAAAC,WAAA,CAAA;QAC5B,IAAItB,QAAQ,KAAK,OAAO,EAAE;UAAA,IAAAuB,SAAA,EAAAC,UAAA,CAAA;UACxB,OAAO;YACLrR,IAAI,EAAE,CAAAoR,CAAAA,SAAA,GAAC7Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEyH,IAAI,KAAAoR,IAAAA,GAAAA,SAAA,GAAI,EAAE,EAAE7Q,MAAM,CAAC9H,CAAC,IAAI,EAACuY,SAAS,IAAA,IAAA,IAATA,SAAS,CAAEnT,QAAQ,CAACpF,CAAC,CAAC,CAAC,CAAA;AAC5DwH,YAAAA,KAAK,EAAE,CACL,GAAG,CAAA,CAAAoR,UAAA,GAAC9Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE0H,KAAK,KAAAoR,IAAAA,GAAAA,UAAA,GAAI,EAAE,EAAE9Q,MAAM,CAAC9H,CAAC,IAAI,EAACuY,SAAS,YAATA,SAAS,CAAEnT,QAAQ,CAACpF,CAAC,CAAC,CAAC,CAAA,EAC1D,GAAGuY,SAAS,CAAA;WAEf,CAAA;AACH,SAAA;QAEA,IAAInB,QAAQ,KAAK,MAAM,EAAE;UAAA,IAAAyB,UAAA,EAAAC,WAAA,CAAA;UACvB,OAAO;AACLvR,YAAAA,IAAI,EAAE,CACJ,GAAG,CAAA,CAAAsR,UAAA,GAAC/Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEyH,IAAI,KAAAsR,IAAAA,GAAAA,UAAA,GAAI,EAAE,EAAE/Q,MAAM,CAAC9H,CAAC,IAAI,EAACuY,SAAS,YAATA,SAAS,CAAEnT,QAAQ,CAACpF,CAAC,CAAC,CAAA,CAAC,EACzD,GAAGuY,SAAS,CACb;YACD/Q,KAAK,EAAE,CAAAsR,CAAAA,WAAA,GAAChZ,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE0H,KAAK,KAAAsR,IAAAA,GAAAA,WAAA,GAAI,EAAE,EAAEhR,MAAM,CAAC9H,CAAC,IAAI,EAACuY,SAAS,IAATA,IAAAA,IAAAA,SAAS,CAAEnT,QAAQ,CAACpF,CAAC,CAAC,CAAA,CAAA;WAC9D,CAAA;AACH,SAAA;QAEA,OAAO;UACLuH,IAAI,EAAE,CAAAkR,CAAAA,UAAA,GAAC3Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEyH,IAAI,KAAAkR,IAAAA,GAAAA,UAAA,GAAI,EAAE,EAAE3Q,MAAM,CAAC9H,CAAC,IAAI,EAACuY,SAAS,IAAA,IAAA,IAATA,SAAS,CAAEnT,QAAQ,CAACpF,CAAC,CAAC,CAAC,CAAA;UAC5DwH,KAAK,EAAE,CAAAkR,CAAAA,WAAA,GAAC5Y,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE0H,KAAK,KAAAkR,IAAAA,GAAAA,WAAA,GAAI,EAAE,EAAE5Q,MAAM,CAAC9H,CAAC,IAAI,EAACuY,SAAS,IAATA,IAAAA,IAAAA,SAAS,CAAEnT,QAAQ,CAACpF,CAAC,CAAC,CAAA,CAAA;SAC9D,CAAA;AACH,OAAC,CAAC,CAAA;KACH,CAAA;IAEDf,MAAM,CAAC8Z,SAAS,GAAG,MAAM;AACvB,MAAA,MAAM7S,WAAW,GAAGjH,MAAM,CAAC6G,cAAc,EAAE,CAAA;AAE3C,MAAA,OAAOI,WAAW,CAACnE,IAAI,CACrB/B,CAAC,IAAA;AAAA,QAAA,IAAAgZ,qBAAA,EAAApU,IAAA,EAAAiM,qBAAA,CAAA;AAAA,QAAA,OACC,CAAAmI,CAAAA,qBAAA,GAAChZ,CAAC,CAACyE,SAAS,CAACwU,aAAa,KAAA,IAAA,GAAAD,qBAAA,GAAI,IAAI,MAAApU,CAAAA,IAAA,IAAAiM,qBAAA,GACjCnN,KAAK,CAACO,OAAO,CAACiV,mBAAmB,KAAA,IAAA,GAAArI,qBAAA,GAChCnN,KAAK,CAACO,OAAO,CAACgV,aAAa,KAAA,IAAA,GAAArU,IAAA,GAC3B,IAAI,CAAC,CAAA;AAAA,OACX,CAAC,CAAA;KACF,CAAA;IAED3F,MAAM,CAACka,WAAW,GAAG,MAAM;AACzB,MAAA,MAAMC,aAAa,GAAGna,MAAM,CAAC6G,cAAc,EAAE,CAACiB,GAAG,CAAC/G,CAAC,IAAIA,CAAC,CAACmE,EAAE,CAAC,CAAA;MAE5D,MAAM;QAAEoD,IAAI;AAAEC,QAAAA,KAAAA;AAAM,OAAC,GAAG9D,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAAA;AAEtD,MAAA,MAAM+R,MAAM,GAAGD,aAAa,CAACrX,IAAI,CAAC/B,CAAC,IAAIuH,IAAI,IAAA,IAAA,GAAA,KAAA,CAAA,GAAJA,IAAI,CAAEnC,QAAQ,CAACpF,CAAC,CAAC,CAAC,CAAA;AACzD,MAAA,MAAMsZ,OAAO,GAAGF,aAAa,CAACrX,IAAI,CAAC/B,CAAC,IAAIwH,KAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAEpC,QAAQ,CAACpF,CAAC,CAAC,CAAC,CAAA;MAE3D,OAAOqZ,MAAM,GAAG,MAAM,GAAGC,OAAO,GAAG,OAAO,GAAG,KAAK,CAAA;KACnD,CAAA;IAEDra,MAAM,CAACsa,cAAc,GAAG,MAAM;MAAA,IAAAlI,qBAAA,EAAAC,sBAAA,CAAA;AAC5B,MAAA,MAAM8F,QAAQ,GAAGnY,MAAM,CAACka,WAAW,EAAE,CAAA;AAErC,MAAA,OAAO/B,QAAQ,GAAA,CAAA/F,qBAAA,GAAA,CAAAC,sBAAA,GACX5N,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,KAAA,IAAA,IAAA,CAAAgK,sBAAA,GAA9BA,sBAAA,CAAiC8F,QAAQ,CAAC,KAA1C9F,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAA4CiE,OAAO,CAACtW,MAAM,CAACkF,EAAE,CAAC,YAAAkN,qBAAA,GAAI,CAAC,CAAC,GACpE,CAAC,CAAA;KACN,CAAA;GACF;AAED9F,EAAAA,SAAS,EAAEA,CACT5H,GAAe,EACfD,KAAmB,KACV;AACTC,IAAAA,GAAG,CAAC6V,qBAAqB,GAAGtY,IAAI,CAC9B,MAAM,CACJyC,GAAG,CAAC8V,mBAAmB,EAAE,EACzB/V,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,EACnC7D,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACoF,QAAQ,EAAErF,IAAI,EAAEC,KAAK,KAAK;AACzB,MAAA,MAAMkS,YAAsB,GAAG,CAAC,IAAInS,IAAI,IAAA,IAAA,GAAJA,IAAI,GAAI,EAAE,GAAG,IAAIC,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,EAAE,EAAE,CAAA;AAElE,MAAA,OAAOoF,QAAQ,CAAC9E,MAAM,CAAC9H,CAAC,IAAI,CAAC0Z,YAAY,CAACtU,QAAQ,CAACpF,CAAC,CAACf,MAAM,CAACkF,EAAE,CAAC,CAAC,CAAA;KACjE,EACDlB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,uBAAuB,CACpE,CAAC,CAAA;AACDN,IAAAA,GAAG,CAACgW,mBAAmB,GAAGzY,IAAI,CAC5B,MAAM,CAACyC,GAAG,CAAC8V,mBAAmB,EAAE,EAAE/V,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,CAAC,EACtE,CAACqF,QAAQ,EAAErF,IAAI,KAAK;AAClB,MAAA,MAAMqS,KAAK,GAAG,CAACrS,IAAI,IAAA,IAAA,GAAJA,IAAI,GAAI,EAAE,EACtBR,GAAG,CAACnD,QAAQ,IAAIgJ,QAAQ,CAAC/E,IAAI,CAAC9D,IAAI,IAAIA,IAAI,CAAC9E,MAAM,CAACkF,EAAE,KAAKP,QAAQ,CAAE,CAAC,CACpEkE,MAAM,CAACC,OAAO,CAAC,CACfhB,GAAG,CAAC/G,CAAC,KAAK;AAAE,QAAA,GAAGA,CAAC;AAAEoX,QAAAA,QAAQ,EAAE,MAAA;AAAO,OAAC,CAAyB,CAAC,CAAA;AAEjE,MAAA,OAAOwC,KAAK,CAAA;KACb,EACD3W,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,qBAAqB,CAClE,CAAC,CAAA;AACDN,IAAAA,GAAG,CAACkW,oBAAoB,GAAG3Y,IAAI,CAC7B,MAAM,CAACyC,GAAG,CAAC8V,mBAAmB,EAAE,EAAE/V,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CAAC,EACvE,CAACoF,QAAQ,EAAEpF,KAAK,KAAK;AACnB,MAAA,MAAMoS,KAAK,GAAG,CAACpS,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,EAAE,EACvBT,GAAG,CAACnD,QAAQ,IAAIgJ,QAAQ,CAAC/E,IAAI,CAAC9D,IAAI,IAAIA,IAAI,CAAC9E,MAAM,CAACkF,EAAE,KAAKP,QAAQ,CAAE,CAAC,CACpEkE,MAAM,CAACC,OAAO,CAAC,CACfhB,GAAG,CAAC/G,CAAC,KAAK;AAAE,QAAA,GAAGA,CAAC;AAAEoX,QAAAA,QAAQ,EAAE,OAAA;AAAQ,OAAC,CAAyB,CAAC,CAAA;AAElE,MAAA,OAAOwC,KAAK,CAAA;KACb,EACD3W,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,sBAAsB,CACnE,CAAC,CAAA;GACF;EAEDgD,WAAW,EAA0BvD,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAAC8U,gBAAgB,GAAGjZ,OAAO,IAC9BmE,KAAK,CAACO,OAAO,CAACoU,qBAAqB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAnC3U,KAAK,CAACO,OAAO,CAACoU,qBAAqB,CAAG9Y,OAAO,CAAC,CAAA;IAEhDmE,KAAK,CAACoW,kBAAkB,GAAGxH,YAAY,IAAA;MAAA,IAAAC,qBAAA,EAAAC,mBAAA,CAAA;MAAA,OACrC9O,KAAK,CAAC8U,gBAAgB,CACpBlG,YAAY,GACR6F,4BAA4B,EAAE,GAAA5F,CAAAA,qBAAA,GAAAC,CAAAA,mBAAA,GAC9B9O,KAAK,CAAC+O,YAAY,KAAlBD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoBlL,aAAa,KAAAiL,IAAAA,GAAAA,qBAAA,GAAI4F,4BAA4B,EACvE,CAAC,CAAA;AAAA,KAAA,CAAA;AAEHzU,IAAAA,KAAK,CAACqW,sBAAsB,GAAG3C,QAAQ,IAAI;AAAA,MAAA,IAAA4C,qBAAA,CAAA;MACzC,MAAMC,YAAY,GAAGvW,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAAA;MAEnD,IAAI,CAAC8P,QAAQ,EAAE;QAAA,IAAA8C,kBAAA,EAAAC,mBAAA,CAAA;QACb,OAAOpS,OAAO,CAAC,CAAAmS,CAAAA,kBAAA,GAAAD,YAAY,CAAC1S,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjB2S,kBAAA,CAAmBjZ,MAAM,MAAAkZ,CAAAA,mBAAA,GAAIF,YAAY,CAACzS,KAAK,KAAlB2S,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoBlZ,MAAM,CAAC,CAAA,CAAA;AACzE,OAAA;AACA,MAAA,OAAO8G,OAAO,CAAA,CAAAiS,qBAAA,GAACC,YAAY,CAAC7C,QAAQ,CAAC,KAAtB4C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAwB/Y,MAAM,CAAC,CAAA;KAC/C,CAAA;AAEDyC,IAAAA,KAAK,CAAC0W,kBAAkB,GAAGlZ,IAAI,CAC7B,MAAM,CAACwC,KAAK,CAACgJ,iBAAiB,EAAE,EAAEhJ,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,CAAC,EACtE,CAACE,UAAU,EAAEF,IAAI,KAAK;AACpB,MAAA,OAAO,CAACA,IAAI,IAAJA,IAAAA,GAAAA,IAAI,GAAI,EAAE,EACfR,GAAG,CAACnD,QAAQ,IAAI6D,UAAU,CAACI,IAAI,CAAC5I,MAAM,IAAIA,MAAM,CAACkF,EAAE,KAAKP,QAAQ,CAAE,CAAC,CACnEkE,MAAM,CAACC,OAAO,CAAC,CAAA;KACnB,EACD9E,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,oBAAoB,CACpE,CAAC,CAAA;AAEDP,IAAAA,KAAK,CAAC2W,mBAAmB,GAAGnZ,IAAI,CAC9B,MAAM,CAACwC,KAAK,CAACgJ,iBAAiB,EAAE,EAAEhJ,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CAAC,EACvE,CAACC,UAAU,EAAED,KAAK,KAAK;AACrB,MAAA,OAAO,CAACA,KAAK,IAALA,IAAAA,GAAAA,KAAK,GAAI,EAAE,EAChBT,GAAG,CAACnD,QAAQ,IAAI6D,UAAU,CAACI,IAAI,CAAC5I,MAAM,IAAIA,MAAM,CAACkF,EAAE,KAAKP,QAAQ,CAAE,CAAC,CACnEkE,MAAM,CAACC,OAAO,CAAC,CAAA;KACnB,EACD9E,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,qBAAqB,CACrE,CAAC,CAAA;AAEDP,IAAAA,KAAK,CAAC4W,oBAAoB,GAAGpZ,IAAI,CAC/B,MAAM,CACJwC,KAAK,CAACgJ,iBAAiB,EAAE,EACzBhJ,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,EACnC7D,KAAK,CAAC2D,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACC,UAAU,EAAEF,IAAI,EAAEC,KAAK,KAAK;AAC3B,MAAA,MAAMkS,YAAsB,GAAG,CAAC,IAAInS,IAAI,IAAA,IAAA,GAAJA,IAAI,GAAI,EAAE,GAAG,IAAIC,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,EAAE,EAAE,CAAA;AAElE,MAAA,OAAOC,UAAU,CAACK,MAAM,CAAC9H,CAAC,IAAI,CAAC0Z,YAAY,CAACtU,QAAQ,CAACpF,CAAC,CAACmE,EAAE,CAAC,CAAC,CAAA;KAC5D,EACDlB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,sBAAsB,CACtE,CAAC,CAAA;AACH,GAAA;AACF;;AClUA;;AA2MA;;AAEO,MAAMsW,mBAAmB,GAAG;AACjCrG,EAAAA,IAAI,EAAE,GAAG;AACTsG,EAAAA,OAAO,EAAE,EAAE;EACXC,OAAO,EAAErL,MAAM,CAACsL,gBAAAA;AAClB,EAAC;AAED,MAAMC,+BAA+B,GAAGA,OAA8B;AACpEC,EAAAA,WAAW,EAAE,IAAI;AACjBC,EAAAA,SAAS,EAAE,IAAI;AACfC,EAAAA,WAAW,EAAE,IAAI;AACjBC,EAAAA,eAAe,EAAE,IAAI;AACrBC,EAAAA,gBAAgB,EAAE,KAAK;AACvBC,EAAAA,iBAAiB,EAAE,EAAA;AACrB,CAAC,CAAC,CAAA;AAEK,MAAMC,YAA0B,GAAG;EACxCxL,mBAAmB,EAAEA,MAA6B;AAChD,IAAA,OAAO6K,mBAAmB,CAAA;GAC3B;EACD3K,eAAe,EAAGC,KAAK,IAA6B;IAClD,OAAO;MACLsL,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAET,+BAA+B,EAAE;MACnD,GAAG9K,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfrM,KAAmB,IACY;IAC/B,OAAO;AACL2X,MAAAA,gBAAgB,EAAE,OAAO;AACzBC,MAAAA,qBAAqB,EAAE,KAAK;AAC5BC,MAAAA,oBAAoB,EAAE7b,gBAAgB,CAAC,cAAc,EAAEgE,KAAK,CAAC;AAC7D8X,MAAAA,wBAAwB,EAAE9b,gBAAgB,CAAC,kBAAkB,EAAEgE,KAAK,CAAA;KACrE,CAAA;GACF;AAEDc,EAAAA,YAAY,EAAEA,CACZvF,MAA6B,EAC7ByE,KAAmB,KACV;IACTzE,MAAM,CAACwc,OAAO,GAAG,MAAM;AAAA,MAAA,IAAAC,qBAAA,EAAA9W,IAAA,EAAA+W,qBAAA,CAAA;AACrB,MAAA,MAAMC,UAAU,GAAGlY,KAAK,CAAC2D,QAAQ,EAAE,CAAC8T,YAAY,CAAClc,MAAM,CAACkF,EAAE,CAAC,CAAA;MAE3D,OAAO9B,IAAI,CAACW,GAAG,CACbX,IAAI,CAACU,GAAG,CAAA,CAAA2Y,qBAAA,GACNzc,MAAM,CAACwF,SAAS,CAAC+V,OAAO,KAAAkB,IAAAA,GAAAA,qBAAA,GAAInB,mBAAmB,CAACC,OAAO,EAAA5V,CAAAA,IAAA,GACvDgX,UAAU,IAAVA,IAAAA,GAAAA,UAAU,GAAI3c,MAAM,CAACwF,SAAS,CAACyP,IAAI,KAAA,IAAA,GAAAtP,IAAA,GAAI2V,mBAAmB,CAACrG,IAC7D,CAAC,EAAA,CAAAyH,qBAAA,GACD1c,MAAM,CAACwF,SAAS,CAACgW,OAAO,KAAAkB,IAAAA,GAAAA,qBAAA,GAAIpB,mBAAmB,CAACE,OAClD,CAAC,CAAA;KACF,CAAA;AAEDxb,IAAAA,MAAM,CAAC4c,QAAQ,GAAG3a,IAAI,CACpBkW,QAAQ,IAAI,CACVA,QAAQ,EACRC,sBAAsB,CAAC3T,KAAK,EAAE0T,QAAQ,CAAC,EACvC1T,KAAK,CAAC2D,QAAQ,EAAE,CAAC8T,YAAY,CAC9B,EACD,CAAC/D,QAAQ,EAAE1R,OAAO,KAChBA,OAAO,CACJoW,KAAK,CAAC,CAAC,EAAE7c,MAAM,CAACkY,QAAQ,CAACC,QAAQ,CAAC,CAAC,CACnCvK,MAAM,CAAC,CAACgG,GAAG,EAAE5T,MAAM,KAAK4T,GAAG,GAAG5T,MAAM,CAACwc,OAAO,EAAE,EAAE,CAAC,CAAC,EACvDxY,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,UAAU,CAC1D,CAAC,CAAA;AAEDhF,IAAAA,MAAM,CAAC8c,QAAQ,GAAG7a,IAAI,CACpBkW,QAAQ,IAAI,CACVA,QAAQ,EACRC,sBAAsB,CAAC3T,KAAK,EAAE0T,QAAQ,CAAC,EACvC1T,KAAK,CAAC2D,QAAQ,EAAE,CAAC8T,YAAY,CAC9B,EACD,CAAC/D,QAAQ,EAAE1R,OAAO,KAChBA,OAAO,CACJoW,KAAK,CAAC7c,MAAM,CAACkY,QAAQ,CAACC,QAAQ,CAAC,GAAG,CAAC,CAAC,CACpCvK,MAAM,CAAC,CAACgG,GAAG,EAAE5T,MAAM,KAAK4T,GAAG,GAAG5T,MAAM,CAACwc,OAAO,EAAE,EAAE,CAAC,CAAC,EACvDxY,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAE,UAAU,CAC1D,CAAC,CAAA;IAEDhF,MAAM,CAAC+c,SAAS,GAAG,MAAM;AACvBtY,MAAAA,KAAK,CAACuY,eAAe,CAACC,KAAA,IAAiC;QAAA,IAAhC;AAAE,UAAA,CAACjd,MAAM,CAACkF,EAAE,GAAGgY,CAAC;UAAE,GAAGC,IAAAA;AAAK,SAAC,GAAAF,KAAA,CAAA;AAChD,QAAA,OAAOE,IAAI,CAAA;AACb,OAAC,CAAC,CAAA;KACH,CAAA;IACDnd,MAAM,CAACod,YAAY,GAAG,MAAM;MAAA,IAAAzL,qBAAA,EAAAC,qBAAA,CAAA;MAC1B,OACE,CAAA,CAAAD,qBAAA,GAAC3R,MAAM,CAACwF,SAAS,CAAC6X,cAAc,KAAA1L,IAAAA,GAAAA,qBAAA,GAAI,IAAI,OAAAC,qBAAA,GACvCnN,KAAK,CAACO,OAAO,CAACsY,oBAAoB,KAAA1L,IAAAA,GAAAA,qBAAA,GAAI,IAAI,CAAC,CAAA;KAE/C,CAAA;IACD5R,MAAM,CAACud,aAAa,GAAG,MAAM;AAC3B,MAAA,OAAO9Y,KAAK,CAAC2D,QAAQ,EAAE,CAAC+T,gBAAgB,CAACJ,gBAAgB,KAAK/b,MAAM,CAACkF,EAAE,CAAA;KACxE,CAAA;GACF;AAEDgC,EAAAA,YAAY,EAAEA,CACZhB,MAA6B,EAC7BzB,KAAmB,KACV;IACTyB,MAAM,CAACsW,OAAO,GAAG,MAAM;MACrB,IAAI5I,GAAG,GAAG,CAAC,CAAA;MAEX,MAAMlS,OAAO,GAAIwE,MAA6B,IAAK;AACjD,QAAA,IAAIA,MAAM,CAACoB,UAAU,CAACtF,MAAM,EAAE;AAC5BkE,UAAAA,MAAM,CAACoB,UAAU,CAAC1F,OAAO,CAACF,OAAO,CAAC,CAAA;AACpC,SAAC,MAAM;AAAA,UAAA,IAAA8b,qBAAA,CAAA;AACL5J,UAAAA,GAAG,IAAA4J,CAAAA,qBAAA,GAAItX,MAAM,CAAClG,MAAM,CAACwc,OAAO,EAAE,KAAAgB,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;AACrC,SAAA;OACD,CAAA;MAED9b,OAAO,CAACwE,MAAM,CAAC,CAAA;AAEf,MAAA,OAAO0N,GAAG,CAAA;KACX,CAAA;IACD1N,MAAM,CAAC0W,QAAQ,GAAG,MAAM;AACtB,MAAA,IAAI1W,MAAM,CAAClD,KAAK,GAAG,CAAC,EAAE;AACpB,QAAA,MAAMya,iBAAiB,GAAGvX,MAAM,CAACuB,WAAW,CAACsC,OAAO,CAAC7D,MAAM,CAAClD,KAAK,GAAG,CAAC,CAAE,CAAA;QACvE,OAAOya,iBAAiB,CAACb,QAAQ,EAAE,GAAGa,iBAAiB,CAACjB,OAAO,EAAE,CAAA;AACnE,OAAA;AAEA,MAAA,OAAO,CAAC,CAAA;KACT,CAAA;AACDtW,IAAAA,MAAM,CAACwX,gBAAgB,GAAGC,gBAAgB,IAAI;MAC5C,MAAM3d,MAAM,GAAGyE,KAAK,CAACqI,SAAS,CAAC5G,MAAM,CAAClG,MAAM,CAACkF,EAAE,CAAC,CAAA;MAChD,MAAM0Y,SAAS,GAAG5d,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAEod,YAAY,EAAE,CAAA;AAExC,MAAA,OAAQS,CAAU,IAAK;AACrB,QAAA,IAAI,CAAC7d,MAAM,IAAI,CAAC4d,SAAS,EAAE;AACzB,UAAA,OAAA;AACF,SAAA;AAEEC,QAAAA,CAAC,CAASC,OAAO,IAAA,IAAA,IAAjBD,CAAC,CAASC,OAAO,EAAI,CAAA;AAEvB,QAAA,IAAIC,iBAAiB,CAACF,CAAC,CAAC,EAAE;AACxB;UACA,IAAIA,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACG,OAAO,CAAChc,MAAM,GAAG,CAAC,EAAE;AACrC,YAAA,OAAA;AACF,WAAA;AACF,SAAA;AAEA,QAAA,MAAM4Z,SAAS,GAAG1V,MAAM,CAACsW,OAAO,EAAE,CAAA;AAElC,QAAA,MAAMR,iBAAqC,GAAG9V,MAAM,GAChDA,MAAM,CAACwB,cAAc,EAAE,CAACI,GAAG,CAAC/G,CAAC,IAAI,CAACA,CAAC,CAACf,MAAM,CAACkF,EAAE,EAAEnE,CAAC,CAACf,MAAM,CAACwc,OAAO,EAAE,CAAC,CAAC,GACnE,CAAC,CAACxc,MAAM,CAACkF,EAAE,EAAElF,MAAM,CAACwc,OAAO,EAAE,CAAC,CAAC,CAAA;QAEnC,MAAMyB,OAAO,GAAGF,iBAAiB,CAACF,CAAC,CAAC,GAChCza,IAAI,CAACC,KAAK,CAACwa,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAEC,OAAO,CAAC,GAChCJ,CAAC,CAAgBI,OAAO,CAAA;QAE7B,MAAMC,eAAkC,GAAG,EAAE,CAAA;AAE7C,QAAA,MAAMC,YAAY,GAAGA,CACnBC,SAAyB,EACzBC,UAAmB,KAChB;AACH,UAAA,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;AAClC,YAAA,OAAA;AACF,WAAA;AAEA5Z,UAAAA,KAAK,CAAC6Z,mBAAmB,CAACzd,GAAG,IAAI;YAAA,IAAA0d,gBAAA,EAAAC,cAAA,CAAA;AAC/B,YAAA,MAAMC,cAAc,GAClBha,KAAK,CAACO,OAAO,CAACqX,qBAAqB,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AACxD,YAAA,MAAMR,WAAW,GACf,CAACwC,UAAU,IAAAE,CAAAA,gBAAA,GAAI1d,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAE8a,WAAW,KAAA4C,IAAAA,GAAAA,gBAAA,GAAI,CAAC,CAAC,IAAIE,cAAc,CAAA;YACzD,MAAM3C,eAAe,GAAG1Y,IAAI,CAACU,GAAG,CAC9B+X,WAAW,IAAA2C,CAAAA,cAAA,GAAI3d,GAAG,oBAAHA,GAAG,CAAE+a,SAAS,KAAA,IAAA,GAAA4C,cAAA,GAAI,CAAC,CAAC,EACnC,CAAC,QACH,CAAC,CAAA;AAED3d,YAAAA,GAAG,CAACmb,iBAAiB,CAACpa,OAAO,CAAC8c,KAAA,IAA4B;AAAA,cAAA,IAA3B,CAAC/Z,QAAQ,EAAEga,UAAU,CAAC,GAAAD,KAAA,CAAA;cACnDR,eAAe,CAACvZ,QAAQ,CAAC,GACvBvB,IAAI,CAACC,KAAK,CACRD,IAAI,CAACU,GAAG,CAAC6a,UAAU,GAAGA,UAAU,GAAG7C,eAAe,EAAE,CAAC,CAAC,GAAG,GAC3D,CAAC,GAAG,GAAG,CAAA;AACX,aAAC,CAAC,CAAA;YAEF,OAAO;AACL,cAAA,GAAGjb,GAAG;cACNgb,WAAW;AACXC,cAAAA,eAAAA;aACD,CAAA;AACH,WAAC,CAAC,CAAA;UAEF,IACErX,KAAK,CAACO,OAAO,CAACoX,gBAAgB,KAAK,UAAU,IAC7CgC,SAAS,KAAK,KAAK,EACnB;AACA3Z,YAAAA,KAAK,CAACuY,eAAe,CAACnc,GAAG,KAAK;AAC5B,cAAA,GAAGA,GAAG;cACN,GAAGqd,eAAAA;AACL,aAAC,CAAC,CAAC,CAAA;AACL,WAAA;SACD,CAAA;QAED,MAAMU,MAAM,GAAIP,UAAmB,IAAKF,YAAY,CAAC,MAAM,EAAEE,UAAU,CAAC,CAAA;QAExE,MAAMQ,KAAK,GAAIR,UAAmB,IAAK;AACrCF,UAAAA,YAAY,CAAC,KAAK,EAAEE,UAAU,CAAC,CAAA;AAE/B5Z,UAAAA,KAAK,CAAC6Z,mBAAmB,CAACzd,GAAG,KAAK;AAChC,YAAA,GAAGA,GAAG;AACNkb,YAAAA,gBAAgB,EAAE,KAAK;AACvBJ,YAAAA,WAAW,EAAE,IAAI;AACjBC,YAAAA,SAAS,EAAE,IAAI;AACfC,YAAAA,WAAW,EAAE,IAAI;AACjBC,YAAAA,eAAe,EAAE,IAAI;AACrBE,YAAAA,iBAAiB,EAAE,EAAA;AACrB,WAAC,CAAC,CAAC,CAAA;SACJ,CAAA;QAED,MAAM8C,eAAe,GACnBnB,gBAAgB,IAAI,OAAOoB,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG,IAAI,CAAA;AAEvE,QAAA,MAAMC,WAAW,GAAG;UAClBC,WAAW,EAAGpB,CAAa,IAAKe,MAAM,CAACf,CAAC,CAACI,OAAO,CAAC;UACjDiB,SAAS,EAAGrB,CAAa,IAAK;YAC5BiB,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEK,mBAAmB,CAClC,WAAW,EACXH,WAAW,CAACC,WACd,CAAC,CAAA;YACDH,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEK,mBAAmB,CAClC,SAAS,EACTH,WAAW,CAACE,SACd,CAAC,CAAA;AACDL,YAAAA,KAAK,CAAChB,CAAC,CAACI,OAAO,CAAC,CAAA;AAClB,WAAA;SACD,CAAA;AAED,QAAA,MAAMmB,WAAW,GAAG;UAClBH,WAAW,EAAGpB,CAAa,IAAK;YAC9B,IAAIA,CAAC,CAACwB,UAAU,EAAE;cAChBxB,CAAC,CAACyB,cAAc,EAAE,CAAA;cAClBzB,CAAC,CAAC0B,eAAe,EAAE,CAAA;AACrB,aAAA;YACAX,MAAM,CAACf,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAEC,OAAO,CAAC,CAAA;AAC7B,YAAA,OAAO,KAAK,CAAA;WACb;UACDiB,SAAS,EAAGrB,CAAa,IAAK;AAAA,YAAA,IAAA2B,WAAA,CAAA;YAC5BV,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEK,mBAAmB,CAClC,WAAW,EACXC,WAAW,CAACH,WACd,CAAC,CAAA;YACDH,eAAe,IAAA,IAAA,IAAfA,eAAe,CAAEK,mBAAmB,CAClC,UAAU,EACVC,WAAW,CAACF,SACd,CAAC,CAAA;YACD,IAAIrB,CAAC,CAACwB,UAAU,EAAE;cAChBxB,CAAC,CAACyB,cAAc,EAAE,CAAA;cAClBzB,CAAC,CAAC0B,eAAe,EAAE,CAAA;AACrB,aAAA;AACAV,YAAAA,KAAK,CAAAW,CAAAA,WAAA,GAAC3B,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAZwB,WAAA,CAAcvB,OAAO,CAAC,CAAA;AAC9B,WAAA;SACD,CAAA;AAED,QAAA,MAAMwB,kBAAkB,GAAGC,qBAAqB,EAAE,GAC9C;AAAEC,UAAAA,OAAO,EAAE,KAAA;AAAM,SAAC,GAClB,KAAK,CAAA;AAET,QAAA,IAAI5B,iBAAiB,CAACF,CAAC,CAAC,EAAE;AACxBiB,UAAAA,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEc,gBAAgB,CAC/B,WAAW,EACXR,WAAW,CAACH,WAAW,EACvBQ,kBACF,CAAC,CAAA;AACDX,UAAAA,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEc,gBAAgB,CAC/B,UAAU,EACVR,WAAW,CAACF,SAAS,EACrBO,kBACF,CAAC,CAAA;AACH,SAAC,MAAM;AACLX,UAAAA,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEc,gBAAgB,CAC/B,WAAW,EACXZ,WAAW,CAACC,WAAW,EACvBQ,kBACF,CAAC,CAAA;AACDX,UAAAA,eAAe,IAAfA,IAAAA,IAAAA,eAAe,CAAEc,gBAAgB,CAC/B,SAAS,EACTZ,WAAW,CAACE,SAAS,EACrBO,kBACF,CAAC,CAAA;AACH,SAAA;AAEAhb,QAAAA,KAAK,CAAC6Z,mBAAmB,CAACzd,GAAG,KAAK;AAChC,UAAA,GAAGA,GAAG;AACN8a,UAAAA,WAAW,EAAEsC,OAAO;UACpBrC,SAAS;AACTC,UAAAA,WAAW,EAAE,CAAC;AACdC,UAAAA,eAAe,EAAE,CAAC;UAClBE,iBAAiB;UACjBD,gBAAgB,EAAE/b,MAAM,CAACkF,EAAAA;AAC3B,SAAC,CAAC,CAAC,CAAA;OACJ,CAAA;KACF,CAAA;GACF;EAED8C,WAAW,EAA0BvD,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACuY,eAAe,GAAG1c,OAAO,IAC7BmE,KAAK,CAACO,OAAO,CAACsX,oBAAoB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAlC7X,KAAK,CAACO,OAAO,CAACsX,oBAAoB,CAAGhc,OAAO,CAAC,CAAA;AAC/CmE,IAAAA,KAAK,CAAC6Z,mBAAmB,GAAGhe,OAAO,IACjCmE,KAAK,CAACO,OAAO,CAACuX,wBAAwB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAtC9X,KAAK,CAACO,OAAO,CAACuX,wBAAwB,CAAGjc,OAAO,CAAC,CAAA;AACnDmE,IAAAA,KAAK,CAACob,iBAAiB,GAAGxM,YAAY,IAAI;AAAA,MAAA,IAAAC,qBAAA,CAAA;MACxC7O,KAAK,CAACuY,eAAe,CACnB3J,YAAY,GAAG,EAAE,IAAAC,qBAAA,GAAG7O,KAAK,CAAC+O,YAAY,CAAC0I,YAAY,KAAA,IAAA,GAAA5I,qBAAA,GAAI,EACzD,CAAC,CAAA;KACF,CAAA;AACD7O,IAAAA,KAAK,CAACqb,mBAAmB,GAAGzM,YAAY,IAAI;AAAA,MAAA,IAAA0M,sBAAA,CAAA;MAC1Ctb,KAAK,CAAC6Z,mBAAmB,CACvBjL,YAAY,GACRqI,+BAA+B,EAAE,GAAA,CAAAqE,sBAAA,GACjCtb,KAAK,CAAC+O,YAAY,CAAC2I,gBAAgB,KAAA,IAAA,GAAA4D,sBAAA,GACjCrE,+BAA+B,EACvC,CAAC,CAAA;KACF,CAAA;IACDjX,KAAK,CAACub,YAAY,GAAG,MAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAAA,OAAAD,CAAAA,qBAAA,IAAAC,sBAAA,GACnBzb,KAAK,CAACwD,eAAe,EAAE,CAAC,CAAC,CAAC,KAA1BiY,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAA4BnW,OAAO,CAAC6D,MAAM,CAAC,CAACgG,GAAG,EAAE1N,MAAM,KAAK;AAC1D,QAAA,OAAO0N,GAAG,GAAG1N,MAAM,CAACsW,OAAO,EAAE,CAAA;AAC/B,OAAC,EAAE,CAAC,CAAC,KAAAyD,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;AAAA,KAAA,CAAA;IACZxb,KAAK,CAAC0b,gBAAgB,GAAG,MAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAAA,OAAAD,CAAAA,qBAAA,IAAAC,sBAAA,GACvB5b,KAAK,CAAC2E,mBAAmB,EAAE,CAAC,CAAC,CAAC,KAA9BiX,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAgCtW,OAAO,CAAC6D,MAAM,CAAC,CAACgG,GAAG,EAAE1N,MAAM,KAAK;AAC9D,QAAA,OAAO0N,GAAG,GAAG1N,MAAM,CAACsW,OAAO,EAAE,CAAA;AAC/B,OAAC,EAAE,CAAC,CAAC,KAAA4D,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;AAAA,KAAA,CAAA;IACZ3b,KAAK,CAAC6b,kBAAkB,GAAG,MAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAAA,OAAAD,CAAAA,qBAAA,IAAAC,sBAAA,GACzB/b,KAAK,CAAC0E,qBAAqB,EAAE,CAAC,CAAC,CAAC,KAAhCqX,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAkCzW,OAAO,CAAC6D,MAAM,CAAC,CAACgG,GAAG,EAAE1N,MAAM,KAAK;AAChE,QAAA,OAAO0N,GAAG,GAAG1N,MAAM,CAACsW,OAAO,EAAE,CAAA;AAC/B,OAAC,EAAE,CAAC,CAAC,KAAA+D,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;AAAA,KAAA,CAAA;IACZ9b,KAAK,CAACgc,iBAAiB,GAAG,MAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAAA,OAAAD,CAAAA,qBAAA,IAAAC,sBAAA,GACxBlc,KAAK,CAAC8E,oBAAoB,EAAE,CAAC,CAAC,CAAC,KAA/BoX,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAiC5W,OAAO,CAAC6D,MAAM,CAAC,CAACgG,GAAG,EAAE1N,MAAM,KAAK;AAC/D,QAAA,OAAO0N,GAAG,GAAG1N,MAAM,CAACsW,OAAO,EAAE,CAAA;AAC/B,OAAC,EAAE,CAAC,CAAC,KAAAkE,IAAAA,GAAAA,qBAAA,GAAI,CAAC,CAAA;AAAA,KAAA,CAAA;AACd,GAAA;AACF,EAAC;AAED,IAAIE,gBAAgC,GAAG,IAAI,CAAA;AACpC,SAASlB,qBAAqBA,GAAG;AACtC,EAAA,IAAI,OAAOkB,gBAAgB,KAAK,SAAS,EAAE,OAAOA,gBAAgB,CAAA;EAElE,IAAIC,SAAS,GAAG,KAAK,CAAA;EACrB,IAAI;AACF,IAAA,MAAM7b,OAAO,GAAG;MACd,IAAI2a,OAAOA,GAAG;AACZkB,QAAAA,SAAS,GAAG,IAAI,CAAA;AAChB,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;KACD,CAAA;AAED,IAAA,MAAMrgB,IAAI,GAAGA,MAAM,EAAE,CAAA;IAErBsgB,MAAM,CAAClB,gBAAgB,CAAC,MAAM,EAAEpf,IAAI,EAAEwE,OAAO,CAAC,CAAA;AAC9C8b,IAAAA,MAAM,CAAC3B,mBAAmB,CAAC,MAAM,EAAE3e,IAAI,CAAC,CAAA;GACzC,CAAC,OAAOugB,GAAG,EAAE;AACZF,IAAAA,SAAS,GAAG,KAAK,CAAA;AACnB,GAAA;AACAD,EAAAA,gBAAgB,GAAGC,SAAS,CAAA;AAC5B,EAAA,OAAOD,gBAAgB,CAAA;AACzB,CAAA;AAEA,SAAS7C,iBAAiBA,CAACF,CAAU,EAAmB;AACtD,EAAA,OAAQA,CAAC,CAAgBmD,IAAI,KAAK,YAAY,CAAA;AAChD;;AC7aA;;AAEO,MAAMC,gBAA8B,GAAG;EAC5CtQ,eAAe,EAAGC,KAAK,IAA2B;IAChD,OAAO;MACLsQ,gBAAgB,EAAE,EAAE;MACpB,GAAGtQ,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfrM,KAAmB,IACU;IAC7B,OAAO;AACL0c,MAAAA,wBAAwB,EAAE1gB,gBAAgB,CAAC,kBAAkB,EAAEgE,KAAK,CAAA;KACrE,CAAA;GACF;AAEDc,EAAAA,YAAY,EAAEA,CACZvF,MAA6B,EAC7ByE,KAAmB,KACV;AACTzE,IAAAA,MAAM,CAACohB,gBAAgB,GAAG9P,KAAK,IAAI;AACjC,MAAA,IAAItR,MAAM,CAACqhB,UAAU,EAAE,EAAE;AACvB5c,QAAAA,KAAK,CAAC6c,mBAAmB,CAACzgB,GAAG,KAAK;AAChC,UAAA,GAAGA,GAAG;AACN,UAAA,CAACb,MAAM,CAACkF,EAAE,GAAGoM,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,CAACtR,MAAM,CAACuL,YAAY,EAAC;AAC7C,SAAC,CAAC,CAAC,CAAA;AACL,OAAA;KACD,CAAA;IACDvL,MAAM,CAACuL,YAAY,GAAG,MAAM;MAAA,IAAA5F,IAAA,EAAAyM,qBAAA,CAAA;AAC1B,MAAA,MAAMmP,YAAY,GAAGvhB,MAAM,CAACyG,OAAO,CAAA;AACnC,MAAA,OAAA,CAAAd,IAAA,GACG4b,YAAY,CAACvf,MAAM,GAChBuf,YAAY,CAACze,IAAI,CAAC0e,CAAC,IAAIA,CAAC,CAACjW,YAAY,EAAE,CAAC,GAAA,CAAA6G,qBAAA,GACxC3N,KAAK,CAAC2D,QAAQ,EAAE,CAAC8Y,gBAAgB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjC9O,qBAAA,CAAoCpS,MAAM,CAACkF,EAAE,CAAC,KAAAS,IAAAA,GAAAA,IAAA,GAAK,IAAI,CAAA;KAE9D,CAAA;IAED3F,MAAM,CAACqhB,UAAU,GAAG,MAAM;MAAA,IAAA1P,qBAAA,EAAAC,qBAAA,CAAA;MACxB,OACE,CAAA,CAAAD,qBAAA,GAAC3R,MAAM,CAACwF,SAAS,CAACic,YAAY,KAAA9P,IAAAA,GAAAA,qBAAA,GAAI,IAAI,OAAAC,qBAAA,GACrCnN,KAAK,CAACO,OAAO,CAACyc,YAAY,KAAA7P,IAAAA,GAAAA,qBAAA,GAAI,IAAI,CAAC,CAAA;KAEvC,CAAA;IACD5R,MAAM,CAAC0hB,0BAA0B,GAAG,MAAM;AACxC,MAAA,OAAQ7D,CAAU,IAAK;AACrB7d,QAAAA,MAAM,CAACohB,gBAAgB,IAAvBphB,IAAAA,IAAAA,MAAM,CAACohB,gBAAgB,CACnBvD,CAAC,CAAgB8D,MAAM,CAAsBC,OACjD,CAAC,CAAA;OACF,CAAA;KACF,CAAA;GACF;AAEDtV,EAAAA,SAAS,EAAEA,CACT5H,GAAe,EACfD,KAAmB,KACV;IACTC,GAAG,CAAC8V,mBAAmB,GAAGvY,IAAI,CAC5B,MAAM,CAACyC,GAAG,CAAC8I,WAAW,EAAE,EAAE/I,KAAK,CAAC2D,QAAQ,EAAE,CAAC8Y,gBAAgB,CAAC,EAC5DvG,KAAK,IAAI;AACP,MAAA,OAAOA,KAAK,CAAC9R,MAAM,CAAC/D,IAAI,IAAIA,IAAI,CAAC9E,MAAM,CAACuL,YAAY,EAAE,CAAC,CAAA;KACxD,EACDvH,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,qBAAqB,CAClE,CAAC,CAAA;IACDN,GAAG,CAACmd,eAAe,GAAG5f,IAAI,CACxB,MAAM,CACJyC,GAAG,CAACgW,mBAAmB,EAAE,EACzBhW,GAAG,CAAC6V,qBAAqB,EAAE,EAC3B7V,GAAG,CAACkW,oBAAoB,EAAE,CAC3B,EACD,CAACtS,IAAI,EAAEoC,MAAM,EAAEnC,KAAK,KAAK,CAAC,GAAGD,IAAI,EAAE,GAAGoC,MAAM,EAAE,GAAGnC,KAAK,CAAC,EACvDvE,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,iBAAiB,CAC9D,CAAC,CAAA;GACF;EAEDgD,WAAW,EAA0BvD,KAAmB,IAAW;AACjE,IAAA,MAAMqd,wBAAwB,GAAGA,CAC/BphB,GAAW,EACXqhB,UAA0C,KACL;AACrC,MAAA,OAAO9f,IAAI,CACT,MAAM,CACJ8f,UAAU,EAAE,EACZA,UAAU,EAAE,CACTlZ,MAAM,CAAC9H,CAAC,IAAIA,CAAC,CAACwK,YAAY,EAAE,CAAC,CAC7BzD,GAAG,CAAC/G,CAAC,IAAIA,CAAC,CAACmE,EAAE,CAAC,CACdwG,IAAI,CAAC,GAAG,CAAC,CACb,EACDjF,OAAO,IAAI;AACT,QAAA,OAAOA,OAAO,CAACoC,MAAM,CAAC9H,CAAC,IAAIA,CAAC,CAACwK,YAAY,oBAAdxK,CAAC,CAACwK,YAAY,EAAI,CAAC,CAAA;OAC/C,EACDvH,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,cAAc,EAAEtE,GAAG,CACnD,CAAC,CAAA;KACF,CAAA;AAED+D,IAAAA,KAAK,CAACud,qBAAqB,GAAGF,wBAAwB,CACpD,uBAAuB,EACvB,MAAMrd,KAAK,CAACwd,iBAAiB,EAC/B,CAAC,CAAA;AACDxd,IAAAA,KAAK,CAAC0D,qBAAqB,GAAG2Z,wBAAwB,CACpD,uBAAuB,EACvB,MAAMrd,KAAK,CAACgJ,iBAAiB,EAC/B,CAAC,CAAA;AACDhJ,IAAAA,KAAK,CAACyd,yBAAyB,GAAGJ,wBAAwB,CACxD,2BAA2B,EAC3B,MAAMrd,KAAK,CAAC0W,kBAAkB,EAChC,CAAC,CAAA;AACD1W,IAAAA,KAAK,CAAC0d,0BAA0B,GAAGL,wBAAwB,CACzD,4BAA4B,EAC5B,MAAMrd,KAAK,CAAC2W,mBAAmB,EACjC,CAAC,CAAA;AACD3W,IAAAA,KAAK,CAAC2d,2BAA2B,GAAGN,wBAAwB,CAC1D,6BAA6B,EAC7B,MAAMrd,KAAK,CAAC4W,oBAAoB,EAClC,CAAC,CAAA;AAED5W,IAAAA,KAAK,CAAC6c,mBAAmB,GAAGhhB,OAAO,IACjCmE,KAAK,CAACO,OAAO,CAACmc,wBAAwB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAtC1c,KAAK,CAACO,OAAO,CAACmc,wBAAwB,CAAG7gB,OAAO,CAAC,CAAA;AAEnDmE,IAAAA,KAAK,CAAC4d,qBAAqB,GAAGhP,YAAY,IAAI;AAAA,MAAA,IAAAC,qBAAA,CAAA;MAC5C7O,KAAK,CAAC6c,mBAAmB,CACvBjO,YAAY,GAAG,EAAE,IAAAC,qBAAA,GAAG7O,KAAK,CAAC+O,YAAY,CAAC0N,gBAAgB,KAAA,IAAA,GAAA5N,qBAAA,GAAI,EAC7D,CAAC,CAAA;KACF,CAAA;AAED7O,IAAAA,KAAK,CAAC6d,uBAAuB,GAAGhR,KAAK,IAAI;AAAA,MAAA,IAAAiR,MAAA,CAAA;AACvCjR,MAAAA,KAAK,GAAAiR,CAAAA,MAAA,GAAGjR,KAAK,KAAAiR,IAAAA,GAAAA,MAAA,GAAI,CAAC9d,KAAK,CAAC+d,sBAAsB,EAAE,CAAA;AAEhD/d,MAAAA,KAAK,CAAC6c,mBAAmB,CACvB7c,KAAK,CAACgJ,iBAAiB,EAAE,CAACG,MAAM,CAC9B,CAAC6U,GAAG,EAAEziB,MAAM,MAAM;AAChB,QAAA,GAAGyiB,GAAG;AACN,QAAA,CAACziB,MAAM,CAACkF,EAAE,GAAG,CAACoM,KAAK,GAAG,EAACtR,MAAM,CAACqhB,UAAU,IAAjBrhB,IAAAA,IAAAA,MAAM,CAACqhB,UAAU,EAAI,CAAG/P,GAAAA,KAAAA;AACjD,OAAC,CAAC,EACF,EACF,CACF,CAAC,CAAA;KACF,CAAA;IAED7M,KAAK,CAAC+d,sBAAsB,GAAG,MAC7B,CAAC/d,KAAK,CAACgJ,iBAAiB,EAAE,CAAC3K,IAAI,CAAC9C,MAAM,IAAI,EAACA,MAAM,CAACuL,YAAY,IAAnBvL,IAAAA,IAAAA,MAAM,CAACuL,YAAY,EAAI,CAAC,CAAA,CAAA;IAErE9G,KAAK,CAACie,uBAAuB,GAAG,MAC9Bje,KAAK,CAACgJ,iBAAiB,EAAE,CAAC3K,IAAI,CAAC9C,MAAM,IAAIA,MAAM,CAACuL,YAAY,IAAA,IAAA,GAAA,KAAA,CAAA,GAAnBvL,MAAM,CAACuL,YAAY,EAAI,CAAC,CAAA;IAEnE9G,KAAK,CAACke,oCAAoC,GAAG,MAAM;AACjD,MAAA,OAAQ9E,CAAU,IAAK;AAAA,QAAA,IAAA+E,OAAA,CAAA;AACrBne,QAAAA,KAAK,CAAC6d,uBAAuB,CAAAM,CAAAA,OAAA,GACzB/E,CAAC,CAAgB8D,MAAM,KAAzBiB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAA,CAAgDhB,OAClD,CAAC,CAAA;OACF,CAAA;KACF,CAAA;AACH,GAAA;AACF,EAAC;AAEM,SAASxJ,sBAAsBA,CACpC3T,KAAmB,EACnB0T,QAA2C,EAC3C;AACA,EAAA,OAAO,CAACA,QAAQ,GACZ1T,KAAK,CAAC0D,qBAAqB,EAAE,GAC7BgQ,QAAQ,KAAK,QAAQ,GACnB1T,KAAK,CAAC2d,2BAA2B,EAAE,GACnCjK,QAAQ,KAAK,MAAM,GACjB1T,KAAK,CAACyd,yBAAyB,EAAE,GACjCzd,KAAK,CAAC0d,0BAA0B,EAAE,CAAA;AAC5C;;ACjSA;;AAEO,MAAMU,cAA4B,GAAG;EAC1C7a,WAAW,EAA0BvD,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACqe,yBAAyB,GAC7Bre,KAAK,CAACO,OAAO,CAACiJ,kBAAkB,IAChCxJ,KAAK,CAACO,OAAO,CAACiJ,kBAAkB,CAACxJ,KAAK,EAAE,YAAY,CAAC,CAAA;IAEvDA,KAAK,CAACse,wBAAwB,GAAG,MAAM;MACrC,IAAIte,KAAK,CAACO,OAAO,CAAC2O,eAAe,IAAI,CAAClP,KAAK,CAACqe,yBAAyB,EAAE;AACrE,QAAA,OAAOre,KAAK,CAACyJ,sBAAsB,EAAE,CAAA;AACvC,OAAA;AAEA,MAAA,OAAOzJ,KAAK,CAACqe,yBAAyB,EAAE,CAAA;KACzC,CAAA;AAEDre,IAAAA,KAAK,CAACue,6BAA6B,GACjCve,KAAK,CAACO,OAAO,CAACoJ,sBAAsB,IACpC3J,KAAK,CAACO,OAAO,CAACoJ,sBAAsB,CAAC3J,KAAK,EAAE,YAAY,CAAC,CAAA;IAC3DA,KAAK,CAACwe,4BAA4B,GAAG,MAAM;AACzC,MAAA,IAAI,CAACxe,KAAK,CAACue,6BAA6B,EAAE;QACxC,OAAO,IAAI3U,GAAG,EAAE,CAAA;AAClB,OAAA;AAEA,MAAA,OAAO5J,KAAK,CAACue,6BAA6B,EAAE,CAAA;KAC7C,CAAA;AAEDve,IAAAA,KAAK,CAACye,6BAA6B,GACjCze,KAAK,CAACO,OAAO,CAACuJ,sBAAsB,IACpC9J,KAAK,CAACO,OAAO,CAACuJ,sBAAsB,CAAC9J,KAAK,EAAE,YAAY,CAAC,CAAA;IAC3DA,KAAK,CAAC0e,4BAA4B,GAAG,MAAM;AACzC,MAAA,IAAI,CAAC1e,KAAK,CAACye,6BAA6B,EAAE;AACxC,QAAA,OAAA;AACF,OAAA;AAEA,MAAA,OAAOze,KAAK,CAACye,6BAA6B,EAAE,CAAA;KAC7C,CAAA;AACH,GAAA;AACF;;AC4BA;;AAEO,MAAME,eAA6B,GAAG;EAC3CzS,eAAe,EAAGC,KAAK,IAA6B;IAClD,OAAO;AACLyS,MAAAA,YAAY,EAAEpd,SAAS;MACvB,GAAG2K,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfrM,KAAmB,IACY;IAC/B,OAAO;AACL6e,MAAAA,oBAAoB,EAAE7iB,gBAAgB,CAAC,cAAc,EAAEgE,KAAK,CAAC;AAC7D8e,MAAAA,cAAc,EAAE,MAAM;MACtBC,wBAAwB,EAAExjB,MAAM,IAAI;AAAA,QAAA,IAAAyjB,qBAAA,CAAA;AAClC,QAAA,MAAMnS,KAAK,GAAA,CAAAmS,qBAAA,GAAGhf,KAAK,CAChB2M,eAAe,EAAE,CACjBC,QAAQ,CAAC,CAAC,CAAC,KAAAoS,IAAAA,IAAAA,CAAAA,qBAAA,GAFAA,qBAAA,CAEE/V,sBAAsB,EAAE,CACrC1N,MAAM,CAACkF,EAAE,CAAC,KAHCue,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAGC1e,QAAQ,EAAE,CAAA;QAEzB,OAAO,OAAOuM,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,CAAA;AAC/D,OAAA;KACD,CAAA;GACF;AAED/L,EAAAA,YAAY,EAAEA,CACZvF,MAA8B,EAC9ByE,KAAmB,KACV;IACTzE,MAAM,CAAC0jB,kBAAkB,GAAG,MAAM;AAAA,MAAA,IAAA/R,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAA8R,qBAAA,CAAA;AAChC,MAAA,OACE,CAAAhS,CAAAA,qBAAA,GAAC3R,MAAM,CAACwF,SAAS,CAACoe,kBAAkB,KAAAjS,IAAAA,GAAAA,qBAAA,GAAI,IAAI,OAAAC,qBAAA,GAC3CnN,KAAK,CAACO,OAAO,CAAC4e,kBAAkB,KAAA,IAAA,GAAAhS,qBAAA,GAAI,IAAI,CAAC,KAAAC,CAAAA,sBAAA,GACzCpN,KAAK,CAACO,OAAO,CAACgN,aAAa,KAAA,IAAA,GAAAH,sBAAA,GAAI,IAAI,CAAC,KAAA8R,CAAAA,qBAAA,GACpClf,KAAK,CAACO,OAAO,CAACwe,wBAAwB,oBAAtC/e,KAAK,CAACO,OAAO,CAACwe,wBAAwB,CAAGxjB,MAAM,CAAC,YAAA2jB,qBAAA,GAAI,IAAI,CAAC,IAC1D,CAAC,CAAC3jB,MAAM,CAACC,UAAU,CAAA;KAEtB,CAAA;GACF;EAED+H,WAAW,EAA0BvD,KAAmB,IAAW;IACjEA,KAAK,CAACof,qBAAqB,GAAG,MAAM;MAClC,OAAOtT,SAAS,CAAC/B,cAAc,CAAA;KAChC,CAAA;IAED/J,KAAK,CAACqf,iBAAiB,GAAG,MAAM;MAAA,IAAAtS,qBAAA,EAAAC,sBAAA,CAAA;MAC9B,MAAM;AAAE8R,QAAAA,cAAc,EAAEA,cAAAA;OAAgB,GAAG9e,KAAK,CAACO,OAAO,CAAA;AAExD,MAAA,OAAOlE,UAAU,CAACyiB,cAAc,CAAC,GAC7BA,cAAc,GACdA,cAAc,KAAK,MAAM,GACvB9e,KAAK,CAACof,qBAAqB,EAAE,GAAArS,CAAAA,qBAAA,GAAAC,CAAAA,sBAAA,GAC7BhN,KAAK,CAACO,OAAO,CAACuL,SAAS,KAAvBkB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAA0B8R,cAAc,CAAW,KAAA/R,IAAAA,GAAAA,qBAAA,GACnDjB,SAAS,CAACgT,cAAc,CAAoB,CAAA;KACnD,CAAA;AAED9e,IAAAA,KAAK,CAACsf,eAAe,GAAGzjB,OAAO,IAAI;AACjCmE,MAAAA,KAAK,CAACO,OAAO,CAACse,oBAAoB,IAAlC7e,IAAAA,IAAAA,KAAK,CAACO,OAAO,CAACse,oBAAoB,CAAGhjB,OAAO,CAAC,CAAA;KAC9C,CAAA;AAEDmE,IAAAA,KAAK,CAACuf,iBAAiB,GAAG3Q,YAAY,IAAI;AACxC5O,MAAAA,KAAK,CAACsf,eAAe,CACnB1Q,YAAY,GAAGpN,SAAS,GAAGxB,KAAK,CAAC+O,YAAY,CAAC6P,YAChD,CAAC,CAAA;KACF,CAAA;AACH,GAAA;AACF;;ACKA;;AAEO,MAAMY,YAA0B,GAAG;EACxCtT,eAAe,EAAGC,KAAK,IAAyB;IAC9C,OAAO;MACLsT,QAAQ,EAAE,EAAE;MACZ,GAAGtT,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfrM,KAAmB,IACQ;IAC3B,OAAO;AACL0f,MAAAA,gBAAgB,EAAE1jB,gBAAgB,CAAC,UAAU,EAAEgE,KAAK,CAAC;AACrD2f,MAAAA,oBAAoB,EAAE,IAAA;KACvB,CAAA;GACF;EAEDpc,WAAW,EAA0BvD,KAAmB,IAAW;IACjE,IAAI4f,UAAU,GAAG,KAAK,CAAA;IACtB,IAAIC,MAAM,GAAG,KAAK,CAAA;IAElB7f,KAAK,CAAC8f,kBAAkB,GAAG,MAAM;MAAA,IAAA5e,IAAA,EAAA6e,qBAAA,CAAA;MAC/B,IAAI,CAACH,UAAU,EAAE;QACf5f,KAAK,CAACggB,MAAM,CAAC,MAAM;AACjBJ,UAAAA,UAAU,GAAG,IAAI,CAAA;AACnB,SAAC,CAAC,CAAA;AACF,QAAA,OAAA;AACF,OAAA;MAEA,IAAA1e,CAAAA,IAAA,GAAA6e,CAAAA,qBAAA,GACE/f,KAAK,CAACO,OAAO,CAAC0f,YAAY,KAAAF,IAAAA,GAAAA,qBAAA,GAC1B/f,KAAK,CAACO,OAAO,CAAC2f,iBAAiB,KAAA,IAAA,GAAAhf,IAAA,GAC/B,CAAClB,KAAK,CAACO,OAAO,CAAC4f,eAAe,EAC9B;AACA,QAAA,IAAIN,MAAM,EAAE,OAAA;AACZA,QAAAA,MAAM,GAAG,IAAI,CAAA;QACb7f,KAAK,CAACggB,MAAM,CAAC,MAAM;UACjBhgB,KAAK,CAACogB,aAAa,EAAE,CAAA;AACrBP,UAAAA,MAAM,GAAG,KAAK,CAAA;AAChB,SAAC,CAAC,CAAA;AACJ,OAAA;KACD,CAAA;AACD7f,IAAAA,KAAK,CAACqgB,WAAW,GAAGxkB,OAAO,IAAImE,KAAK,CAACO,OAAO,CAACmf,gBAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAA9B1f,KAAK,CAACO,OAAO,CAACmf,gBAAgB,CAAG7jB,OAAO,CAAC,CAAA;AACxEmE,IAAAA,KAAK,CAACsgB,qBAAqB,GAAGb,QAAQ,IAAI;MACxC,IAAIA,QAAQ,IAARA,IAAAA,GAAAA,QAAQ,GAAI,CAACzf,KAAK,CAACugB,oBAAoB,EAAE,EAAE;AAC7CvgB,QAAAA,KAAK,CAACqgB,WAAW,CAAC,IAAI,CAAC,CAAA;AACzB,OAAC,MAAM;AACLrgB,QAAAA,KAAK,CAACqgB,WAAW,CAAC,EAAE,CAAC,CAAA;AACvB,OAAA;KACD,CAAA;AACDrgB,IAAAA,KAAK,CAACogB,aAAa,GAAGxR,YAAY,IAAI;MAAA,IAAA4R,qBAAA,EAAA1R,mBAAA,CAAA;MACpC9O,KAAK,CAACqgB,WAAW,CAACzR,YAAY,GAAG,EAAE,GAAA,CAAA4R,qBAAA,GAAA,CAAA1R,mBAAA,GAAG9O,KAAK,CAAC+O,YAAY,KAAlBD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoB2Q,QAAQ,YAAAe,qBAAA,GAAI,EAAE,CAAC,CAAA;KAC1E,CAAA;IACDxgB,KAAK,CAACygB,oBAAoB,GAAG,MAAM;AACjC,MAAA,OAAOzgB,KAAK,CACT0gB,wBAAwB,EAAE,CAC1B9T,QAAQ,CAACvO,IAAI,CAAC4B,GAAG,IAAIA,GAAG,CAAC0gB,YAAY,EAAE,CAAC,CAAA;KAC5C,CAAA;IACD3gB,KAAK,CAAC4gB,+BAA+B,GAAG,MAAM;AAC5C,MAAA,OAAQxH,CAAU,IAAK;AACnBA,QAAAA,CAAC,CAASC,OAAO,IAAA,IAAA,IAAjBD,CAAC,CAASC,OAAO,EAAI,CAAA;QACvBrZ,KAAK,CAACsgB,qBAAqB,EAAE,CAAA;OAC9B,CAAA;KACF,CAAA;IACDtgB,KAAK,CAAC6gB,qBAAqB,GAAG,MAAM;MAClC,MAAMpB,QAAQ,GAAGzf,KAAK,CAAC2D,QAAQ,EAAE,CAAC8b,QAAQ,CAAA;AAC1C,MAAA,OAAOA,QAAQ,KAAK,IAAI,IAAIxN,MAAM,CAACpC,MAAM,CAAC4P,QAAQ,CAAC,CAACphB,IAAI,CAACgG,OAAO,CAAC,CAAA;KAClE,CAAA;IACDrE,KAAK,CAACugB,oBAAoB,GAAG,MAAM;MACjC,MAAMd,QAAQ,GAAGzf,KAAK,CAAC2D,QAAQ,EAAE,CAAC8b,QAAQ,CAAA;;AAE1C;AACA,MAAA,IAAI,OAAOA,QAAQ,KAAK,SAAS,EAAE;QACjC,OAAOA,QAAQ,KAAK,IAAI,CAAA;AAC1B,OAAA;MAEA,IAAI,CAACxN,MAAM,CAAC6O,IAAI,CAACrB,QAAQ,CAAC,CAACliB,MAAM,EAAE;AACjC,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;;AAEA;AACA,MAAA,IAAIyC,KAAK,CAAC+gB,WAAW,EAAE,CAACnU,QAAQ,CAACvO,IAAI,CAAC4B,GAAG,IAAI,CAACA,GAAG,CAAC+gB,aAAa,EAAE,CAAC,EAAE;AAClE,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;;AAEA;AACA,MAAA,OAAO,IAAI,CAAA;KACZ,CAAA;IACDhhB,KAAK,CAACihB,gBAAgB,GAAG,MAAM;MAC7B,IAAIra,QAAQ,GAAG,CAAC,CAAA;AAEhB,MAAA,MAAMsa,MAAM,GACVlhB,KAAK,CAAC2D,QAAQ,EAAE,CAAC8b,QAAQ,KAAK,IAAI,GAC9BxN,MAAM,CAAC6O,IAAI,CAAC9gB,KAAK,CAAC+gB,WAAW,EAAE,CAACI,QAAQ,CAAC,GACzClP,MAAM,CAAC6O,IAAI,CAAC9gB,KAAK,CAAC2D,QAAQ,EAAE,CAAC8b,QAAQ,CAAC,CAAA;AAE5CyB,MAAAA,MAAM,CAAC/jB,OAAO,CAACsD,EAAE,IAAI;AACnB,QAAA,MAAM2gB,OAAO,GAAG3gB,EAAE,CAACmB,KAAK,CAAC,GAAG,CAAC,CAAA;QAC7BgF,QAAQ,GAAGjI,IAAI,CAACU,GAAG,CAACuH,QAAQ,EAAEwa,OAAO,CAAC7jB,MAAM,CAAC,CAAA;AAC/C,OAAC,CAAC,CAAA;AAEF,MAAA,OAAOqJ,QAAQ,CAAA;KAChB,CAAA;IACD5G,KAAK,CAACqhB,sBAAsB,GAAG,MAAMrhB,KAAK,CAACshB,iBAAiB,EAAE,CAAA;IAC9DthB,KAAK,CAACuhB,mBAAmB,GAAG,MAAM;MAChC,IAAI,CAACvhB,KAAK,CAACwhB,oBAAoB,IAAIxhB,KAAK,CAACO,OAAO,CAACghB,mBAAmB,EAAE;QACpEvhB,KAAK,CAACwhB,oBAAoB,GAAGxhB,KAAK,CAACO,OAAO,CAACghB,mBAAmB,CAACvhB,KAAK,CAAC,CAAA;AACvE,OAAA;MAEA,IAAIA,KAAK,CAACO,OAAO,CAAC4f,eAAe,IAAI,CAACngB,KAAK,CAACwhB,oBAAoB,EAAE;AAChE,QAAA,OAAOxhB,KAAK,CAACqhB,sBAAsB,EAAE,CAAA;AACvC,OAAA;AAEA,MAAA,OAAOrhB,KAAK,CAACwhB,oBAAoB,EAAE,CAAA;KACpC,CAAA;GACF;AAED3Z,EAAAA,SAAS,EAAEA,CACT5H,GAAe,EACfD,KAAmB,KACV;AACTC,IAAAA,GAAG,CAACwhB,cAAc,GAAGhC,QAAQ,IAAI;AAC/Bzf,MAAAA,KAAK,CAACqgB,WAAW,CAACjkB,GAAG,IAAI;AAAA,QAAA,IAAAslB,SAAA,CAAA;AACvB,QAAA,MAAMC,MAAM,GAAGvlB,GAAG,KAAK,IAAI,GAAG,IAAI,GAAG,CAAC,EAACA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAG6D,GAAG,CAACQ,EAAE,CAAC,CAAA,CAAA;QAEpD,IAAImhB,WAA8B,GAAG,EAAE,CAAA;QAEvC,IAAIxlB,GAAG,KAAK,IAAI,EAAE;AAChB6V,UAAAA,MAAM,CAAC6O,IAAI,CAAC9gB,KAAK,CAAC+gB,WAAW,EAAE,CAACI,QAAQ,CAAC,CAAChkB,OAAO,CAAC0kB,KAAK,IAAI;AACzDD,YAAAA,WAAW,CAACC,KAAK,CAAC,GAAG,IAAI,CAAA;AAC3B,WAAC,CAAC,CAAA;AACJ,SAAC,MAAM;AACLD,UAAAA,WAAW,GAAGxlB,GAAG,CAAA;AACnB,SAAA;QAEAqjB,QAAQ,GAAA,CAAAiC,SAAA,GAAGjC,QAAQ,YAAAiC,SAAA,GAAI,CAACC,MAAM,CAAA;AAE9B,QAAA,IAAI,CAACA,MAAM,IAAIlC,QAAQ,EAAE;UACvB,OAAO;AACL,YAAA,GAAGmC,WAAW;YACd,CAAC3hB,GAAG,CAACQ,EAAE,GAAG,IAAA;WACX,CAAA;AACH,SAAA;AAEA,QAAA,IAAIkhB,MAAM,IAAI,CAAClC,QAAQ,EAAE;UACvB,MAAM;AAAE,YAAA,CAACxf,GAAG,CAACQ,EAAE,GAAGgY,CAAC;YAAE,GAAGC,IAAAA;AAAK,WAAC,GAAGkJ,WAAW,CAAA;AAC5C,UAAA,OAAOlJ,IAAI,CAAA;AACb,SAAA;AAEA,QAAA,OAAOtc,GAAG,CAAA;AACZ,OAAC,CAAC,CAAA;KACH,CAAA;IACD6D,GAAG,CAAC+gB,aAAa,GAAG,MAAM;AAAA,MAAA,IAAAc,qBAAA,CAAA;MACxB,MAAMrC,QAAQ,GAAGzf,KAAK,CAAC2D,QAAQ,EAAE,CAAC8b,QAAQ,CAAA;AAE1C,MAAA,OAAO,CAAC,EAAA,CAAAqC,qBAAA,GACN9hB,KAAK,CAACO,OAAO,CAACwhB,gBAAgB,IAA9B/hB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAACO,OAAO,CAACwhB,gBAAgB,CAAG9hB,GAAG,CAAC,KAAA6hB,IAAAA,GAAAA,qBAAA,GACpCrC,QAAQ,KAAK,IAAI,KAAIA,QAAQ,IAAA,IAAA,GAAA,KAAA,CAAA,GAARA,QAAQ,CAAGxf,GAAG,CAACQ,EAAE,CAAC,CACzC,CAAA,CAAA;KACF,CAAA;IACDR,GAAG,CAAC0gB,YAAY,GAAG,MAAM;AAAA,MAAA,IAAAqB,qBAAA,EAAA7U,qBAAA,EAAA8F,YAAA,CAAA;AACvB,MAAA,OAAA,CAAA+O,qBAAA,GACEhiB,KAAK,CAACO,OAAO,CAAC0hB,eAAe,IAA7BjiB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAACO,OAAO,CAAC0hB,eAAe,CAAGhiB,GAAG,CAAC,KAAA+hB,IAAAA,GAAAA,qBAAA,GACnC,CAAA7U,CAAAA,qBAAA,GAACnN,KAAK,CAACO,OAAO,CAAC2hB,eAAe,KAAA/U,IAAAA,GAAAA,qBAAA,GAAI,IAAI,KAAK,CAAC,EAAA8F,CAAAA,YAAA,GAAChT,GAAG,CAAC+H,OAAO,KAAXiL,IAAAA,IAAAA,YAAA,CAAa1V,MAAM,CAAA,CAAA;KAEpE,CAAA;IACD0C,GAAG,CAACkiB,uBAAuB,GAAG,MAAM;MAClC,IAAIC,eAAe,GAAG,IAAI,CAAA;MAC1B,IAAIvZ,UAAU,GAAG5I,GAAG,CAAA;AAEpB,MAAA,OAAOmiB,eAAe,IAAIvZ,UAAU,CAACZ,QAAQ,EAAE;QAC7CY,UAAU,GAAG7I,KAAK,CAAC0I,MAAM,CAACG,UAAU,CAACZ,QAAQ,EAAE,IAAI,CAAC,CAAA;AACpDma,QAAAA,eAAe,GAAGvZ,UAAU,CAACmY,aAAa,EAAE,CAAA;AAC9C,OAAA;AAEA,MAAA,OAAOoB,eAAe,CAAA;KACvB,CAAA;IACDniB,GAAG,CAACoiB,wBAAwB,GAAG,MAAM;AACnC,MAAA,MAAMC,SAAS,GAAGriB,GAAG,CAAC0gB,YAAY,EAAE,CAAA;AAEpC,MAAA,OAAO,MAAM;QACX,IAAI,CAAC2B,SAAS,EAAE,OAAA;QAChBriB,GAAG,CAACwhB,cAAc,EAAE,CAAA;OACrB,CAAA;KACF,CAAA;AACH,GAAA;AACF;;AC1KA;;AAEA,MAAMc,gBAAgB,GAAG,CAAC,CAAA;AAC1B,MAAMC,eAAe,GAAG,EAAE,CAAA;AAE1B,MAAMC,yBAAyB,GAAGA,OAAwB;AACxDC,EAAAA,SAAS,EAAEH,gBAAgB;AAC3BI,EAAAA,QAAQ,EAAEH,eAAAA;AACZ,CAAC,CAAC,CAAA;AAEK,MAAMI,aAA2B,GAAG;EACzC1W,eAAe,EAAGC,KAAK,IAA2B;IAChD,OAAO;AACL,MAAA,GAAGA,KAAK;AACR0W,MAAAA,UAAU,EAAE;QACV,GAAGJ,yBAAyB,EAAE;AAC9B,QAAA,IAAGtW,KAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAE0W,UAAU;AACtB,OAAA;KACD,CAAA;GACF;EAEDxW,iBAAiB,EACfrM,KAAmB,IACU;IAC7B,OAAO;AACL8iB,MAAAA,kBAAkB,EAAE9mB,gBAAgB,CAAC,YAAY,EAAEgE,KAAK,CAAA;KACzD,CAAA;GACF;EAEDuD,WAAW,EAA0BvD,KAAmB,IAAW;IACjE,IAAI4f,UAAU,GAAG,KAAK,CAAA;IACtB,IAAIC,MAAM,GAAG,KAAK,CAAA;IAElB7f,KAAK,CAAC+iB,mBAAmB,GAAG,MAAM;MAAA,IAAA7hB,IAAA,EAAA6e,qBAAA,CAAA;MAChC,IAAI,CAACH,UAAU,EAAE;QACf5f,KAAK,CAACggB,MAAM,CAAC,MAAM;AACjBJ,UAAAA,UAAU,GAAG,IAAI,CAAA;AACnB,SAAC,CAAC,CAAA;AACF,QAAA,OAAA;AACF,OAAA;MAEA,IAAA1e,CAAAA,IAAA,GAAA6e,CAAAA,qBAAA,GACE/f,KAAK,CAACO,OAAO,CAAC0f,YAAY,KAAAF,IAAAA,GAAAA,qBAAA,GAC1B/f,KAAK,CAACO,OAAO,CAACyiB,kBAAkB,KAAA,IAAA,GAAA9hB,IAAA,GAChC,CAAClB,KAAK,CAACO,OAAO,CAAC0iB,gBAAgB,EAC/B;AACA,QAAA,IAAIpD,MAAM,EAAE,OAAA;AACZA,QAAAA,MAAM,GAAG,IAAI,CAAA;QACb7f,KAAK,CAACggB,MAAM,CAAC,MAAM;UACjBhgB,KAAK,CAACkjB,cAAc,EAAE,CAAA;AACtBrD,UAAAA,MAAM,GAAG,KAAK,CAAA;AAChB,SAAC,CAAC,CAAA;AACJ,OAAA;KACD,CAAA;AACD7f,IAAAA,KAAK,CAACmjB,aAAa,GAAGtnB,OAAO,IAAI;MAC/B,MAAMunB,WAAqC,GAAGhnB,GAAG,IAAI;AACnD,QAAA,IAAIinB,QAAQ,GAAGznB,gBAAgB,CAACC,OAAO,EAAEO,GAAG,CAAC,CAAA;AAE7C,QAAA,OAAOinB,QAAQ,CAAA;OAChB,CAAA;AAED,MAAA,OAAOrjB,KAAK,CAACO,OAAO,CAACuiB,kBAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAhC9iB,KAAK,CAACO,OAAO,CAACuiB,kBAAkB,CAAGM,WAAW,CAAC,CAAA;KACvD,CAAA;AACDpjB,IAAAA,KAAK,CAACsjB,eAAe,GAAG1U,YAAY,IAAI;AAAA,MAAA,IAAA2U,qBAAA,CAAA;MACtCvjB,KAAK,CAACmjB,aAAa,CACjBvU,YAAY,GACR6T,yBAAyB,EAAE,GAAA,CAAAc,qBAAA,GAC3BvjB,KAAK,CAAC+O,YAAY,CAAC8T,UAAU,KAAA,IAAA,GAAAU,qBAAA,GAAId,yBAAyB,EAChE,CAAC,CAAA;KACF,CAAA;AACDziB,IAAAA,KAAK,CAACwjB,YAAY,GAAG3nB,OAAO,IAAI;AAC9BmE,MAAAA,KAAK,CAACmjB,aAAa,CAAC/mB,GAAG,IAAI;QACzB,IAAIsmB,SAAS,GAAG9mB,gBAAgB,CAACC,OAAO,EAAEO,GAAG,CAACsmB,SAAS,CAAC,CAAA;AAExD,QAAA,MAAMe,YAAY,GAChB,OAAOzjB,KAAK,CAACO,OAAO,CAACmjB,SAAS,KAAK,WAAW,IAC9C1jB,KAAK,CAACO,OAAO,CAACmjB,SAAS,KAAK,CAAC,CAAC,GAC1BhY,MAAM,CAACsL,gBAAgB,GACvBhX,KAAK,CAACO,OAAO,CAACmjB,SAAS,GAAG,CAAC,CAAA;AAEjChB,QAAAA,SAAS,GAAG/jB,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEV,IAAI,CAACW,GAAG,CAACojB,SAAS,EAAEe,YAAY,CAAC,CAAC,CAAA;QAE1D,OAAO;AACL,UAAA,GAAGrnB,GAAG;AACNsmB,UAAAA,SAAAA;SACD,CAAA;AACH,OAAC,CAAC,CAAA;KACH,CAAA;AACD1iB,IAAAA,KAAK,CAACkjB,cAAc,GAAGtU,YAAY,IAAI;MAAA,IAAA+U,sBAAA,EAAA7U,mBAAA,CAAA;AACrC9O,MAAAA,KAAK,CAACwjB,YAAY,CAChB5U,YAAY,GACR2T,gBAAgB,GAAAoB,CAAAA,sBAAA,GAAA7U,CAAAA,mBAAA,GAChB9O,KAAK,CAAC+O,YAAY,KAAAD,IAAAA,IAAAA,CAAAA,mBAAA,GAAlBA,mBAAA,CAAoB+T,UAAU,KAA9B/T,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAgC4T,SAAS,KAAAiB,IAAAA,GAAAA,sBAAA,GAAIpB,gBACnD,CAAC,CAAA;KACF,CAAA;AACDviB,IAAAA,KAAK,CAAC4jB,aAAa,GAAGhV,YAAY,IAAI;MAAA,IAAAiV,sBAAA,EAAAC,oBAAA,CAAA;AACpC9jB,MAAAA,KAAK,CAAC+jB,WAAW,CACfnV,YAAY,GACR4T,eAAe,GAAAqB,CAAAA,sBAAA,GAAAC,CAAAA,oBAAA,GACf9jB,KAAK,CAAC+O,YAAY,KAAA+U,IAAAA,IAAAA,CAAAA,oBAAA,GAAlBA,oBAAA,CAAoBjB,UAAU,KAA9BiB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,oBAAA,CAAgCnB,QAAQ,KAAAkB,IAAAA,GAAAA,sBAAA,GAAIrB,eAClD,CAAC,CAAA;KACF,CAAA;AACDxiB,IAAAA,KAAK,CAAC+jB,WAAW,GAAGloB,OAAO,IAAI;AAC7BmE,MAAAA,KAAK,CAACmjB,aAAa,CAAC/mB,GAAG,IAAI;AACzB,QAAA,MAAMumB,QAAQ,GAAGhkB,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEzD,gBAAgB,CAACC,OAAO,EAAEO,GAAG,CAACumB,QAAQ,CAAC,CAAC,CAAA;QACrE,MAAMqB,WAAW,GAAG5nB,GAAG,CAACumB,QAAQ,GAAGvmB,GAAG,CAACsmB,SAAU,CAAA;QACjD,MAAMA,SAAS,GAAG/jB,IAAI,CAACoR,KAAK,CAACiU,WAAW,GAAGrB,QAAQ,CAAC,CAAA;QAEpD,OAAO;AACL,UAAA,GAAGvmB,GAAG;UACNsmB,SAAS;AACTC,UAAAA,QAAAA;SACD,CAAA;AACH,OAAC,CAAC,CAAA;KACH,CAAA;AACD;IACA3iB,KAAK,CAACikB,YAAY,GAAGpoB,OAAO,IAC1BmE,KAAK,CAACmjB,aAAa,CAAC/mB,GAAG,IAAI;AAAA,MAAA,IAAA8nB,qBAAA,CAAA;AACzB,MAAA,IAAIC,YAAY,GAAGvoB,gBAAgB,CACjCC,OAAO,EAAA,CAAAqoB,qBAAA,GACPlkB,KAAK,CAACO,OAAO,CAACmjB,SAAS,KAAA,IAAA,GAAAQ,qBAAA,GAAI,CAAC,CAC9B,CAAC,CAAA;AAED,MAAA,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;QACpCA,YAAY,GAAGxlB,IAAI,CAACU,GAAG,CAAC,CAAC,CAAC,EAAE8kB,YAAY,CAAC,CAAA;AAC3C,OAAA;MAEA,OAAO;AACL,QAAA,GAAG/nB,GAAG;AACNsnB,QAAAA,SAAS,EAAES,YAAAA;OACZ,CAAA;AACH,KAAC,CAAC,CAAA;AAEJnkB,IAAAA,KAAK,CAACokB,cAAc,GAAG5mB,IAAI,CACzB,MAAM,CAACwC,KAAK,CAACqkB,YAAY,EAAE,CAAC,EAC5BX,SAAS,IAAI;MACX,IAAIY,WAAqB,GAAG,EAAE,CAAA;AAC9B,MAAA,IAAIZ,SAAS,IAAIA,SAAS,GAAG,CAAC,EAAE;QAC9BY,WAAW,GAAG,CAAC,GAAG,IAAI7nB,KAAK,CAACinB,SAAS,CAAC,CAAC,CAACa,IAAI,CAAC,IAAI,CAAC,CAAClhB,GAAG,CAAC,CAACoV,CAAC,EAAEpP,CAAC,KAAKA,CAAC,CAAC,CAAA;AACrE,OAAA;AACA,MAAA,OAAOib,WAAW,CAAA;KACnB,EACD/kB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,gBAAgB,CAC9D,CAAC,CAAA;AAEDP,IAAAA,KAAK,CAACwkB,kBAAkB,GAAG,MAAMxkB,KAAK,CAAC2D,QAAQ,EAAE,CAACkf,UAAU,CAACH,SAAS,GAAG,CAAC,CAAA;IAE1E1iB,KAAK,CAACykB,cAAc,GAAG,MAAM;MAC3B,MAAM;AAAE/B,QAAAA,SAAAA;AAAU,OAAC,GAAG1iB,KAAK,CAAC2D,QAAQ,EAAE,CAACkf,UAAU,CAAA;AAEjD,MAAA,MAAMa,SAAS,GAAG1jB,KAAK,CAACqkB,YAAY,EAAE,CAAA;AAEtC,MAAA,IAAIX,SAAS,KAAK,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;MAEA,IAAIA,SAAS,KAAK,CAAC,EAAE;AACnB,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;AAEA,MAAA,OAAOhB,SAAS,GAAGgB,SAAS,GAAG,CAAC,CAAA;KACjC,CAAA;IAED1jB,KAAK,CAAC0kB,YAAY,GAAG,MAAM;MACzB,OAAO1kB,KAAK,CAACwjB,YAAY,CAACpnB,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC,CAAA;KAC1C,CAAA;IAED4D,KAAK,CAAC2kB,QAAQ,GAAG,MAAM;AACrB,MAAA,OAAO3kB,KAAK,CAACwjB,YAAY,CAACpnB,GAAG,IAAI;QAC/B,OAAOA,GAAG,GAAG,CAAC,CAAA;AAChB,OAAC,CAAC,CAAA;KACH,CAAA;IAED4D,KAAK,CAAC4kB,SAAS,GAAG,MAAM;AACtB,MAAA,OAAO5kB,KAAK,CAACwjB,YAAY,CAAC,CAAC,CAAC,CAAA;KAC7B,CAAA;IAEDxjB,KAAK,CAAC6kB,QAAQ,GAAG,MAAM;MACrB,OAAO7kB,KAAK,CAACwjB,YAAY,CAACxjB,KAAK,CAACqkB,YAAY,EAAE,GAAG,CAAC,CAAC,CAAA;KACpD,CAAA;IAEDrkB,KAAK,CAAC0gB,wBAAwB,GAAG,MAAM1gB,KAAK,CAACuhB,mBAAmB,EAAE,CAAA;IAClEvhB,KAAK,CAAC8kB,qBAAqB,GAAG,MAAM;MAClC,IACE,CAAC9kB,KAAK,CAAC+kB,sBAAsB,IAC7B/kB,KAAK,CAACO,OAAO,CAACukB,qBAAqB,EACnC;QACA9kB,KAAK,CAAC+kB,sBAAsB,GAC1B/kB,KAAK,CAACO,OAAO,CAACukB,qBAAqB,CAAC9kB,KAAK,CAAC,CAAA;AAC9C,OAAA;MAEA,IAAIA,KAAK,CAACO,OAAO,CAAC0iB,gBAAgB,IAAI,CAACjjB,KAAK,CAAC+kB,sBAAsB,EAAE;AACnE,QAAA,OAAO/kB,KAAK,CAAC0gB,wBAAwB,EAAE,CAAA;AACzC,OAAA;AAEA,MAAA,OAAO1gB,KAAK,CAAC+kB,sBAAsB,EAAE,CAAA;KACtC,CAAA;IAED/kB,KAAK,CAACqkB,YAAY,GAAG,MAAM;AAAA,MAAA,IAAAW,sBAAA,CAAA;AACzB,MAAA,OAAA,CAAAA,sBAAA,GACEhlB,KAAK,CAACO,OAAO,CAACmjB,SAAS,KAAA,IAAA,GAAAsB,sBAAA,GACvBrmB,IAAI,CAACsmB,IAAI,CAACjlB,KAAK,CAACklB,WAAW,EAAE,GAAGllB,KAAK,CAAC2D,QAAQ,EAAE,CAACkf,UAAU,CAACF,QAAQ,CAAC,CAAA;KAExE,CAAA;IAED3iB,KAAK,CAACklB,WAAW,GAAG,MAAM;AAAA,MAAA,IAAAC,qBAAA,CAAA;AACxB,MAAA,OAAA,CAAAA,qBAAA,GACEnlB,KAAK,CAACO,OAAO,CAAC6kB,QAAQ,KAAAD,IAAAA,GAAAA,qBAAA,GAAInlB,KAAK,CAAC0gB,wBAAwB,EAAE,CAAC2E,IAAI,CAAC9nB,MAAM,CAAA;KAEzE,CAAA;AACH,GAAA;AACF;;ACtRA;;AAEA,MAAM+nB,yBAAyB,GAAGA,OAAwB;AACxDC,EAAAA,GAAG,EAAE,EAAE;AACPC,EAAAA,MAAM,EAAE,EAAA;AACV,CAAC,CAAC,CAAA;AAEK,MAAMC,UAAwB,GAAG;EACtCvZ,eAAe,EAAGC,KAAK,IAA2B;IAChD,OAAO;MACLuZ,UAAU,EAAEJ,yBAAyB,EAAE;MACvC,GAAGnZ,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfrM,KAAmB,IACU;IAC7B,OAAO;AACL2lB,MAAAA,kBAAkB,EAAE3pB,gBAAgB,CAAC,YAAY,EAAEgE,KAAK,CAAA;KACzD,CAAA;GACF;AAED6H,EAAAA,SAAS,EAAEA,CACT5H,GAAe,EACfD,KAAmB,KACV;IACTC,GAAG,CAAC2U,GAAG,GAAG,CAAClB,QAAQ,EAAEkS,eAAe,EAAEC,iBAAiB,KAAK;AAC1D,MAAA,MAAMC,UAAU,GAAGF,eAAe,GAC9B3lB,GAAG,CAACuI,WAAW,EAAE,CAACnF,GAAG,CAACnC,IAAA,IAAA;QAAA,IAAC;AAAET,UAAAA,EAAAA;AAAG,SAAC,GAAAS,IAAA,CAAA;AAAA,QAAA,OAAKT,EAAE,CAAA;AAAA,OAAA,CAAC,GACrC,EAAE,CAAA;AACN,MAAA,MAAMslB,YAAY,GAAGF,iBAAiB,GAClC5lB,GAAG,CAAC0I,aAAa,EAAE,CAACtF,GAAG,CAACmV,KAAA,IAAA;QAAA,IAAC;AAAE/X,UAAAA,EAAAA;AAAG,SAAC,GAAA+X,KAAA,CAAA;AAAA,QAAA,OAAK/X,EAAE,CAAA;AAAA,OAAA,CAAC,GACvC,EAAE,CAAA;AACN,MAAA,MAAMygB,MAAM,GAAG,IAAI5Q,GAAG,CAAC,CAAC,GAAGyV,YAAY,EAAE9lB,GAAG,CAACQ,EAAE,EAAE,GAAGqlB,UAAU,CAAC,CAAC,CAAA;AAEhE9lB,MAAAA,KAAK,CAACgmB,aAAa,CAAC5pB,GAAG,IAAI;QAAA,IAAA6pB,SAAA,EAAAC,YAAA,CAAA;QACzB,IAAIxS,QAAQ,KAAK,QAAQ,EAAE;UAAA,IAAAyS,QAAA,EAAAC,WAAA,CAAA;UACzB,OAAO;YACLb,GAAG,EAAE,CAAAY,CAAAA,QAAA,GAAC/pB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEmpB,GAAG,KAAAY,IAAAA,GAAAA,QAAA,GAAI,EAAE,EAAE/hB,MAAM,CAAC9H,CAAC,IAAI,EAAC4kB,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEmF,GAAG,CAAC/pB,CAAC,CAAC,CAAC,CAAA;AAClDkpB,YAAAA,MAAM,EAAE,CACN,GAAG,CAAAY,CAAAA,WAAA,GAAChqB,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEopB,MAAM,KAAAY,IAAAA,GAAAA,WAAA,GAAI,EAAE,EAAEhiB,MAAM,CAAC9H,CAAC,IAAI,EAAC4kB,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEmF,GAAG,CAAC/pB,CAAC,CAAC,CAAA,CAAC,EACnD,GAAGG,KAAK,CAAC4T,IAAI,CAAC6Q,MAAM,CAAC,CAAA;WAExB,CAAA;AACH,SAAA;QAEA,IAAIxN,QAAQ,KAAK,KAAK,EAAE;UAAA,IAAA4S,SAAA,EAAAC,YAAA,CAAA;UACtB,OAAO;AACLhB,YAAAA,GAAG,EAAE,CACH,GAAG,CAAAe,CAAAA,SAAA,GAAClqB,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEmpB,GAAG,KAAAe,IAAAA,GAAAA,SAAA,GAAI,EAAE,EAAEliB,MAAM,CAAC9H,CAAC,IAAI,EAAC4kB,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEmF,GAAG,CAAC/pB,CAAC,CAAC,CAAC,CAAA,EAChD,GAAGG,KAAK,CAAC4T,IAAI,CAAC6Q,MAAM,CAAC,CACtB;YACDsE,MAAM,EAAE,CAAAe,CAAAA,YAAA,GAACnqB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEopB,MAAM,KAAAe,IAAAA,GAAAA,YAAA,GAAI,EAAE,EAAEniB,MAAM,CAAC9H,CAAC,IAAI,EAAC4kB,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEmF,GAAG,CAAC/pB,CAAC,CAAC,CAAA,CAAA;WACxD,CAAA;AACH,SAAA;QAEA,OAAO;UACLipB,GAAG,EAAE,CAAAU,CAAAA,SAAA,GAAC7pB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEmpB,GAAG,KAAAU,IAAAA,GAAAA,SAAA,GAAI,EAAE,EAAE7hB,MAAM,CAAC9H,CAAC,IAAI,EAAC4kB,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEmF,GAAG,CAAC/pB,CAAC,CAAC,CAAC,CAAA;UAClDkpB,MAAM,EAAE,CAAAU,CAAAA,YAAA,GAAC9pB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEopB,MAAM,KAAAU,IAAAA,GAAAA,YAAA,GAAI,EAAE,EAAE9hB,MAAM,CAAC9H,CAAC,IAAI,EAAC4kB,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEmF,GAAG,CAAC/pB,CAAC,CAAC,CAAA,CAAA;SACxD,CAAA;AACH,OAAC,CAAC,CAAA;KACH,CAAA;IACD2D,GAAG,CAACoV,SAAS,GAAG,MAAM;AAAA,MAAA,IAAA4E,KAAA,CAAA;MACpB,MAAM;QAAEuM,gBAAgB;AAAEjR,QAAAA,aAAAA;OAAe,GAAGvV,KAAK,CAACO,OAAO,CAAA;AACzD,MAAA,IAAI,OAAOimB,gBAAgB,KAAK,UAAU,EAAE;QAC1C,OAAOA,gBAAgB,CAACvmB,GAAG,CAAC,CAAA;AAC9B,OAAA;MACA,OAAAga,CAAAA,KAAA,GAAOuM,gBAAgB,IAAhBA,IAAAA,GAAAA,gBAAgB,GAAIjR,aAAa,KAAA,IAAA,GAAA0E,KAAA,GAAI,IAAI,CAAA;KACjD,CAAA;IACDha,GAAG,CAACwV,WAAW,GAAG,MAAM;AACtB,MAAA,MAAMyL,MAAM,GAAG,CAACjhB,GAAG,CAACQ,EAAE,CAAC,CAAA;MAEvB,MAAM;QAAE8kB,GAAG;AAAEC,QAAAA,MAAAA;AAAO,OAAC,GAAGxlB,KAAK,CAAC2D,QAAQ,EAAE,CAAC+hB,UAAU,CAAA;AAEnD,MAAA,MAAMe,KAAK,GAAGvF,MAAM,CAAC7iB,IAAI,CAAC/B,CAAC,IAAIipB,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAE7jB,QAAQ,CAACpF,CAAC,CAAC,CAAC,CAAA;AAChD,MAAA,MAAMoqB,QAAQ,GAAGxF,MAAM,CAAC7iB,IAAI,CAAC/B,CAAC,IAAIkpB,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAE9jB,QAAQ,CAACpF,CAAC,CAAC,CAAC,CAAA;MAEtD,OAAOmqB,KAAK,GAAG,KAAK,GAAGC,QAAQ,GAAG,QAAQ,GAAG,KAAK,CAAA;KACnD,CAAA;IACDzmB,GAAG,CAAC4V,cAAc,GAAG,MAAM;MAAA,IAAA8Q,qBAAA,EAAAC,qBAAA,CAAA;AACzB,MAAA,MAAMlT,QAAQ,GAAGzT,GAAG,CAACwV,WAAW,EAAE,CAAA;AAClC,MAAA,IAAI,CAAC/B,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAA;AAExB,MAAA,MAAMmT,mBAAmB,GAAA,CAAAF,qBAAA,GAAG3mB,KAAK,CAC9B8mB,cAAc,CAACpT,QAAQ,CAAC,KADCiT,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAExBtjB,GAAG,CAAC0jB,KAAA,IAAA;QAAA,IAAC;AAAEtmB,UAAAA,EAAAA;AAAG,SAAC,GAAAsmB,KAAA,CAAA;AAAA,QAAA,OAAKtmB,EAAE,CAAA;OAAC,CAAA,CAAA;AAEvB,MAAA,OAAA,CAAAmmB,qBAAA,GAAOC,mBAAmB,IAAnBA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAmB,CAAEhV,OAAO,CAAC5R,GAAG,CAACQ,EAAE,CAAC,KAAA,IAAA,GAAAmmB,qBAAA,GAAI,CAAC,CAAC,CAAA;KAClD,CAAA;GACF;EAEDrjB,WAAW,EAA0BvD,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACgmB,aAAa,GAAGnqB,OAAO,IAAImE,KAAK,CAACO,OAAO,CAAColB,kBAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAhC3lB,KAAK,CAACO,OAAO,CAAColB,kBAAkB,CAAG9pB,OAAO,CAAC,CAAA;IAE5EmE,KAAK,CAACgnB,eAAe,GAAGpY,YAAY,IAAA;MAAA,IAAAqY,qBAAA,EAAAnY,mBAAA,CAAA;MAAA,OAClC9O,KAAK,CAACgmB,aAAa,CACjBpX,YAAY,GACR0W,yBAAyB,EAAE,GAAA2B,CAAAA,qBAAA,GAAAnY,CAAAA,mBAAA,GAC3B9O,KAAK,CAAC+O,YAAY,KAAlBD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoB4W,UAAU,KAAAuB,IAAAA,GAAAA,qBAAA,GAAI3B,yBAAyB,EACjE,CAAC,CAAA;AAAA,KAAA,CAAA;AAEHtlB,IAAAA,KAAK,CAACknB,mBAAmB,GAAGxT,QAAQ,IAAI;AAAA,MAAA,IAAA4C,qBAAA,CAAA;MACtC,MAAMC,YAAY,GAAGvW,KAAK,CAAC2D,QAAQ,EAAE,CAAC+hB,UAAU,CAAA;MAEhD,IAAI,CAAChS,QAAQ,EAAE;QAAA,IAAAyT,iBAAA,EAAAC,oBAAA,CAAA;QACb,OAAO/iB,OAAO,CAAC,CAAA8iB,CAAAA,iBAAA,GAAA5Q,YAAY,CAACgP,GAAG,KAAA,IAAA,GAAA,KAAA,CAAA,GAAhB4B,iBAAA,CAAkB5pB,MAAM,MAAA6pB,CAAAA,oBAAA,GAAI7Q,YAAY,CAACiP,MAAM,KAAnB4B,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,oBAAA,CAAqB7pB,MAAM,CAAC,CAAA,CAAA;AACzE,OAAA;AACA,MAAA,OAAO8G,OAAO,CAAA,CAAAiS,qBAAA,GAACC,YAAY,CAAC7C,QAAQ,CAAC,KAAtB4C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAwB/Y,MAAM,CAAC,CAAA;KAC/C,CAAA;AAEDyC,IAAAA,KAAK,CAAC8mB,cAAc,GAAGtpB,IAAI,CACzBkW,QAAQ,IAAI,CACV1T,KAAK,CAAC+gB,WAAW,EAAE,CAACsE,IAAI,EACxBrlB,KAAK,CAAC2D,QAAQ,EAAE,CAAC+hB,UAAU,CAAChS,QAAQ,CAAE,EACtCA,QAAQ,CACT,EACD,CAAC2T,WAAW,EAAEC,YAAY,EAAE5T,QAAQ,KAAK;AAAA,MAAA,IAAA6T,qBAAA,CAAA;AACvC,MAAA,MAAMlC,IAAI,GACR,CAAAkC,CAAAA,qBAAA,GAAAvnB,KAAK,CAACO,OAAO,CAACinB,cAAc,KAAAD,IAAAA,GAAAA,qBAAA,GAAI,IAAI;AAChC;AACA;MACA,CAACD,YAAY,WAAZA,YAAY,GAAI,EAAE,EAAEjkB,GAAG,CAACwe,KAAK,IAAI;QAChC,MAAM5hB,GAAG,GAAGD,KAAK,CAAC0I,MAAM,CAACmZ,KAAK,EAAE,IAAI,CAAC,CAAA;QACrC,OAAO5hB,GAAG,CAACkiB,uBAAuB,EAAE,GAAGliB,GAAG,GAAG,IAAI,CAAA;AACnD,OAAC,CAAC;AACF;MACA,CAACqnB,YAAY,WAAZA,YAAY,GAAI,EAAE,EAAEjkB,GAAG,CACtBwe,KAAK,IAAIwF,WAAW,CAACljB,IAAI,CAAClE,GAAG,IAAIA,GAAG,CAACQ,EAAE,KAAKohB,KAAK,CACnD,CAAC,CAAA;MAEP,OAAOwD,IAAI,CACRjhB,MAAM,CAACC,OAAO,CAAC,CACfhB,GAAG,CAAC/G,CAAC,KAAK;AAAE,QAAA,GAAGA,CAAC;AAAEoX,QAAAA,QAAAA;AAAS,OAAC,CAAC,CAAC,CAAA;KAClC,EACDnU,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,gBAAgB,CAC7D,CAAC,CAAA;IAEDP,KAAK,CAACynB,UAAU,GAAG,MAAMznB,KAAK,CAAC8mB,cAAc,CAAC,KAAK,CAAC,CAAA;IAEpD9mB,KAAK,CAAC0nB,aAAa,GAAG,MAAM1nB,KAAK,CAAC8mB,cAAc,CAAC,QAAQ,CAAC,CAAA;AAE1D9mB,IAAAA,KAAK,CAAC2nB,aAAa,GAAGnqB,IAAI,CACxB,MAAM,CACJwC,KAAK,CAAC+gB,WAAW,EAAE,CAACsE,IAAI,EACxBrlB,KAAK,CAAC2D,QAAQ,EAAE,CAAC+hB,UAAU,CAACH,GAAG,EAC/BvlB,KAAK,CAAC2D,QAAQ,EAAE,CAAC+hB,UAAU,CAACF,MAAM,CACnC,EACD,CAACoC,OAAO,EAAErC,GAAG,EAAEC,MAAM,KAAK;MACxB,MAAMqC,YAAY,GAAG,IAAIvX,GAAG,CAAC,CAAC,IAAIiV,GAAG,IAAA,IAAA,GAAHA,GAAG,GAAI,EAAE,GAAG,IAAIC,MAAM,IAAA,IAAA,GAANA,MAAM,GAAI,EAAE,EAAE,CAAC,CAAA;AACjE,MAAA,OAAOoC,OAAO,CAACxjB,MAAM,CAAC9H,CAAC,IAAI,CAACurB,YAAY,CAACxB,GAAG,CAAC/pB,CAAC,CAACmE,EAAE,CAAC,CAAC,CAAA;KACpD,EACDlB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,WAAW,EAAE,eAAe,CAC5D,CAAC,CAAA;AACH,GAAA;AACF;;AC5EA;;AAEO,MAAMunB,YAA0B,GAAG;EACxC5b,eAAe,EAAGC,KAAK,IAA6B;IAClD,OAAO;MACL4b,YAAY,EAAE,EAAE;MAChB,GAAG5b,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfrM,KAAmB,IACY;IAC/B,OAAO;AACLgoB,MAAAA,oBAAoB,EAAEhsB,gBAAgB,CAAC,cAAc,EAAEgE,KAAK,CAAC;AAC7DioB,MAAAA,kBAAkB,EAAE,IAAI;AACxBC,MAAAA,uBAAuB,EAAE,IAAI;AAC7BC,MAAAA,qBAAqB,EAAE,IAAA;AACvB;AACA;AACA;KACD,CAAA;GACF;EAED5kB,WAAW,EAA0BvD,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACooB,eAAe,GAAGvsB,OAAO,IAC7BmE,KAAK,CAACO,OAAO,CAACynB,oBAAoB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAlChoB,KAAK,CAACO,OAAO,CAACynB,oBAAoB,CAAGnsB,OAAO,CAAC,CAAA;IAC/CmE,KAAK,CAACqoB,iBAAiB,GAAGzZ,YAAY,IAAA;AAAA,MAAA,IAAAqY,qBAAA,CAAA;MAAA,OACpCjnB,KAAK,CAACooB,eAAe,CACnBxZ,YAAY,GAAG,EAAE,GAAAqY,CAAAA,qBAAA,GAAGjnB,KAAK,CAAC+O,YAAY,CAACgZ,YAAY,YAAAd,qBAAA,GAAI,EACzD,CAAC,CAAA;AAAA,KAAA,CAAA;AACHjnB,IAAAA,KAAK,CAACsoB,qBAAqB,GAAGzb,KAAK,IAAI;AACrC7M,MAAAA,KAAK,CAACooB,eAAe,CAAChsB,GAAG,IAAI;AAC3ByQ,QAAAA,KAAK,GACH,OAAOA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,CAAC7M,KAAK,CAACuoB,oBAAoB,EAAE,CAAA;AAEtE,QAAA,MAAMR,YAAY,GAAG;UAAE,GAAG3rB,GAAAA;SAAK,CAAA;QAE/B,MAAMosB,kBAAkB,GAAGxoB,KAAK,CAACyS,qBAAqB,EAAE,CAAC7F,QAAQ,CAAA;;AAEjE;AACA;AACA,QAAA,IAAIC,KAAK,EAAE;AACT2b,UAAAA,kBAAkB,CAACrrB,OAAO,CAAC8C,GAAG,IAAI;AAChC,YAAA,IAAI,CAACA,GAAG,CAACwoB,YAAY,EAAE,EAAE;AACvB,cAAA,OAAA;AACF,aAAA;AACAV,YAAAA,YAAY,CAAC9nB,GAAG,CAACQ,EAAE,CAAC,GAAG,IAAI,CAAA;AAC7B,WAAC,CAAC,CAAA;AACJ,SAAC,MAAM;AACL+nB,UAAAA,kBAAkB,CAACrrB,OAAO,CAAC8C,GAAG,IAAI;AAChC,YAAA,OAAO8nB,YAAY,CAAC9nB,GAAG,CAACQ,EAAE,CAAC,CAAA;AAC7B,WAAC,CAAC,CAAA;AACJ,SAAA;AAEA,QAAA,OAAOsnB,YAAY,CAAA;AACrB,OAAC,CAAC,CAAA;KACH,CAAA;IACD/nB,KAAK,CAAC0oB,yBAAyB,GAAG7b,KAAK,IACrC7M,KAAK,CAACooB,eAAe,CAAChsB,GAAG,IAAI;AAC3B,MAAA,MAAMusB,aAAa,GACjB,OAAO9b,KAAK,KAAK,WAAW,GACxBA,KAAK,GACL,CAAC7M,KAAK,CAAC4oB,wBAAwB,EAAE,CAAA;AAEvC,MAAA,MAAMb,YAA+B,GAAG;QAAE,GAAG3rB,GAAAA;OAAK,CAAA;MAElD4D,KAAK,CAAC+gB,WAAW,EAAE,CAACsE,IAAI,CAACloB,OAAO,CAAC8C,GAAG,IAAI;AACtC4oB,QAAAA,mBAAmB,CAACd,YAAY,EAAE9nB,GAAG,CAACQ,EAAE,EAAEkoB,aAAa,EAAE,IAAI,EAAE3oB,KAAK,CAAC,CAAA;AACvE,OAAC,CAAC,CAAA;AAEF,MAAA,OAAO+nB,YAAY,CAAA;AACrB,KAAC,CAAC,CAAA;;AAEJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;IACA/nB,KAAK,CAAC8oB,sBAAsB,GAAG,MAAM9oB,KAAK,CAAC2M,eAAe,EAAE,CAAA;IAC5D3M,KAAK,CAAC+oB,mBAAmB,GAAGvrB,IAAI,CAC9B,MAAM,CAACwC,KAAK,CAAC2D,QAAQ,EAAE,CAACokB,YAAY,EAAE/nB,KAAK,CAAC2M,eAAe,EAAE,CAAC,EAC9D,CAACob,YAAY,EAAEiB,QAAQ,KAAK;MAC1B,IAAI,CAAC/W,MAAM,CAAC6O,IAAI,CAACiH,YAAY,CAAC,CAACxqB,MAAM,EAAE;QACrC,OAAO;AACL8nB,UAAAA,IAAI,EAAE,EAAE;AACRzY,UAAAA,QAAQ,EAAE,EAAE;AACZuU,UAAAA,QAAQ,EAAE,EAAC;SACZ,CAAA;AACH,OAAA;AAEA,MAAA,OAAO8H,YAAY,CAACjpB,KAAK,EAAEgpB,QAAQ,CAAC,CAAA;KACrC,EACDzpB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,qBAAqB,CACnE,CAAC,CAAA;IAEDP,KAAK,CAACkpB,2BAA2B,GAAG1rB,IAAI,CACtC,MAAM,CAACwC,KAAK,CAAC2D,QAAQ,EAAE,CAACokB,YAAY,EAAE/nB,KAAK,CAACgP,mBAAmB,EAAE,CAAC,EAClE,CAAC+Y,YAAY,EAAEiB,QAAQ,KAAK;MAC1B,IAAI,CAAC/W,MAAM,CAAC6O,IAAI,CAACiH,YAAY,CAAC,CAACxqB,MAAM,EAAE;QACrC,OAAO;AACL8nB,UAAAA,IAAI,EAAE,EAAE;AACRzY,UAAAA,QAAQ,EAAE,EAAE;AACZuU,UAAAA,QAAQ,EAAE,EAAC;SACZ,CAAA;AACH,OAAA;AAEA,MAAA,OAAO8H,YAAY,CAACjpB,KAAK,EAAEgpB,QAAQ,CAAC,CAAA;KACrC,EACDzpB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,6BAA6B,CAC3E,CAAC,CAAA;IAEDP,KAAK,CAACmpB,0BAA0B,GAAG3rB,IAAI,CACrC,MAAM,CAACwC,KAAK,CAAC2D,QAAQ,EAAE,CAACokB,YAAY,EAAE/nB,KAAK,CAACshB,iBAAiB,EAAE,CAAC,EAChE,CAACyG,YAAY,EAAEiB,QAAQ,KAAK;MAC1B,IAAI,CAAC/W,MAAM,CAAC6O,IAAI,CAACiH,YAAY,CAAC,CAACxqB,MAAM,EAAE;QACrC,OAAO;AACL8nB,UAAAA,IAAI,EAAE,EAAE;AACRzY,UAAAA,QAAQ,EAAE,EAAE;AACZuU,UAAAA,QAAQ,EAAE,EAAC;SACZ,CAAA;AACH,OAAA;AAEA,MAAA,OAAO8H,YAAY,CAACjpB,KAAK,EAAEgpB,QAAQ,CAAC,CAAA;KACrC,EACDzpB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,4BAA4B,CAC1E,CAAC,CAAA;;AAED;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;IAEAP,KAAK,CAACuoB,oBAAoB,GAAG,MAAM;MACjC,MAAMC,kBAAkB,GAAGxoB,KAAK,CAACgP,mBAAmB,EAAE,CAACpC,QAAQ,CAAA;MAC/D,MAAM;AAAEmb,QAAAA,YAAAA;AAAa,OAAC,GAAG/nB,KAAK,CAAC2D,QAAQ,EAAE,CAAA;AAEzC,MAAA,IAAIylB,iBAAiB,GAAG/kB,OAAO,CAC7BmkB,kBAAkB,CAACjrB,MAAM,IAAI0U,MAAM,CAAC6O,IAAI,CAACiH,YAAY,CAAC,CAACxqB,MACzD,CAAC,CAAA;AAED,MAAA,IAAI6rB,iBAAiB,EAAE;QACrB,IACEZ,kBAAkB,CAACnqB,IAAI,CACrB4B,GAAG,IAAIA,GAAG,CAACwoB,YAAY,EAAE,IAAI,CAACV,YAAY,CAAC9nB,GAAG,CAACQ,EAAE,CACnD,CAAC,EACD;AACA2oB,UAAAA,iBAAiB,GAAG,KAAK,CAAA;AAC3B,SAAA;AACF,OAAA;AAEA,MAAA,OAAOA,iBAAiB,CAAA;KACzB,CAAA;IAEDppB,KAAK,CAAC4oB,wBAAwB,GAAG,MAAM;AACrC,MAAA,MAAMS,kBAAkB,GAAGrpB,KAAK,CAC7B8kB,qBAAqB,EAAE,CACvBlY,QAAQ,CAACxI,MAAM,CAACnE,GAAG,IAAIA,GAAG,CAACwoB,YAAY,EAAE,CAAC,CAAA;MAC7C,MAAM;AAAEV,QAAAA,YAAAA;AAAa,OAAC,GAAG/nB,KAAK,CAAC2D,QAAQ,EAAE,CAAA;AAEzC,MAAA,IAAI2lB,qBAAqB,GAAG,CAAC,CAACD,kBAAkB,CAAC9rB,MAAM,CAAA;AAEvD,MAAA,IACE+rB,qBAAqB,IACrBD,kBAAkB,CAAChrB,IAAI,CAAC4B,GAAG,IAAI,CAAC8nB,YAAY,CAAC9nB,GAAG,CAACQ,EAAE,CAAC,CAAC,EACrD;AACA6oB,QAAAA,qBAAqB,GAAG,KAAK,CAAA;AAC/B,OAAA;AAEA,MAAA,OAAOA,qBAAqB,CAAA;KAC7B,CAAA;IAEDtpB,KAAK,CAACupB,qBAAqB,GAAG,MAAM;AAAA,MAAA,IAAAC,qBAAA,CAAA;MAClC,MAAMC,aAAa,GAAGxX,MAAM,CAAC6O,IAAI,CAAA0I,CAAAA,qBAAA,GAC/BxpB,KAAK,CAAC2D,QAAQ,EAAE,CAACokB,YAAY,KAAAyB,IAAAA,GAAAA,qBAAA,GAAI,EACnC,CAAC,CAACjsB,MAAM,CAAA;AACR,MAAA,OACEksB,aAAa,GAAG,CAAC,IACjBA,aAAa,GAAGzpB,KAAK,CAACgP,mBAAmB,EAAE,CAACpC,QAAQ,CAACrP,MAAM,CAAA;KAE9D,CAAA;IAEDyC,KAAK,CAAC0pB,yBAAyB,GAAG,MAAM;MACtC,MAAML,kBAAkB,GAAGrpB,KAAK,CAAC8kB,qBAAqB,EAAE,CAAClY,QAAQ,CAAA;AACjE,MAAA,OAAO5M,KAAK,CAAC4oB,wBAAwB,EAAE,GACnC,KAAK,GACLS,kBAAkB,CACfjlB,MAAM,CAACnE,GAAG,IAAIA,GAAG,CAACwoB,YAAY,EAAE,CAAC,CACjCpqB,IAAI,CAAC/B,CAAC,IAAIA,CAAC,CAACqtB,aAAa,EAAE,IAAIrtB,CAAC,CAACstB,iBAAiB,EAAE,CAAC,CAAA;KAC7D,CAAA;IAED5pB,KAAK,CAAC6pB,+BAA+B,GAAG,MAAM;AAC5C,MAAA,OAAQzQ,CAAU,IAAK;QACrBpZ,KAAK,CAACsoB,qBAAqB,CACvBlP,CAAC,CAAgB8D,MAAM,CAAsBC,OACjD,CAAC,CAAA;OACF,CAAA;KACF,CAAA;IAEDnd,KAAK,CAAC8pB,mCAAmC,GAAG,MAAM;AAChD,MAAA,OAAQ1Q,CAAU,IAAK;QACrBpZ,KAAK,CAAC0oB,yBAAyB,CAC3BtP,CAAC,CAAgB8D,MAAM,CAAsBC,OACjD,CAAC,CAAA;OACF,CAAA;KACF,CAAA;GACF;AAEDtV,EAAAA,SAAS,EAAEA,CACT5H,GAAe,EACfD,KAAmB,KACV;AACTC,IAAAA,GAAG,CAAC8pB,cAAc,GAAG,CAACld,KAAK,EAAElP,IAAI,KAAK;AACpC,MAAA,MAAMqsB,UAAU,GAAG/pB,GAAG,CAAC0pB,aAAa,EAAE,CAAA;AAEtC3pB,MAAAA,KAAK,CAACooB,eAAe,CAAChsB,GAAG,IAAI;AAAA,QAAA,IAAA6tB,oBAAA,CAAA;QAC3Bpd,KAAK,GAAG,OAAOA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,CAACmd,UAAU,CAAA;QAE1D,IAAI/pB,GAAG,CAACwoB,YAAY,EAAE,IAAIuB,UAAU,KAAKnd,KAAK,EAAE;AAC9C,UAAA,OAAOzQ,GAAG,CAAA;AACZ,SAAA;AAEA,QAAA,MAAM8tB,cAAc,GAAG;UAAE,GAAG9tB,GAAAA;SAAK,CAAA;QAEjCysB,mBAAmB,CACjBqB,cAAc,EACdjqB,GAAG,CAACQ,EAAE,EACNoM,KAAK,EAAA,CAAAod,oBAAA,GACLtsB,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEwsB,cAAc,KAAA,IAAA,GAAAF,oBAAA,GAAI,IAAI,EAC5BjqB,KACF,CAAC,CAAA;AAED,QAAA,OAAOkqB,cAAc,CAAA;AACvB,OAAC,CAAC,CAAA;KACH,CAAA;IACDjqB,GAAG,CAAC0pB,aAAa,GAAG,MAAM;MACxB,MAAM;AAAE5B,QAAAA,YAAAA;AAAa,OAAC,GAAG/nB,KAAK,CAAC2D,QAAQ,EAAE,CAAA;AACzC,MAAA,OAAOymB,aAAa,CAACnqB,GAAG,EAAE8nB,YAAY,CAAC,CAAA;KACxC,CAAA;IAED9nB,GAAG,CAAC2pB,iBAAiB,GAAG,MAAM;MAC5B,MAAM;AAAE7B,QAAAA,YAAAA;AAAa,OAAC,GAAG/nB,KAAK,CAAC2D,QAAQ,EAAE,CAAA;MACzC,OAAO0mB,gBAAgB,CAACpqB,GAAG,EAAE8nB,YAAmB,CAAC,KAAK,MAAM,CAAA;KAC7D,CAAA;IAED9nB,GAAG,CAACqqB,uBAAuB,GAAG,MAAM;MAClC,MAAM;AAAEvC,QAAAA,YAAAA;AAAa,OAAC,GAAG/nB,KAAK,CAAC2D,QAAQ,EAAE,CAAA;MACzC,OAAO0mB,gBAAgB,CAACpqB,GAAG,EAAE8nB,YAAmB,CAAC,KAAK,KAAK,CAAA;KAC5D,CAAA;IAED9nB,GAAG,CAACwoB,YAAY,GAAG,MAAM;AAAA,MAAA,IAAAtb,qBAAA,CAAA;MACvB,IAAI,OAAOnN,KAAK,CAACO,OAAO,CAAC0nB,kBAAkB,KAAK,UAAU,EAAE;AAC1D,QAAA,OAAOjoB,KAAK,CAACO,OAAO,CAAC0nB,kBAAkB,CAAChoB,GAAG,CAAC,CAAA;AAC9C,OAAA;MAEA,OAAAkN,CAAAA,qBAAA,GAAOnN,KAAK,CAACO,OAAO,CAAC0nB,kBAAkB,KAAA,IAAA,GAAA9a,qBAAA,GAAI,IAAI,CAAA;KAChD,CAAA;IAEDlN,GAAG,CAACsqB,mBAAmB,GAAG,MAAM;AAAA,MAAA,IAAAnd,sBAAA,CAAA;MAC9B,IAAI,OAAOpN,KAAK,CAACO,OAAO,CAAC4nB,qBAAqB,KAAK,UAAU,EAAE;AAC7D,QAAA,OAAOnoB,KAAK,CAACO,OAAO,CAAC4nB,qBAAqB,CAACloB,GAAG,CAAC,CAAA;AACjD,OAAA;MAEA,OAAAmN,CAAAA,sBAAA,GAAOpN,KAAK,CAACO,OAAO,CAAC4nB,qBAAqB,KAAA,IAAA,GAAA/a,sBAAA,GAAI,IAAI,CAAA;KACnD,CAAA;IAEDnN,GAAG,CAACuqB,iBAAiB,GAAG,MAAM;AAAA,MAAA,IAAAC,sBAAA,CAAA;MAC5B,IAAI,OAAOzqB,KAAK,CAACO,OAAO,CAAC2nB,uBAAuB,KAAK,UAAU,EAAE;AAC/D,QAAA,OAAOloB,KAAK,CAACO,OAAO,CAAC2nB,uBAAuB,CAACjoB,GAAG,CAAC,CAAA;AACnD,OAAA;MAEA,OAAAwqB,CAAAA,sBAAA,GAAOzqB,KAAK,CAACO,OAAO,CAAC2nB,uBAAuB,KAAA,IAAA,GAAAuC,sBAAA,GAAI,IAAI,CAAA;KACrD,CAAA;IACDxqB,GAAG,CAACyqB,wBAAwB,GAAG,MAAM;AACnC,MAAA,MAAMC,SAAS,GAAG1qB,GAAG,CAACwoB,YAAY,EAAE,CAAA;AAEpC,MAAA,OAAQrP,CAAU,IAAK;AAAA,QAAA,IAAA+E,OAAA,CAAA;QACrB,IAAI,CAACwM,SAAS,EAAE,OAAA;AAChB1qB,QAAAA,GAAG,CAAC8pB,cAAc,CAAA5L,CAAAA,OAAA,GACd/E,CAAC,CAAgB8D,MAAM,KAAzBiB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAA,CAAgDhB,OAClD,CAAC,CAAA;OACF,CAAA;KACF,CAAA;AACH,GAAA;AACF,EAAC;AAED,MAAM0L,mBAAmB,GAAGA,CAC1BqB,cAAuC,EACvCzpB,EAAU,EACVoM,KAAc,EACd+d,eAAwB,EACxB5qB,KAAmB,KAChB;AAAA,EAAA,IAAAiT,YAAA,CAAA;EACH,MAAMhT,GAAG,GAAGD,KAAK,CAAC0I,MAAM,CAACjI,EAAE,EAAE,IAAI,CAAC,CAAA;;AAElC;;AAEA;AACA;AACA;AACA;AACA,EAAA,IAAIoM,KAAK,EAAE;AACT,IAAA,IAAI,CAAC5M,GAAG,CAACuqB,iBAAiB,EAAE,EAAE;AAC5BvY,MAAAA,MAAM,CAAC6O,IAAI,CAACoJ,cAAc,CAAC,CAAC/sB,OAAO,CAAClB,GAAG,IAAI,OAAOiuB,cAAc,CAACjuB,GAAG,CAAC,CAAC,CAAA;AACxE,KAAA;AACA,IAAA,IAAIgE,GAAG,CAACwoB,YAAY,EAAE,EAAE;AACtByB,MAAAA,cAAc,CAACzpB,EAAE,CAAC,GAAG,IAAI,CAAA;AAC3B,KAAA;AACF,GAAC,MAAM;IACL,OAAOypB,cAAc,CAACzpB,EAAE,CAAC,CAAA;AAC3B,GAAA;AACA;;AAEA,EAAA,IAAImqB,eAAe,IAAA3X,CAAAA,YAAA,GAAIhT,GAAG,CAAC+H,OAAO,KAAA,IAAA,IAAXiL,YAAA,CAAa1V,MAAM,IAAI0C,GAAG,CAACsqB,mBAAmB,EAAE,EAAE;IACvEtqB,GAAG,CAAC+H,OAAO,CAAC7K,OAAO,CAAC8C,GAAG,IACrB4oB,mBAAmB,CAACqB,cAAc,EAAEjqB,GAAG,CAACQ,EAAE,EAAEoM,KAAK,EAAE+d,eAAe,EAAE5qB,KAAK,CAC3E,CAAC,CAAA;AACH,GAAA;AACF,CAAC,CAAA;AAEM,SAASipB,YAAYA,CAC1BjpB,KAAmB,EACnBgpB,QAAyB,EACR;EACjB,MAAMjB,YAAY,GAAG/nB,KAAK,CAAC2D,QAAQ,EAAE,CAACokB,YAAY,CAAA;EAElD,MAAM8C,mBAAiC,GAAG,EAAE,CAAA;EAC5C,MAAMC,mBAA+C,GAAG,EAAE,CAAA;;AAE1D;AACA,EAAA,MAAMC,WAAW,GAAG,UAAC1F,IAAkB,EAAErkB,KAAK,EAAuB;AACnE,IAAA,OAAOqkB,IAAI,CACRhiB,GAAG,CAACpD,GAAG,IAAI;AAAA,MAAA,IAAA+qB,aAAA,CAAA;AACV,MAAA,MAAMhB,UAAU,GAAGI,aAAa,CAACnqB,GAAG,EAAE8nB,YAAY,CAAC,CAAA;AAEnD,MAAA,IAAIiC,UAAU,EAAE;AACda,QAAAA,mBAAmB,CAACxtB,IAAI,CAAC4C,GAAG,CAAC,CAAA;AAC7B6qB,QAAAA,mBAAmB,CAAC7qB,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;AACnC,OAAA;MAEA,IAAA+qB,CAAAA,aAAA,GAAI/qB,GAAG,CAAC+H,OAAO,KAAXgjB,IAAAA,IAAAA,aAAA,CAAaztB,MAAM,EAAE;AACvB0C,QAAAA,GAAG,GAAG;AACJ,UAAA,GAAGA,GAAG;UACN+H,OAAO,EAAE+iB,WAAW,CAAC9qB,GAAG,CAAC+H,OAAkB,CAAA;SAC5C,CAAA;AACH,OAAA;AAEA,MAAA,IAAIgiB,UAAU,EAAE;AACd,QAAA,OAAO/pB,GAAG,CAAA;AACZ,OAAA;AACF,KAAC,CAAC,CACDmE,MAAM,CAACC,OAAO,CAAC,CAAA;GACnB,CAAA;EAED,OAAO;AACLghB,IAAAA,IAAI,EAAE0F,WAAW,CAAC/B,QAAQ,CAAC3D,IAAI,CAAC;AAChCzY,IAAAA,QAAQ,EAAEie,mBAAmB;AAC7B1J,IAAAA,QAAQ,EAAE2J,mBAAAA;GACX,CAAA;AACH,CAAA;AAEO,SAASV,aAAaA,CAC3BnqB,GAAe,EACfgrB,SAAkC,EACzB;AAAA,EAAA,IAAAC,iBAAA,CAAA;EACT,OAAAA,CAAAA,iBAAA,GAAOD,SAAS,CAAChrB,GAAG,CAACQ,EAAE,CAAC,KAAA,IAAA,GAAAyqB,iBAAA,GAAI,KAAK,CAAA;AACnC,CAAA;AAEO,SAASb,gBAAgBA,CAC9BpqB,GAAe,EACfgrB,SAAkC,EAClCjrB,KAAmB,EACO;AAAA,EAAA,IAAAmrB,aAAA,CAAA;AAC1B,EAAA,IAAI,EAAAA,CAAAA,aAAA,GAAClrB,GAAG,CAAC+H,OAAO,KAAXmjB,IAAAA,IAAAA,aAAA,CAAa5tB,MAAM,CAAE,EAAA,OAAO,KAAK,CAAA;EAEtC,IAAI6tB,mBAAmB,GAAG,IAAI,CAAA;EAC9B,IAAIC,YAAY,GAAG,KAAK,CAAA;AAExBprB,EAAAA,GAAG,CAAC+H,OAAO,CAAC7K,OAAO,CAACmuB,MAAM,IAAI;AAC5B;AACA,IAAA,IAAID,YAAY,IAAI,CAACD,mBAAmB,EAAE;AACxC,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,IAAIE,MAAM,CAAC7C,YAAY,EAAE,EAAE;AACzB,MAAA,IAAI2B,aAAa,CAACkB,MAAM,EAAEL,SAAS,CAAC,EAAE;AACpCI,QAAAA,YAAY,GAAG,IAAI,CAAA;AACrB,OAAC,MAAM;AACLD,QAAAA,mBAAmB,GAAG,KAAK,CAAA;AAC7B,OAAA;AACF,KAAA;;AAEA;IACA,IAAIE,MAAM,CAACtjB,OAAO,IAAIsjB,MAAM,CAACtjB,OAAO,CAACzK,MAAM,EAAE;MAC3C,MAAMguB,sBAAsB,GAAGlB,gBAAgB,CAACiB,MAAM,EAAEL,SAAgB,CAAC,CAAA;MACzE,IAAIM,sBAAsB,KAAK,KAAK,EAAE;AACpCF,QAAAA,YAAY,GAAG,IAAI,CAAA;AACrB,OAAC,MAAM,IAAIE,sBAAsB,KAAK,MAAM,EAAE;AAC5CF,QAAAA,YAAY,GAAG,IAAI,CAAA;AACnBD,QAAAA,mBAAmB,GAAG,KAAK,CAAA;AAC7B,OAAC,MAAM;AACLA,QAAAA,mBAAmB,GAAG,KAAK,CAAA;AAC7B,OAAA;AACF,KAAA;AACF,GAAC,CAAC,CAAA;EAEF,OAAOA,mBAAmB,GAAG,KAAK,GAAGC,YAAY,GAAG,MAAM,GAAG,KAAK,CAAA;AACpE;;ACzpBO,MAAMG,mBAAmB,GAAG,aAAY;AAE/C,MAAMC,YAA4B,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEzrB,QAAQ,KAAK;AAC7D,EAAA,OAAO0rB,mBAAmB,CACxBzhB,QAAQ,CAACuhB,IAAI,CAACprB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAACgK,WAAW,EAAE,EAC/CC,QAAQ,CAACwhB,IAAI,CAACrrB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAACgK,WAAW,EAC/C,CAAC,CAAA;AACH,CAAC,CAAA;AAED,MAAM2hB,yBAAyC,GAAGA,CAACH,IAAI,EAAEC,IAAI,EAAEzrB,QAAQ,KAAK;EAC1E,OAAO0rB,mBAAmB,CACxBzhB,QAAQ,CAACuhB,IAAI,CAACprB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,EACjCiK,QAAQ,CAACwhB,IAAI,CAACrrB,QAAQ,CAACJ,QAAQ,CAAC,CAClC,CAAC,CAAA;AACH,CAAC,CAAA;;AAED;AACA;AACA,MAAM4rB,IAAoB,GAAGA,CAACJ,IAAI,EAAEC,IAAI,EAAEzrB,QAAQ,KAAK;AACrD,EAAA,OAAO6rB,YAAY,CACjB5hB,QAAQ,CAACuhB,IAAI,CAACprB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAACgK,WAAW,EAAE,EAC/CC,QAAQ,CAACwhB,IAAI,CAACrrB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAACgK,WAAW,EAC/C,CAAC,CAAA;AACH,CAAC,CAAA;;AAED;AACA;AACA,MAAM8hB,iBAAiC,GAAGA,CAACN,IAAI,EAAEC,IAAI,EAAEzrB,QAAQ,KAAK;EAClE,OAAO6rB,YAAY,CACjB5hB,QAAQ,CAACuhB,IAAI,CAACprB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,EACjCiK,QAAQ,CAACwhB,IAAI,CAACrrB,QAAQ,CAACJ,QAAQ,CAAC,CAClC,CAAC,CAAA;AACH,CAAC,CAAA;AAED,MAAM+rB,QAAwB,GAAGA,CAACP,IAAI,EAAEC,IAAI,EAAEzrB,QAAQ,KAAK;AACzD,EAAA,MAAMgQ,CAAC,GAAGwb,IAAI,CAACprB,QAAQ,CAAOJ,QAAQ,CAAC,CAAA;AACvC,EAAA,MAAMiQ,CAAC,GAAGwb,IAAI,CAACrrB,QAAQ,CAAOJ,QAAQ,CAAC,CAAA;;AAEvC;AACA;AACA;AACA,EAAA,OAAOgQ,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AACnC,CAAC,CAAA;AAED,MAAM+b,KAAqB,GAAGA,CAACR,IAAI,EAAEC,IAAI,EAAEzrB,QAAQ,KAAK;AACtD,EAAA,OAAO6rB,YAAY,CAACL,IAAI,CAACprB,QAAQ,CAACJ,QAAQ,CAAC,EAAEyrB,IAAI,CAACrrB,QAAQ,CAACJ,QAAQ,CAAC,CAAC,CAAA;AACvE,CAAC,CAAA;;AAED;;AAEA,SAAS6rB,YAAYA,CAAC7b,CAAM,EAAEC,CAAM,EAAE;AACpC,EAAA,OAAOD,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;AACrC,CAAA;AAEA,SAAShG,QAAQA,CAAC+F,CAAM,EAAE;AACxB,EAAA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;AACzB,IAAA,IAAIvE,KAAK,CAACuE,CAAC,CAAC,IAAIA,CAAC,KAAKtE,QAAQ,IAAIsE,CAAC,KAAK,CAACtE,QAAQ,EAAE;AACjD,MAAA,OAAO,EAAE,CAAA;AACX,KAAA;IACA,OAAO1M,MAAM,CAACgR,CAAC,CAAC,CAAA;AAClB,GAAA;AACA,EAAA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;AACzB,IAAA,OAAOA,CAAC,CAAA;AACV,GAAA;AACA,EAAA,OAAO,EAAE,CAAA;AACX,CAAA;;AAEA;AACA;AACA;AACA,SAAS0b,mBAAmBA,CAACO,IAAY,EAAEC,IAAY,EAAE;AACvD;AACA;AACA,EAAA,MAAMlc,CAAC,GAAGic,IAAI,CAACvqB,KAAK,CAAC4pB,mBAAmB,CAAC,CAACpnB,MAAM,CAACC,OAAO,CAAC,CAAA;AACzD,EAAA,MAAM8L,CAAC,GAAGic,IAAI,CAACxqB,KAAK,CAAC4pB,mBAAmB,CAAC,CAACpnB,MAAM,CAACC,OAAO,CAAC,CAAA;;AAEzD;AACA,EAAA,OAAO6L,CAAC,CAAC3S,MAAM,IAAI4S,CAAC,CAAC5S,MAAM,EAAE;AAC3B,IAAA,MAAM8uB,EAAE,GAAGnc,CAAC,CAACoE,KAAK,EAAG,CAAA;AACrB,IAAA,MAAMgY,EAAE,GAAGnc,CAAC,CAACmE,KAAK,EAAG,CAAA;AAErB,IAAA,MAAMiY,EAAE,GAAGC,QAAQ,CAACH,EAAE,EAAE,EAAE,CAAC,CAAA;AAC3B,IAAA,MAAMI,EAAE,GAAGD,QAAQ,CAACF,EAAE,EAAE,EAAE,CAAC,CAAA;IAE3B,MAAMI,KAAK,GAAG,CAACH,EAAE,EAAEE,EAAE,CAAC,CAACxc,IAAI,EAAE,CAAA;;AAE7B;AACA,IAAA,IAAItE,KAAK,CAAC+gB,KAAK,CAAC,CAAC,CAAE,CAAC,EAAE;MACpB,IAAIL,EAAE,GAAGC,EAAE,EAAE;AACX,QAAA,OAAO,CAAC,CAAA;AACV,OAAA;MACA,IAAIA,EAAE,GAAGD,EAAE,EAAE;AACX,QAAA,OAAO,CAAC,CAAC,CAAA;AACX,OAAA;AACA,MAAA,SAAA;AACF,KAAA;;AAEA;AACA,IAAA,IAAI1gB,KAAK,CAAC+gB,KAAK,CAAC,CAAC,CAAE,CAAC,EAAE;MACpB,OAAO/gB,KAAK,CAAC4gB,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AAC3B,KAAA;;AAEA;IACA,IAAIA,EAAE,GAAGE,EAAE,EAAE;AACX,MAAA,OAAO,CAAC,CAAA;AACV,KAAA;IACA,IAAIA,EAAE,GAAGF,EAAE,EAAE;AACX,MAAA,OAAO,CAAC,CAAC,CAAA;AACX,KAAA;AACF,GAAA;AAEA,EAAA,OAAOrc,CAAC,CAAC3S,MAAM,GAAG4S,CAAC,CAAC5S,MAAM,CAAA;AAC5B,CAAA;;AAEA;;AAEO,MAAMovB,UAAU,GAAG;EACxBlB,YAAY;EACZI,yBAAyB;EACzBC,IAAI;EACJE,iBAAiB;EACjBC,QAAQ;AACRC,EAAAA,KAAAA;AACF;;ACsJA;;AAEO,MAAMU,UAAwB,GAAG;EACtC1gB,eAAe,EAAGC,KAAK,IAAwB;IAC7C,OAAO;AACL0gB,MAAAA,OAAO,EAAE,EAAE;MACX,GAAG1gB,KAAAA;KACJ,CAAA;GACF;EAEDH,mBAAmB,EAAEA,MAAsD;IACzE,OAAO;AACL8gB,MAAAA,SAAS,EAAE,MAAM;AACjBC,MAAAA,aAAa,EAAE,CAAA;KAChB,CAAA;GACF;EAED1gB,iBAAiB,EACfrM,KAAmB,IACO;IAC1B,OAAO;AACLgtB,MAAAA,eAAe,EAAEhxB,gBAAgB,CAAC,SAAS,EAAEgE,KAAK,CAAC;MACnDitB,gBAAgB,EAAG7T,CAAU,IAAK;QAChC,OAAQA,CAAC,CAAgB8T,QAAQ,CAAA;AACnC,OAAA;KACD,CAAA;GACF;AAEDpsB,EAAAA,YAAY,EAAEA,CACZvF,MAA6B,EAC7ByE,KAAmB,KACV;IACTzE,MAAM,CAAC4xB,gBAAgB,GAAG,MAAM;AAC9B,MAAA,MAAMC,SAAS,GAAGptB,KAAK,CAACgP,mBAAmB,EAAE,CAACpC,QAAQ,CAACwL,KAAK,CAAC,EAAE,CAAC,CAAA;MAEhE,IAAIiV,QAAQ,GAAG,KAAK,CAAA;AAEpB,MAAA,KAAK,MAAMptB,GAAG,IAAImtB,SAAS,EAAE;QAC3B,MAAMvgB,KAAK,GAAG5M,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEK,QAAQ,CAAC/E,MAAM,CAACkF,EAAE,CAAC,CAAA;AAEtC,QAAA,IAAIwR,MAAM,CAACC,SAAS,CAAC/H,QAAQ,CAACgI,IAAI,CAACtF,KAAK,CAAC,KAAK,eAAe,EAAE;UAC7D,OAAO8f,UAAU,CAACV,QAAQ,CAAA;AAC5B,SAAA;AAEA,QAAA,IAAI,OAAOpf,KAAK,KAAK,QAAQ,EAAE;AAC7BwgB,UAAAA,QAAQ,GAAG,IAAI,CAAA;UAEf,IAAIxgB,KAAK,CAACjL,KAAK,CAAC4pB,mBAAmB,CAAC,CAACjuB,MAAM,GAAG,CAAC,EAAE;YAC/C,OAAOovB,UAAU,CAAClB,YAAY,CAAA;AAChC,WAAA;AACF,SAAA;AACF,OAAA;AAEA,MAAA,IAAI4B,QAAQ,EAAE;QACZ,OAAOV,UAAU,CAACb,IAAI,CAAA;AACxB,OAAA;MAEA,OAAOa,UAAU,CAACT,KAAK,CAAA;KACxB,CAAA;IACD3wB,MAAM,CAAC+xB,cAAc,GAAG,MAAM;MAC5B,MAAM5gB,QAAQ,GAAG1M,KAAK,CAACgP,mBAAmB,EAAE,CAACpC,QAAQ,CAAC,CAAC,CAAC,CAAA;MAExD,MAAMC,KAAK,GAAGH,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAEpM,QAAQ,CAAC/E,MAAM,CAACkF,EAAE,CAAC,CAAA;AAE3C,MAAA,IAAI,OAAOoM,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;AAEA,MAAA,OAAO,MAAM,CAAA;KACd,CAAA;IACDtR,MAAM,CAACgyB,YAAY,GAAG,MAAM;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAC1B,IAAI,CAAClyB,MAAM,EAAE;QACX,MAAM,IAAIwG,KAAK,EAAE,CAAA;AACnB,OAAA;MAEA,OAAO1F,UAAU,CAACd,MAAM,CAACwF,SAAS,CAAC+rB,SAAS,CAAC,GACzCvxB,MAAM,CAACwF,SAAS,CAAC+rB,SAAS,GAC1BvxB,MAAM,CAACwF,SAAS,CAAC+rB,SAAS,KAAK,MAAM,GACnCvxB,MAAM,CAAC4xB,gBAAgB,EAAE,IAAAK,qBAAA,GAAA,CAAAC,sBAAA,GACzBztB,KAAK,CAACO,OAAO,CAACosB,UAAU,KAAA,IAAA,GAAA,KAAA,CAAA,GAAxBc,sBAAA,CAA2BlyB,MAAM,CAACwF,SAAS,CAAC+rB,SAAS,CAAW,KAAAU,IAAAA,GAAAA,qBAAA,GAChEb,UAAU,CAACpxB,MAAM,CAACwF,SAAS,CAAC+rB,SAAS,CAAqB,CAAA;KACjE,CAAA;AACDvxB,IAAAA,MAAM,CAACmyB,aAAa,GAAG,CAACC,IAAI,EAAEC,KAAK,KAAK;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAA,MAAMC,gBAAgB,GAAGtyB,MAAM,CAACuyB,mBAAmB,EAAE,CAAA;MACrD,MAAMC,cAAc,GAAG,OAAOJ,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,IAAI,CAAA;AAEnE3tB,MAAAA,KAAK,CAACguB,UAAU,CAAC5xB,GAAG,IAAI;AACtB;AACA,QAAA,MAAM6xB,eAAe,GAAG7xB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE+H,IAAI,CAAC7H,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,CAAA;AAC1D,QAAA,MAAMytB,aAAa,GAAG9xB,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE0R,SAAS,CAACxR,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,CAAA;QAE7D,IAAI0tB,UAAwB,GAAG,EAAE,CAAA;;AAEjC;AACA,QAAA,IAAIC,UAAmD,CAAA;QACvD,IAAIC,QAAQ,GAAGN,cAAc,GAAGJ,IAAI,GAAGE,gBAAgB,KAAK,MAAM,CAAA;;AAElE;AACA,QAAA,IAAIzxB,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEmB,MAAM,IAAIhC,MAAM,CAAC+yB,eAAe,EAAE,IAAIV,KAAK,EAAE;AACpD,UAAA,IAAIK,eAAe,EAAE;AACnBG,YAAAA,UAAU,GAAG,QAAQ,CAAA;AACvB,WAAC,MAAM;AACLA,YAAAA,UAAU,GAAG,KAAK,CAAA;AACpB,WAAA;AACF,SAAC,MAAM;AACL;AACA,UAAA,IAAIhyB,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEmB,MAAM,IAAI2wB,aAAa,KAAK9xB,GAAG,CAACmB,MAAM,GAAG,CAAC,EAAE;AACnD6wB,YAAAA,UAAU,GAAG,SAAS,CAAA;WACvB,MAAM,IAAIH,eAAe,EAAE;AAC1BG,YAAAA,UAAU,GAAG,QAAQ,CAAA;AACvB,WAAC,MAAM;AACLA,YAAAA,UAAU,GAAG,SAAS,CAAA;AACxB,WAAA;AACF,SAAA;;AAEA;QACA,IAAIA,UAAU,KAAK,QAAQ,EAAE;AAC3B;UACA,IAAI,CAACL,cAAc,EAAE;AACnB;YACA,IAAI,CAACF,gBAAgB,EAAE;AACrBO,cAAAA,UAAU,GAAG,QAAQ,CAAA;AACvB,aAAA;AACF,WAAA;AACF,SAAA;QAEA,IAAIA,UAAU,KAAK,KAAK,EAAE;AAAA,UAAA,IAAAG,qBAAA,CAAA;AACxBJ,UAAAA,UAAU,GAAG,CACX,GAAG/xB,GAAG,EACN;YACEqE,EAAE,EAAElF,MAAM,CAACkF,EAAE;AACbktB,YAAAA,IAAI,EAAEU,QAAAA;AACR,WAAC,CACF,CAAA;AACD;UACAF,UAAU,CAAC3Z,MAAM,CACf,CAAC,EACD2Z,UAAU,CAAC5wB,MAAM,IAAA,CAAAgxB,qBAAA,GACdvuB,KAAK,CAACO,OAAO,CAACiuB,oBAAoB,KAAAD,IAAAA,GAAAA,qBAAA,GAAI7iB,MAAM,CAACsL,gBAAgB,CAClE,CAAC,CAAA;AACH,SAAC,MAAM,IAAIoX,UAAU,KAAK,QAAQ,EAAE;AAClC;AACAD,UAAAA,UAAU,GAAG/xB,GAAG,CAACiH,GAAG,CAAC/G,CAAC,IAAI;AACxB,YAAA,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,EAAE;cACtB,OAAO;AACL,gBAAA,GAAGnE,CAAC;AACJqxB,gBAAAA,IAAI,EAAEU,QAAAA;eACP,CAAA;AACH,aAAA;AACA,YAAA,OAAO/xB,CAAC,CAAA;AACV,WAAC,CAAC,CAAA;AACJ,SAAC,MAAM,IAAI8xB,UAAU,KAAK,QAAQ,EAAE;AAClCD,UAAAA,UAAU,GAAG/xB,GAAG,CAACgI,MAAM,CAAC9H,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,CAAA;AAClD,SAAC,MAAM;AACL0tB,UAAAA,UAAU,GAAG,CACX;YACE1tB,EAAE,EAAElF,MAAM,CAACkF,EAAE;AACbktB,YAAAA,IAAI,EAAEU,QAAAA;AACR,WAAC,CACF,CAAA;AACH,SAAA;AAEA,QAAA,OAAOF,UAAU,CAAA;AACnB,OAAC,CAAC,CAAA;KACH,CAAA;IAED5yB,MAAM,CAACkzB,eAAe,GAAG,MAAM;MAAA,IAAAvtB,IAAA,EAAAwtB,qBAAA,CAAA;AAC7B,MAAA,MAAMC,aAAa,GAAA,CAAAztB,IAAA,GAAA,CAAAwtB,qBAAA,GACjBnzB,MAAM,CAACwF,SAAS,CAAC4tB,aAAa,KAAA,IAAA,GAAAD,qBAAA,GAC9B1uB,KAAK,CAACO,OAAO,CAACouB,aAAa,KAAA,IAAA,GAAAztB,IAAA,GAC3B3F,MAAM,CAAC+xB,cAAc,EAAE,KAAK,MAAM,CAAA;AACpC,MAAA,OAAOqB,aAAa,GAAG,MAAM,GAAG,KAAK,CAAA;KACtC,CAAA;AAEDpzB,IAAAA,MAAM,CAACuyB,mBAAmB,GAAIF,KAAe,IAAK;MAAA,IAAAzgB,qBAAA,EAAAC,sBAAA,CAAA;AAChD,MAAA,MAAMwhB,kBAAkB,GAAGrzB,MAAM,CAACkzB,eAAe,EAAE,CAAA;AACnD,MAAA,MAAMI,QAAQ,GAAGtzB,MAAM,CAACuzB,WAAW,EAAE,CAAA;MAErC,IAAI,CAACD,QAAQ,EAAE;AACb,QAAA,OAAOD,kBAAkB,CAAA;AAC3B,OAAA;AAEA,MAAA,IACEC,QAAQ,KAAKD,kBAAkB,KAAA,CAAAzhB,qBAAA,GAC9BnN,KAAK,CAACO,OAAO,CAACwuB,oBAAoB,KAAA,IAAA,GAAA5hB,qBAAA,GAAI,IAAI,CAAC;AAAI;AAC/CygB,MAAAA,KAAK,GAAAxgB,CAAAA,sBAAA,GAAGpN,KAAK,CAACO,OAAO,CAACyuB,iBAAiB,KAAA,IAAA,GAAA5hB,sBAAA,GAAI,IAAI,GAAG,IAAI,CAAC;QACxD;AACA,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;AACA,MAAA,OAAOyhB,QAAQ,KAAK,MAAM,GAAG,KAAK,GAAG,MAAM,CAAA;KAC5C,CAAA;IAEDtzB,MAAM,CAAC0zB,UAAU,GAAG,MAAM;MAAA,IAAA/hB,qBAAA,EAAAud,sBAAA,CAAA;AACxB,MAAA,OACE,CAAAvd,CAAAA,qBAAA,GAAC3R,MAAM,CAACwF,SAAS,CAACmuB,aAAa,KAAAhiB,IAAAA,GAAAA,qBAAA,GAAI,IAAI,OAAAud,sBAAA,GACtCzqB,KAAK,CAACO,OAAO,CAAC2uB,aAAa,KAAA,IAAA,GAAAzE,sBAAA,GAAI,IAAI,CAAC,IACrC,CAAC,CAAClvB,MAAM,CAACC,UAAU,CAAA;KAEtB,CAAA;IAEDD,MAAM,CAAC+yB,eAAe,GAAG,MAAM;MAAA,IAAA9V,KAAA,EAAA2W,sBAAA,CAAA;MAC7B,OAAA3W,CAAAA,KAAA,GAAA2W,CAAAA,sBAAA,GACE5zB,MAAM,CAACwF,SAAS,CAACquB,eAAe,KAAA,IAAA,GAAAD,sBAAA,GAChCnvB,KAAK,CAACO,OAAO,CAAC6uB,eAAe,KAAA5W,IAAAA,GAAAA,KAAA,GAC7B,CAAC,CAACjd,MAAM,CAACC,UAAU,CAAA;KAEtB,CAAA;IAEDD,MAAM,CAACuzB,WAAW,GAAG,MAAM;AAAA,MAAA,IAAAO,qBAAA,CAAA;MACzB,MAAMC,UAAU,GAAAD,CAAAA,qBAAA,GAAGrvB,KAAK,CAAC2D,QAAQ,EAAE,CAACkpB,OAAO,KAAA,IAAA,GAAA,KAAA,CAAA,GAAxBwC,qBAAA,CAA0BlrB,IAAI,CAAC7H,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,CAAA;MAE1E,OAAO,CAAC6uB,UAAU,GAAG,KAAK,GAAGA,UAAU,CAAC3B,IAAI,GAAG,MAAM,GAAG,KAAK,CAAA;KAC9D,CAAA;IAEDpyB,MAAM,CAACg0B,YAAY,GAAG,MAAA;MAAA,IAAAC,sBAAA,EAAAC,sBAAA,CAAA;AAAA,MAAA,OAAA,CAAAD,sBAAA,GAAA,CAAAC,sBAAA,GACpBzvB,KAAK,CAAC2D,QAAQ,EAAE,CAACkpB,OAAO,KAAA,IAAA,GAAA,KAAA,CAAA,GAAxB4C,sBAAA,CAA0B3hB,SAAS,CAACxR,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,KAAA,IAAA,GAAA+uB,sBAAA,GAAI,CAAC,CAAC,CAAA;AAAA,KAAA,CAAA;IAEpEj0B,MAAM,CAACm0B,YAAY,GAAG,MAAM;AAC1B;MACA1vB,KAAK,CAACguB,UAAU,CAAC5xB,GAAG,IAClBA,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEmB,MAAM,GAAGnB,GAAG,CAACgI,MAAM,CAAC9H,CAAC,IAAIA,CAAC,CAACmE,EAAE,KAAKlF,MAAM,CAACkF,EAAE,CAAC,GAAG,EACtD,CAAC,CAAA;KACF,CAAA;IAEDlF,MAAM,CAACo0B,uBAAuB,GAAG,MAAM;AACrC,MAAA,MAAMC,OAAO,GAAGr0B,MAAM,CAAC0zB,UAAU,EAAE,CAAA;AAEnC,MAAA,OAAQ7V,CAAU,IAAK;QACrB,IAAI,CAACwW,OAAO,EAAE,OAAA;AACZxW,QAAAA,CAAC,CAASC,OAAO,IAAA,IAAA,IAAjBD,CAAC,CAASC,OAAO,EAAI,CAAA;AACvB9d,QAAAA,MAAM,CAACmyB,aAAa,IAApBnyB,IAAAA,IAAAA,MAAM,CAACmyB,aAAa,CAClBlsB,SAAS,EACTjG,MAAM,CAAC+yB,eAAe,EAAE,GAAGtuB,KAAK,CAACO,OAAO,CAAC0sB,gBAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAA9BjtB,KAAK,CAACO,OAAO,CAAC0sB,gBAAgB,CAAG7T,CAAC,CAAC,GAAG,KACnE,CAAC,CAAA;OACF,CAAA;KACF,CAAA;GACF;EAED7V,WAAW,EAA0BvD,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACguB,UAAU,GAAGnyB,OAAO,IAAImE,KAAK,CAACO,OAAO,CAACysB,eAAe,IAAA,IAAA,GAAA,KAAA,CAAA,GAA7BhtB,KAAK,CAACO,OAAO,CAACysB,eAAe,CAAGnxB,OAAO,CAAC,CAAA;AACtEmE,IAAAA,KAAK,CAAC6vB,YAAY,GAAGjhB,YAAY,IAAI;MAAA,IAAAkhB,qBAAA,EAAAhhB,mBAAA,CAAA;MACnC9O,KAAK,CAACguB,UAAU,CAACpf,YAAY,GAAG,EAAE,GAAA,CAAAkhB,qBAAA,GAAA,CAAAhhB,mBAAA,GAAG9O,KAAK,CAAC+O,YAAY,qBAAlBD,mBAAA,CAAoB+d,OAAO,KAAAiD,IAAAA,GAAAA,qBAAA,GAAI,EAAE,CAAC,CAAA;KACxE,CAAA;IACD9vB,KAAK,CAAC+vB,oBAAoB,GAAG,MAAM/vB,KAAK,CAAC0S,kBAAkB,EAAE,CAAA;IAC7D1S,KAAK,CAACshB,iBAAiB,GAAG,MAAM;MAC9B,IAAI,CAACthB,KAAK,CAACgwB,kBAAkB,IAAIhwB,KAAK,CAACO,OAAO,CAAC+gB,iBAAiB,EAAE;QAChEthB,KAAK,CAACgwB,kBAAkB,GAAGhwB,KAAK,CAACO,OAAO,CAAC+gB,iBAAiB,CAACthB,KAAK,CAAC,CAAA;AACnE,OAAA;MAEA,IAAIA,KAAK,CAACO,OAAO,CAAC0vB,aAAa,IAAI,CAACjwB,KAAK,CAACgwB,kBAAkB,EAAE;AAC5D,QAAA,OAAOhwB,KAAK,CAAC+vB,oBAAoB,EAAE,CAAA;AACrC,OAAA;AAEA,MAAA,OAAO/vB,KAAK,CAACgwB,kBAAkB,EAAE,CAAA;KAClC,CAAA;AACH,GAAA;AACF;;ACrfA,MAAME,eAAe,GAAG,CACtB5sB,OAAO,EACPkZ,gBAAgB,EAChBlJ,cAAc,EACdoB,aAAa,EACbpL,cAAc,EACdyC,eAAe,EACfqS,cAAc;AAAE;AAChBO,eAAe;AAAE;AACjBiO,UAAU,EACVjc,cAAc;AAAE;AAChB6O,YAAY,EACZoD,aAAa,EACb6C,UAAU,EACVqC,YAAY,EACZtQ,YAAY,CACJ,CAAA;;AAEV;;AAgOO,SAASjU,WAAWA,CACzBhD,OAAoC,EACtB;EAAA,IAAA4vB,kBAAA,EAAAC,qBAAA,CAAA;AACd,EAAA,IACExwB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,KACpCS,OAAO,CAACZ,QAAQ,IAAIY,OAAO,CAAC8vB,UAAU,CAAC,EACxC;AACAlxB,IAAAA,OAAO,CAACC,IAAI,CAAC,4BAA4B,CAAC,CAAA;AAC5C,GAAA;AAEA,EAAA,MAAMwB,SAAS,GAAG,CAAC,GAAGsvB,eAAe,EAAE,IAAAC,CAAAA,kBAAA,GAAI5vB,OAAO,CAACK,SAAS,KAAA,IAAA,GAAAuvB,kBAAA,GAAI,EAAE,EAAE,CAAA;AAEpE,EAAA,IAAInwB,KAAK,GAAG;AAAEY,IAAAA,SAAAA;GAAsC,CAAA;AAEpD,EAAA,MAAM0vB,cAAc,GAAGtwB,KAAK,CAACY,SAAS,CAACuI,MAAM,CAAC,CAAC6U,GAAG,EAAEnd,OAAO,KAAK;AAC9D,IAAA,OAAOoR,MAAM,CAACse,MAAM,CAACvS,GAAG,EAAEnd,OAAO,CAACwL,iBAAiB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAzBxL,OAAO,CAACwL,iBAAiB,CAAGrM,KAAK,CAAC,CAAC,CAAA;GAC9D,EAAE,EAAE,CAAgC,CAAA;EAErC,MAAMwwB,YAAY,GAAIjwB,OAAoC,IAAK;AAC7D,IAAA,IAAIP,KAAK,CAACO,OAAO,CAACiwB,YAAY,EAAE;MAC9B,OAAOxwB,KAAK,CAACO,OAAO,CAACiwB,YAAY,CAACF,cAAc,EAAE/vB,OAAO,CAAC,CAAA;AAC5D,KAAA;IAEA,OAAO;AACL,MAAA,GAAG+vB,cAAc;MACjB,GAAG/vB,OAAAA;KACJ,CAAA;GACF,CAAA;EAED,MAAMkwB,gBAAgC,GAAG,EAAE,CAAA;AAE3C,EAAA,IAAI1hB,YAAY,GAAG;AACjB,IAAA,GAAG0hB,gBAAgB;IACnB,IAAAL,CAAAA,qBAAA,GAAI7vB,OAAO,CAACwO,YAAY,KAAAqhB,IAAAA,GAAAA,qBAAA,GAAI,EAAE;GACjB,CAAA;AAEfpwB,EAAAA,KAAK,CAACY,SAAS,CAACzD,OAAO,CAAC0D,OAAO,IAAI;AAAA,IAAA,IAAA6vB,qBAAA,CAAA;AACjC3hB,IAAAA,YAAY,IAAA2hB,qBAAA,GAAI7vB,OAAO,CAACqL,eAAe,IAAvBrL,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAACqL,eAAe,CAAG6C,YAAY,CAAC,KAAA2hB,IAAAA,GAAAA,qBAAA,GACrD3hB,YAA2B,CAAA;AAC/B,GAAC,CAAC,CAAA;EAEF,MAAM8Q,MAAsB,GAAG,EAAE,CAAA;EACjC,IAAI8Q,aAAa,GAAG,KAAK,CAAA;AAEzB,EAAA,MAAMC,YAAiC,GAAG;IACxChwB,SAAS;AACTL,IAAAA,OAAO,EAAE;AACP,MAAA,GAAG+vB,cAAc;MACjB,GAAG/vB,OAAAA;KACJ;IACDwO,YAAY;IACZiR,MAAM,EAAE6Q,EAAE,IAAI;AACZhR,MAAAA,MAAM,CAACxiB,IAAI,CAACwzB,EAAE,CAAC,CAAA;MAEf,IAAI,CAACF,aAAa,EAAE;AAClBA,QAAAA,aAAa,GAAG,IAAI,CAAA;;AAEpB;AACA;AACAG,QAAAA,OAAO,CAACC,OAAO,EAAE,CACdC,IAAI,CAAC,MAAM;UACV,OAAOnR,MAAM,CAACtiB,MAAM,EAAE;AACpBsiB,YAAAA,MAAM,CAACvL,KAAK,EAAE,EAAG,CAAA;AACnB,WAAA;AACAqc,UAAAA,aAAa,GAAG,KAAK,CAAA;SACtB,CAAC,CACDM,KAAK,CAACC,KAAK,IACVC,UAAU,CAAC,MAAM;AACf,UAAA,MAAMD,KAAK,CAAA;AACb,SAAC,CACH,CAAC,CAAA;AACL,OAAA;KACD;IACDE,KAAK,EAAEA,MAAM;AACXpxB,MAAAA,KAAK,CAAC7D,QAAQ,CAAC6D,KAAK,CAAC+O,YAAY,CAAC,CAAA;KACnC;IACDsiB,UAAU,EAAEx1B,OAAO,IAAI;MACrB,MAAMy1B,UAAU,GAAG11B,gBAAgB,CAACC,OAAO,EAAEmE,KAAK,CAACO,OAAO,CAAC,CAAA;AAC3DP,MAAAA,KAAK,CAACO,OAAO,GAAGiwB,YAAY,CAACc,UAAU,CAGtC,CAAA;KACF;IAED3tB,QAAQ,EAAEA,MAAM;AACd,MAAA,OAAO3D,KAAK,CAACO,OAAO,CAAC4L,KAAK,CAAA;KAC3B;IAEDhQ,QAAQ,EAAGN,OAA4B,IAAK;AAC1CmE,MAAAA,KAAK,CAACO,OAAO,CAACgxB,aAAa,IAA3BvxB,IAAAA,IAAAA,KAAK,CAACO,OAAO,CAACgxB,aAAa,CAAG11B,OAAO,CAAC,CAAA;KACvC;AAED21B,IAAAA,SAAS,EAAEA,CAACvxB,GAAU,EAAE1B,KAAa,EAAE0C,MAAmB,KAAA;AAAA,MAAA,IAAA+gB,qBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,qBAAA,GACxDhiB,KAAK,CAACO,OAAO,CAACkxB,QAAQ,IAAtBzxB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAACO,OAAO,CAACkxB,QAAQ,CAAGxxB,GAAG,EAAE1B,KAAK,EAAE0C,MAAM,CAAC,KAAA+gB,IAAAA,GAAAA,qBAAA,GAC3C,CAAE/gB,EAAAA,MAAM,GAAG,CAACA,MAAM,CAACR,EAAE,EAAElC,KAAK,CAAC,CAAC0I,IAAI,CAAC,GAAG,CAAC,GAAG1I,KAAM,CAAC,CAAA,CAAA;AAAA,KAAA;IAEpDoO,eAAe,EAAEA,MAAM;AACrB,MAAA,IAAI,CAAC3M,KAAK,CAAC0xB,gBAAgB,EAAE;QAC3B1xB,KAAK,CAAC0xB,gBAAgB,GAAG1xB,KAAK,CAACO,OAAO,CAACoM,eAAe,CAAC3M,KAAK,CAAC,CAAA;AAC/D,OAAA;AAEA,MAAA,OAAOA,KAAK,CAAC0xB,gBAAgB,EAAG,CAAA;KACjC;AAED;AACA;;IAEA3Q,WAAW,EAAEA,MAAM;AACjB,MAAA,OAAO/gB,KAAK,CAAC8kB,qBAAqB,EAAE,CAAA;KACrC;AACD;AACApc,IAAAA,MAAM,EAAEA,CAACjI,EAAU,EAAEkxB,SAAmB,KAAK;MAC3C,IAAI1xB,GAAG,GAAG,CACR0xB,SAAS,GAAG3xB,KAAK,CAAC0gB,wBAAwB,EAAE,GAAG1gB,KAAK,CAAC+gB,WAAW,EAAE,EAClEI,QAAQ,CAAC1gB,EAAE,CAAC,CAAA;MAEd,IAAI,CAACR,GAAG,EAAE;QACRA,GAAG,GAAGD,KAAK,CAAC2M,eAAe,EAAE,CAACwU,QAAQ,CAAC1gB,EAAE,CAAC,CAAA;QAC1C,IAAI,CAACR,GAAG,EAAE;AACR,UAAA,IAAIL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;AACzC,YAAA,MAAM,IAAIiC,KAAK,CAAE,CAAqCtB,mCAAAA,EAAAA,EAAG,EAAC,CAAC,CAAA;AAC7D,WAAA;UACA,MAAM,IAAIsB,KAAK,EAAE,CAAA;AACnB,SAAA;AACF,OAAA;AAEA,MAAA,OAAO9B,GAAG,CAAA;KACX;AACDoB,IAAAA,oBAAoB,EAAE7D,IAAI,CACxB,MAAM,CAACwC,KAAK,CAACO,OAAO,CAACa,aAAa,CAAC,EACnCA,aAAa,IAAI;AAAA,MAAA,IAAAwwB,cAAA,CAAA;MACfxwB,aAAa,GAAA,CAAAwwB,cAAA,GAAIxwB,aAAa,YAAAwwB,cAAA,GAAI,EAEjC,CAAA;MAED,OAAO;QACLnwB,MAAM,EAAEoP,KAAK,IAAI;UACf,MAAMvP,iBAAiB,GAAGuP,KAAK,CAACpP,MAAM,CAAClG,MAAM,CAC1CwF,SAAqC,CAAA;UAExC,IAAIO,iBAAiB,CAAC7F,WAAW,EAAE;YACjC,OAAO6F,iBAAiB,CAAC7F,WAAW,CAAA;AACtC,WAAA;UAEA,IAAI6F,iBAAiB,CAAC9F,UAAU,EAAE;YAChC,OAAO8F,iBAAiB,CAACb,EAAE,CAAA;AAC7B,WAAA;AAEA,UAAA,OAAO,IAAI,CAAA;SACZ;AACD;AACAJ,QAAAA,IAAI,EAAEwQ,KAAK,IAAA;UAAA,IAAAghB,qBAAA,EAAAC,kBAAA,CAAA;UAAA,OAAAD,CAAAA,qBAAA,IAAAC,kBAAA,GAAIjhB,KAAK,CAACnQ,WAAW,EAAO,KAAxBoxB,IAAAA,IAAAA,kBAAA,CAA0B3nB,QAAQ,IAAA,IAAA,GAAA,KAAA,CAAA,GAAlC2nB,kBAAA,CAA0B3nB,QAAQ,EAAI,KAAA,IAAA,GAAA0nB,qBAAA,GAAI,IAAI,CAAA;AAAA,SAAA;QAC7D,GAAG7xB,KAAK,CAACY,SAAS,CAACuI,MAAM,CAAC,CAAC6U,GAAG,EAAEnd,OAAO,KAAK;AAC1C,UAAA,OAAOoR,MAAM,CAACse,MAAM,CAACvS,GAAG,EAAEnd,OAAO,CAACmL,mBAAmB,oBAA3BnL,OAAO,CAACmL,mBAAmB,EAAI,CAAC,CAAA;SAC3D,EAAE,EAAE,CAAC;QACN,GAAG5K,aAAAA;OACJ,CAAA;KACF,EACD7B,cAAc,CAACgB,OAAO,EAAE,cAAc,EAAE,sBAAsB,CAChE,CAAC;AAEDwxB,IAAAA,cAAc,EAAEA,MAAM/xB,KAAK,CAACO,OAAO,CAACyB,OAAO;AAE3CyB,IAAAA,aAAa,EAAEjG,IAAI,CACjB,MAAM,CAACwC,KAAK,CAAC+xB,cAAc,EAAE,CAAC,EAC9BC,UAAU,IAAI;MACZ,MAAMC,cAAc,GAAG,UACrBD,UAAuC,EACvC/wB,MAA+B,EAC/BD,KAAK,EACwB;AAAA,QAAA,IAD7BA,KAAK,KAAA,KAAA,CAAA,EAAA;AAALA,UAAAA,KAAK,GAAG,CAAC,CAAA;AAAA,SAAA;AAET,QAAA,OAAOgxB,UAAU,CAAC3uB,GAAG,CAACtC,SAAS,IAAI;UACjC,MAAMxF,MAAM,GAAGuF,YAAY,CAACd,KAAK,EAAEe,SAAS,EAAEC,KAAK,EAAEC,MAAM,CAAC,CAAA;UAE5D,MAAMixB,iBAAiB,GAAGnxB,SAGzB,CAAA;UAEDxF,MAAM,CAACyG,OAAO,GAAGkwB,iBAAiB,CAAClwB,OAAO,GACtCiwB,cAAc,CAACC,iBAAiB,CAAClwB,OAAO,EAAEzG,MAAM,EAAEyF,KAAK,GAAG,CAAC,CAAC,GAC5D,EAAE,CAAA;AAEN,UAAA,OAAOzF,MAAM,CAAA;AACf,SAAC,CAAC,CAAA;OACH,CAAA;MAED,OAAO02B,cAAc,CAACD,UAAU,CAAC,CAAA;KAClC,EACDzyB,cAAc,CAACgB,OAAO,EAAE,cAAc,EAAE,eAAe,CACzD,CAAC;AAEDid,IAAAA,iBAAiB,EAAEhgB,IAAI,CACrB,MAAM,CAACwC,KAAK,CAACyD,aAAa,EAAE,CAAC,EAC7BM,UAAU,IAAI;AACZ,MAAA,OAAOA,UAAU,CAAC5B,OAAO,CAAC5G,MAAM,IAAI;AAClC,QAAA,OAAOA,MAAM,CAAC0G,cAAc,EAAE,CAAA;AAChC,OAAC,CAAC,CAAA;KACH,EACD1C,cAAc,CAACgB,OAAO,EAAE,cAAc,EAAE,mBAAmB,CAC7D,CAAC;AAED4xB,IAAAA,sBAAsB,EAAE30B,IAAI,CAC1B,MAAM,CAACwC,KAAK,CAACwd,iBAAiB,EAAE,CAAC,EACjC4U,WAAW,IAAI;MACb,OAAOA,WAAW,CAACjpB,MAAM,CACvB,CAACC,GAAG,EAAE7N,MAAM,KAAK;AACf6N,QAAAA,GAAG,CAAC7N,MAAM,CAACkF,EAAE,CAAC,GAAGlF,MAAM,CAAA;AACvB,QAAA,OAAO6N,GAAG,CAAA;OACX,EACD,EACF,CAAC,CAAA;KACF,EACD7J,cAAc,CAACgB,OAAO,EAAE,cAAc,EAAE,uBAAuB,CACjE,CAAC;IAEDyI,iBAAiB,EAAExL,IAAI,CACrB,MAAM,CAACwC,KAAK,CAACyD,aAAa,EAAE,EAAEzD,KAAK,CAACqC,kBAAkB,EAAE,CAAC,EACzD,CAAC0B,UAAU,EAAEzB,YAAY,KAAK;AAC5B,MAAA,IAAIE,WAAW,GAAGuB,UAAU,CAAC5B,OAAO,CAAC5G,MAAM,IAAIA,MAAM,CAAC6G,cAAc,EAAE,CAAC,CAAA;MACvE,OAAOE,YAAY,CAACE,WAAW,CAAC,CAAA;KACjC,EACDjD,cAAc,CAACgB,OAAO,EAAE,cAAc,EAAE,mBAAmB,CAC7D,CAAC;IAED8H,SAAS,EAAEnI,QAAQ,IAAI;MACrB,MAAM3E,MAAM,GAAGyE,KAAK,CAACmyB,sBAAsB,EAAE,CAACjyB,QAAQ,CAAC,CAAA;MAEvD,IAAIN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACvE,MAAM,EAAE;AACpD4D,QAAAA,OAAO,CAAC+xB,KAAK,CAAE,CAA0BhxB,wBAAAA,EAAAA,QAAS,mBAAkB,CAAC,CAAA;AACvE,OAAA;AAEA,MAAA,OAAO3E,MAAM,CAAA;AACf,KAAA;GACD,CAAA;AAED0W,EAAAA,MAAM,CAACse,MAAM,CAACvwB,KAAK,EAAE4wB,YAAY,CAAC,CAAA;AAElC,EAAA,KAAK,IAAIryB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGyB,KAAK,CAACY,SAAS,CAACrD,MAAM,EAAEgB,KAAK,EAAE,EAAE;AAC3D,IAAA,MAAMsC,OAAO,GAAGb,KAAK,CAACY,SAAS,CAACrC,KAAK,CAAC,CAAA;IACtCsC,OAAO,IAAA,IAAA,IAAPA,OAAO,CAAE0C,WAAW,IAAA,IAAA,IAApB1C,OAAO,CAAE0C,WAAW,CAAGvD,KAAK,CAAC,CAAA;AAC/B,GAAA;AAEA,EAAA,OAAOA,KAAK,CAAA;AACd;;AC1gBO,SAAS2M,eAAeA,GAEJ;AACzB,EAAA,OAAO3M,KAAK,IACVxC,IAAI,CACF,MAAM,CAACwC,KAAK,CAACO,OAAO,CAAC8xB,IAAI,CAAC,EAExBA,IAAI,IAKD;AACH,IAAA,MAAMrJ,QAAyB,GAAG;AAChC3D,MAAAA,IAAI,EAAE,EAAE;AACRzY,MAAAA,QAAQ,EAAE,EAAE;AACZuU,MAAAA,QAAQ,EAAE,EAAC;KACZ,CAAA;IAED,MAAMmR,UAAU,GAAG,UACjBC,YAAqB,EACrBvxB,KAAK,EACL8H,SAAsB,EACL;AAAA,MAAA,IAFjB9H,KAAK,KAAA,KAAA,CAAA,EAAA;AAALA,QAAAA,KAAK,GAAG,CAAC,CAAA;AAAA,OAAA;MAGT,MAAMqkB,IAAI,GAAG,EAAkB,CAAA;AAE/B,MAAA,KAAK,IAAIhc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkpB,YAAY,CAACh1B,MAAM,EAAE8L,CAAC,EAAE,EAAE;AAC5C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,QAAA,MAAMpJ,GAAG,GAAG4H,SAAS,CACnB7H,KAAK,EACLA,KAAK,CAACwxB,SAAS,CAACe,YAAY,CAAClpB,CAAC,CAAC,EAAGA,CAAC,EAAEP,SAAS,CAAC,EAC/CypB,YAAY,CAAClpB,CAAC,CAAC,EACfA,CAAC,EACDrI,KAAK,EACLQ,SAAS,EACTsH,SAAS,IAAA,IAAA,GAAA,KAAA,CAAA,GAATA,SAAS,CAAErI,EACb,CAAC,CAAA;;AAED;AACAuoB,QAAAA,QAAQ,CAACpc,QAAQ,CAACvP,IAAI,CAAC4C,GAAG,CAAC,CAAA;AAC3B;QACA+oB,QAAQ,CAAC7H,QAAQ,CAAClhB,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;AAC/B;AACAolB,QAAAA,IAAI,CAAChoB,IAAI,CAAC4C,GAAG,CAAC,CAAA;;AAEd;AACA,QAAA,IAAID,KAAK,CAACO,OAAO,CAACiyB,UAAU,EAAE;AAAA,UAAA,IAAAC,oBAAA,CAAA;AAC5BxyB,UAAAA,GAAG,CAACyyB,eAAe,GAAG1yB,KAAK,CAACO,OAAO,CAACiyB,UAAU,CAC5CD,YAAY,CAAClpB,CAAC,CAAC,EACfA,CACF,CAAC,CAAA;;AAED;UACA,IAAAopB,CAAAA,oBAAA,GAAIxyB,GAAG,CAACyyB,eAAe,KAAnBD,IAAAA,IAAAA,oBAAA,CAAqBl1B,MAAM,EAAE;AAC/B0C,YAAAA,GAAG,CAAC+H,OAAO,GAAGsqB,UAAU,CAACryB,GAAG,CAACyyB,eAAe,EAAE1xB,KAAK,GAAG,CAAC,EAAEf,GAAG,CAAC,CAAA;AAC/D,WAAA;AACF,SAAA;AACF,OAAA;AAEA,MAAA,OAAOolB,IAAI,CAAA;KACZ,CAAA;AAED2D,IAAAA,QAAQ,CAAC3D,IAAI,GAAGiN,UAAU,CAACD,IAAI,CAAC,CAAA;AAEhC,IAAA,OAAOrJ,QAAQ,CAAA;AACjB,GAAC,EACDzpB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MACzDP,KAAK,CAAC+iB,mBAAmB,EAC3B,CACF,CAAC,CAAA;AACL;;AC9EO,SAASxB,mBAAmBA,GAER;AACzB,EAAA,OAAOvhB,KAAK,IACVxC,IAAI,CACF,MAAM,CACJwC,KAAK,CAAC2D,QAAQ,EAAE,CAAC8b,QAAQ,EACzBzf,KAAK,CAACqhB,sBAAsB,EAAE,EAC9BrhB,KAAK,CAACO,OAAO,CAACof,oBAAoB,CACnC,EACD,CAACF,QAAQ,EAAEuJ,QAAQ,EAAErJ,oBAAoB,KAAK;IAC5C,IACE,CAACqJ,QAAQ,CAAC3D,IAAI,CAAC9nB,MAAM,IACpBkiB,QAAQ,KAAK,IAAI,IAAI,CAACxN,MAAM,CAAC6O,IAAI,CAACrB,QAAQ,IAARA,IAAAA,GAAAA,QAAQ,GAAI,EAAE,CAAC,CAACliB,MAAO,EAC1D;AACA,MAAA,OAAOyrB,QAAQ,CAAA;AACjB,KAAA;IAEA,IAAI,CAACrJ,oBAAoB,EAAE;AACzB;AACA,MAAA,OAAOqJ,QAAQ,CAAA;AACjB,KAAA;IAEA,OAAO2J,UAAU,CAAC3J,QAAQ,CAAC,CAAA;GAC5B,EACDzpB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,qBAAqB,CACnE,CAAC,CAAA;AACL,CAAA;AAEO,SAASoyB,UAAUA,CAAwB3J,QAAyB,EAAE;EAC3E,MAAM4J,YAA0B,GAAG,EAAE,CAAA;EAErC,MAAMC,SAAS,GAAI5yB,GAAe,IAAK;AAAA,IAAA,IAAAgT,YAAA,CAAA;AACrC2f,IAAAA,YAAY,CAACv1B,IAAI,CAAC4C,GAAG,CAAC,CAAA;AAEtB,IAAA,IAAI,CAAAgT,YAAA,GAAAhT,GAAG,CAAC+H,OAAO,KAAXiL,IAAAA,IAAAA,YAAA,CAAa1V,MAAM,IAAI0C,GAAG,CAAC+gB,aAAa,EAAE,EAAE;AAC9C/gB,MAAAA,GAAG,CAAC+H,OAAO,CAAC7K,OAAO,CAAC01B,SAAS,CAAC,CAAA;AAChC,KAAA;GACD,CAAA;AAED7J,EAAAA,QAAQ,CAAC3D,IAAI,CAACloB,OAAO,CAAC01B,SAAS,CAAC,CAAA;EAEhC,OAAO;AACLxN,IAAAA,IAAI,EAAEuN,YAAY;IAClBhmB,QAAQ,EAAEoc,QAAQ,CAACpc,QAAQ;IAC3BuU,QAAQ,EAAE6H,QAAQ,CAAC7H,QAAAA;GACpB,CAAA;AACH;;AC/CO,SAASrX,sBAAsBA,GAGE;AACtC,EAAA,OAAO,CAAC9J,KAAK,EAAEE,QAAQ,KACrB1C,IAAI,CACF,MAAA;AAAA,IAAA,IAAAs1B,gBAAA,CAAA;AAAA,IAAA,OAAM,CAAAA,CAAAA,gBAAA,GAAC9yB,KAAK,CAACqI,SAAS,CAACnI,QAAQ,CAAC,qBAAzB4yB,gBAAA,CAA2BtpB,kBAAkB,EAAE,CAAC,CAAA;AAAA,GAAA,EACvDupB,eAAe,IAAI;AAAA,IAAA,IAAAC,qBAAA,CAAA;AACjB,IAAA,IAAI,CAACD,eAAe,EAAE,OAAOvxB,SAAS,CAAA;AAEtC,IAAA,MAAMyxB,UAAU,GAAAD,CAAAA,qBAAA,GACdD,eAAe,CAACnmB,QAAQ,CAAC,CAAC,CAAC,qBAA3BomB,qBAAA,CAA6B1qB,eAAe,CAACpI,QAAQ,CAAC,CAAA;AAExD,IAAA,IAAI,OAAO+yB,UAAU,KAAK,WAAW,EAAE;AACrC,MAAA,OAAOzxB,SAAS,CAAA;AAClB,KAAA;AAEA,IAAA,IAAI0xB,mBAA+B,GAAG,CAACD,UAAU,EAAEA,UAAU,CAAC,CAAA;AAE9D,IAAA,KAAK,IAAI5pB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0pB,eAAe,CAACnmB,QAAQ,CAACrP,MAAM,EAAE8L,CAAC,EAAE,EAAE;AACxD,MAAA,MAAMwG,MAAM,GACVkjB,eAAe,CAACnmB,QAAQ,CAACvD,CAAC,CAAC,CAAEf,eAAe,CAASpI,QAAQ,CAAC,CAAA;AAEhE,MAAA,KAAK,IAAIizB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtjB,MAAM,CAACtS,MAAM,EAAE41B,CAAC,EAAE,EAAE;AACtC,QAAA,MAAMtmB,KAAK,GAAGgD,MAAM,CAACsjB,CAAC,CAAE,CAAA;AAExB,QAAA,IAAItmB,KAAK,GAAGqmB,mBAAmB,CAAC,CAAC,CAAC,EAAE;AAClCA,UAAAA,mBAAmB,CAAC,CAAC,CAAC,GAAGrmB,KAAK,CAAA;SAC/B,MAAM,IAAIA,KAAK,GAAGqmB,mBAAmB,CAAC,CAAC,CAAC,EAAE;AACzCA,UAAAA,mBAAmB,CAAC,CAAC,CAAC,GAAGrmB,KAAK,CAAA;AAChC,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,OAAOqmB,mBAAmB,CAAA;GAC3B,EACD3zB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,wBAAwB,CACtE,CAAC,CAAA;AACL;;ACtCO,SAAS6yB,UAAUA,CACxB/N,IAAkB,EAClBgO,aAAuC,EACvCrzB,KAAmB,EACnB;AACA,EAAA,IAAIA,KAAK,CAACO,OAAO,CAACgM,kBAAkB,EAAE;AACpC,IAAA,OAAO+mB,uBAAuB,CAACjO,IAAI,EAAEgO,aAAa,EAAErzB,KAAK,CAAC,CAAA;AAC5D,GAAA;AAEA,EAAA,OAAOuzB,sBAAsB,CAAClO,IAAI,EAAEgO,aAAa,EAAErzB,KAAK,CAAC,CAAA;AAC3D,CAAA;AAEA,SAASszB,uBAAuBA,CAC9BE,YAA0B,EAC1BC,SAA4C,EAC5CzzB,KAAmB,EACF;AAAA,EAAA,IAAA0zB,qBAAA,CAAA;EACjB,MAAMC,mBAAiC,GAAG,EAAE,CAAA;EAC5C,MAAMC,mBAA+C,GAAG,EAAE,CAAA;AAC1D,EAAA,MAAMhtB,QAAQ,GAAA,CAAA8sB,qBAAA,GAAG1zB,KAAK,CAACO,OAAO,CAACiM,qBAAqB,KAAA,IAAA,GAAAknB,qBAAA,GAAI,GAAG,CAAA;AAE3D,EAAA,MAAMG,iBAAiB,GAAG,UAACL,YAA0B,EAAExyB,KAAK,EAAS;AAAA,IAAA,IAAdA,KAAK,KAAA,KAAA,CAAA,EAAA;AAALA,MAAAA,KAAK,GAAG,CAAC,CAAA;AAAA,KAAA;IAC9D,MAAMqkB,IAAkB,GAAG,EAAE,CAAA;;AAE7B;AACA,IAAA,KAAK,IAAIhc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmqB,YAAY,CAACj2B,MAAM,EAAE8L,CAAC,EAAE,EAAE;AAAA,MAAA,IAAA4J,YAAA,CAAA;AAC5C,MAAA,IAAIhT,GAAG,GAAGuzB,YAAY,CAACnqB,CAAC,CAAE,CAAA;MAE1B,MAAMyqB,MAAM,GAAGjsB,SAAS,CACtB7H,KAAK,EACLC,GAAG,CAACQ,EAAE,EACNR,GAAG,CAAC6H,QAAQ,EACZ7H,GAAG,CAAC1B,KAAK,EACT0B,GAAG,CAACe,KAAK,EACTQ,SAAS,EACTvB,GAAG,CAACgI,QACN,CAAC,CAAA;AACD6rB,MAAAA,MAAM,CAAC1nB,aAAa,GAAGnM,GAAG,CAACmM,aAAa,CAAA;AAExC,MAAA,IAAI,CAAA6G,YAAA,GAAAhT,GAAG,CAAC+H,OAAO,KAAA,IAAA,IAAXiL,YAAA,CAAa1V,MAAM,IAAIyD,KAAK,GAAG4F,QAAQ,EAAE;AAC3CktB,QAAAA,MAAM,CAAC9rB,OAAO,GAAG6rB,iBAAiB,CAAC5zB,GAAG,CAAC+H,OAAO,EAAEhH,KAAK,GAAG,CAAC,CAAC,CAAA;AAC1Df,QAAAA,GAAG,GAAG6zB,MAAM,CAAA;QAEZ,IAAIL,SAAS,CAACxzB,GAAG,CAAC,IAAI,CAAC6zB,MAAM,CAAC9rB,OAAO,CAACzK,MAAM,EAAE;AAC5C8nB,UAAAA,IAAI,CAAChoB,IAAI,CAAC4C,GAAG,CAAC,CAAA;AACd2zB,UAAAA,mBAAmB,CAAC3zB,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;AACjC0zB,UAAAA,mBAAmB,CAACt2B,IAAI,CAAC4C,GAAG,CAAC,CAAA;AAC7B,UAAA,SAAA;AACF,SAAA;QAEA,IAAIwzB,SAAS,CAACxzB,GAAG,CAAC,IAAI6zB,MAAM,CAAC9rB,OAAO,CAACzK,MAAM,EAAE;AAC3C8nB,UAAAA,IAAI,CAAChoB,IAAI,CAAC4C,GAAG,CAAC,CAAA;AACd2zB,UAAAA,mBAAmB,CAAC3zB,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;AACjC0zB,UAAAA,mBAAmB,CAACt2B,IAAI,CAAC4C,GAAG,CAAC,CAAA;AAC7B,UAAA,SAAA;AACF,SAAA;AACF,OAAC,MAAM;AACLA,QAAAA,GAAG,GAAG6zB,MAAM,CAAA;AACZ,QAAA,IAAIL,SAAS,CAACxzB,GAAG,CAAC,EAAE;AAClBolB,UAAAA,IAAI,CAAChoB,IAAI,CAAC4C,GAAG,CAAC,CAAA;AACd2zB,UAAAA,mBAAmB,CAAC3zB,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;AACjC0zB,UAAAA,mBAAmB,CAACt2B,IAAI,CAAC4C,GAAG,CAAC,CAAA;AAC/B,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,OAAOolB,IAAI,CAAA;GACZ,CAAA;EAED,OAAO;AACLA,IAAAA,IAAI,EAAEwO,iBAAiB,CAACL,YAAY,CAAC;AACrC5mB,IAAAA,QAAQ,EAAE+mB,mBAAmB;AAC7BxS,IAAAA,QAAQ,EAAEyS,mBAAAA;GACX,CAAA;AACH,CAAA;AAEA,SAASL,sBAAsBA,CAC7BC,YAA0B,EAC1BC,SAAmC,EACnCzzB,KAAmB,EACF;AAAA,EAAA,IAAA+zB,sBAAA,CAAA;EACjB,MAAMJ,mBAAiC,GAAG,EAAE,CAAA;EAC5C,MAAMC,mBAA+C,GAAG,EAAE,CAAA;AAC1D,EAAA,MAAMhtB,QAAQ,GAAA,CAAAmtB,sBAAA,GAAG/zB,KAAK,CAACO,OAAO,CAACiM,qBAAqB,KAAA,IAAA,GAAAunB,sBAAA,GAAI,GAAG,CAAA;;AAE3D;AACA,EAAA,MAAMF,iBAAiB,GAAG,UAACL,YAA0B,EAAExyB,KAAK,EAAS;AAAA,IAAA,IAAdA,KAAK,KAAA,KAAA,CAAA,EAAA;AAALA,MAAAA,KAAK,GAAG,CAAC,CAAA;AAAA,KAAA;AAC9D;;IAEA,MAAMqkB,IAAkB,GAAG,EAAE,CAAA;;AAE7B;AACA,IAAA,KAAK,IAAIhc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmqB,YAAY,CAACj2B,MAAM,EAAE8L,CAAC,EAAE,EAAE;AAC5C,MAAA,IAAIpJ,GAAG,GAAGuzB,YAAY,CAACnqB,CAAC,CAAE,CAAA;AAE1B,MAAA,MAAM2qB,IAAI,GAAGP,SAAS,CAACxzB,GAAG,CAAC,CAAA;AAE3B,MAAA,IAAI+zB,IAAI,EAAE;AAAA,QAAA,IAAAhJ,aAAA,CAAA;AACR,QAAA,IAAI,CAAAA,aAAA,GAAA/qB,GAAG,CAAC+H,OAAO,KAAA,IAAA,IAAXgjB,aAAA,CAAaztB,MAAM,IAAIyD,KAAK,GAAG4F,QAAQ,EAAE;UAC3C,MAAMktB,MAAM,GAAGjsB,SAAS,CACtB7H,KAAK,EACLC,GAAG,CAACQ,EAAE,EACNR,GAAG,CAAC6H,QAAQ,EACZ7H,GAAG,CAAC1B,KAAK,EACT0B,GAAG,CAACe,KAAK,EACTQ,SAAS,EACTvB,GAAG,CAACgI,QACN,CAAC,CAAA;AACD6rB,UAAAA,MAAM,CAAC9rB,OAAO,GAAG6rB,iBAAiB,CAAC5zB,GAAG,CAAC+H,OAAO,EAAEhH,KAAK,GAAG,CAAC,CAAC,CAAA;AAC1Df,UAAAA,GAAG,GAAG6zB,MAAM,CAAA;AACd,SAAA;AAEAzO,QAAAA,IAAI,CAAChoB,IAAI,CAAC4C,GAAG,CAAC,CAAA;AACd0zB,QAAAA,mBAAmB,CAACt2B,IAAI,CAAC4C,GAAG,CAAC,CAAA;AAC7B2zB,QAAAA,mBAAmB,CAAC3zB,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;AACnC,OAAA;AACF,KAAA;AAEA,IAAA,OAAOolB,IAAI,CAAA;GACZ,CAAA;EAED,OAAO;AACLA,IAAAA,IAAI,EAAEwO,iBAAiB,CAACL,YAAY,CAAC;AACrC5mB,IAAAA,QAAQ,EAAE+mB,mBAAmB;AAC7BxS,IAAAA,QAAQ,EAAEyS,mBAAAA;GACX,CAAA;AACH;;AC7HO,SAASpqB,kBAAkBA,GAGP;EACzB,OAAO,CAACxJ,KAAK,EAAEE,QAAQ,KACrB1C,IAAI,CACF,MAAM,CACJwC,KAAK,CAACyJ,sBAAsB,EAAE,EAC9BzJ,KAAK,CAAC2D,QAAQ,EAAE,CAACyI,aAAa,EAC9BpM,KAAK,CAAC2D,QAAQ,EAAE,CAACib,YAAY,EAC7B5e,KAAK,CAACgP,mBAAmB,EAAE,CAC5B,EACD,CAACilB,WAAW,EAAE7nB,aAAa,EAAEwS,YAAY,KAAK;AAC5C,IAAA,IACE,CAACqV,WAAW,CAAC5O,IAAI,CAAC9nB,MAAM,IACvB,EAAC6O,aAAa,IAAA,IAAA,IAAbA,aAAa,CAAE7O,MAAM,CAAI,IAAA,CAACqhB,YAAa,EACzC;AACA,MAAA,OAAOqV,WAAW,CAAA;AACpB,KAAA;AAEA,IAAA,MAAMC,aAAa,GAAG,CACpB,GAAG9nB,aAAa,CAAC/I,GAAG,CAAC/G,CAAC,IAAIA,CAAC,CAACmE,EAAE,CAAC,CAAC2D,MAAM,CAAC9H,CAAC,IAAIA,CAAC,KAAK4D,QAAQ,CAAC,EAC3D0e,YAAY,GAAG,YAAY,GAAGpd,SAAS,CACxC,CAAC4C,MAAM,CAACC,OAAO,CAAa,CAAA;IAE7B,MAAM8vB,cAAc,GAAIl0B,GAAe,IAAK;AAC1C;AACA,MAAA,KAAK,IAAIoJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6qB,aAAa,CAAC32B,MAAM,EAAE8L,CAAC,EAAE,EAAE;QAC7C,IAAIpJ,GAAG,CAACmM,aAAa,CAAC8nB,aAAa,CAAC7qB,CAAC,CAAC,CAAE,KAAK,KAAK,EAAE;AAClD,UAAA,OAAO,KAAK,CAAA;AACd,SAAA;AACF,OAAA;AACA,MAAA,OAAO,IAAI,CAAA;KACZ,CAAA;IAED,OAAO+pB,UAAU,CAACa,WAAW,CAAC5O,IAAI,EAAE8O,cAAc,EAAEn0B,KAAK,CAAC,CAAA;GAC3D,EACDT,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,oBAAoB,CAClE,CAAC,CAAA;AACL;;ACxCO,SAASoJ,sBAAsBA,GAGV;AAC1B,EAAA,OAAO,CAAC3J,KAAK,EAAEE,QAAQ,KACrB1C,IAAI,CACF,MAAA;AAAA,IAAA,IAAAs1B,gBAAA,CAAA;AAAA,IAAA,OAAM,CAAAA,CAAAA,gBAAA,GAAC9yB,KAAK,CAACqI,SAAS,CAACnI,QAAQ,CAAC,qBAAzB4yB,gBAAA,CAA2BtpB,kBAAkB,EAAE,CAAC,CAAA;AAAA,GAAA,EACvDupB,eAAe,IAAI;AACjB,IAAA,IAAI,CAACA,eAAe,EAAE,OAAO,IAAInpB,GAAG,EAAE,CAAA;AAEtC,IAAA,IAAIwqB,mBAAmB,GAAG,IAAIxqB,GAAG,EAAe,CAAA;AAEhD,IAAA,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0pB,eAAe,CAACnmB,QAAQ,CAACrP,MAAM,EAAE8L,CAAC,EAAE,EAAE;AACxD,MAAA,MAAMwG,MAAM,GACVkjB,eAAe,CAACnmB,QAAQ,CAACvD,CAAC,CAAC,CAAEf,eAAe,CAASpI,QAAQ,CAAC,CAAA;AAEhE,MAAA,KAAK,IAAIizB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtjB,MAAM,CAACtS,MAAM,EAAE41B,CAAC,EAAE,EAAE;AACtC,QAAA,MAAMtmB,KAAK,GAAGgD,MAAM,CAACsjB,CAAC,CAAE,CAAA;AAExB,QAAA,IAAIiB,mBAAmB,CAAC/N,GAAG,CAACxZ,KAAK,CAAC,EAAE;AAAA,UAAA,IAAAwnB,qBAAA,CAAA;UAClCD,mBAAmB,CAACE,GAAG,CACrBznB,KAAK,EACL,CAAAwnB,CAAAA,qBAAA,GAACD,mBAAmB,CAACG,GAAG,CAAC1nB,KAAK,CAAC,KAAAwnB,IAAAA,GAAAA,qBAAA,GAAI,CAAC,IAAI,CAC1C,CAAC,CAAA;AACH,SAAC,MAAM;AACLD,UAAAA,mBAAmB,CAACE,GAAG,CAACznB,KAAK,EAAE,CAAC,CAAC,CAAA;AACnC,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,OAAOunB,mBAAmB,CAAA;AAC5B,GAAC,EACD70B,cAAc,CACZS,KAAK,CAACO,OAAO,EACb,YAAY,EACX,CAAA,uBAAA,EAAyBL,QAAS,CAAA,CACrC,CACF,CAAC,CAAA;AACL;;ACpCO,SAAS8O,mBAAmBA,GAER;AACzB,EAAA,OAAOhP,KAAK,IACVxC,IAAI,CACF,MAAM,CACJwC,KAAK,CAACyJ,sBAAsB,EAAE,EAC9BzJ,KAAK,CAAC2D,QAAQ,EAAE,CAACyI,aAAa,EAC9BpM,KAAK,CAAC2D,QAAQ,EAAE,CAACib,YAAY,CAC9B,EACD,CAACoK,QAAQ,EAAE5c,aAAa,EAAEwS,YAAY,KAAK;AACzC,IAAA,IACE,CAACoK,QAAQ,CAAC3D,IAAI,CAAC9nB,MAAM,IACpB,EAAC6O,aAAa,IAAA,IAAA,IAAbA,aAAa,CAAE7O,MAAM,CAAI,IAAA,CAACqhB,YAAa,EACzC;AACA,MAAA,KAAK,IAAIvV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2f,QAAQ,CAACpc,QAAQ,CAACrP,MAAM,EAAE8L,CAAC,EAAE,EAAE;QACjD2f,QAAQ,CAACpc,QAAQ,CAACvD,CAAC,CAAC,CAAE+C,aAAa,GAAG,EAAE,CAAA;QACxC4c,QAAQ,CAACpc,QAAQ,CAACvD,CAAC,CAAC,CAAEmF,iBAAiB,GAAG,EAAE,CAAA;AAC9C,OAAA;AACA,MAAA,OAAOwa,QAAQ,CAAA;AACjB,KAAA;IAEA,MAAMwL,qBAAoD,GAAG,EAAE,CAAA;IAC/D,MAAMC,qBAAoD,GAAG,EAAE,CAAA;IAE9D,CAACroB,aAAa,WAAbA,aAAa,GAAI,EAAE,EAAEjP,OAAO,CAACb,CAAC,IAAI;AAAA,MAAA,IAAAo4B,qBAAA,CAAA;MAClC,MAAMn5B,MAAM,GAAGyE,KAAK,CAACqI,SAAS,CAAC/L,CAAC,CAACmE,EAAE,CAAC,CAAA;MAEpC,IAAI,CAAClF,MAAM,EAAE;AACX,QAAA,OAAA;AACF,OAAA;AAEA,MAAA,MAAM0Q,QAAQ,GAAG1Q,MAAM,CAACuR,WAAW,EAAE,CAAA;MAErC,IAAI,CAACb,QAAQ,EAAE;AACb,QAAA,IAAIrM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCX,OAAO,CAAC2C,IAAI,CACT,CAAA,iEAAA,EAAmEvG,MAAM,CAACkF,EAAG,GAChF,CAAC,CAAA;AACH,SAAA;AACA,QAAA,OAAA;AACF,OAAA;MAEA+zB,qBAAqB,CAACn3B,IAAI,CAAC;QACzBoD,EAAE,EAAEnE,CAAC,CAACmE,EAAE;QACRwL,QAAQ;AACR0c,QAAAA,aAAa,GAAA+L,qBAAA,GAAEzoB,QAAQ,CAACb,kBAAkB,oBAA3Ba,QAAQ,CAACb,kBAAkB,CAAG9O,CAAC,CAACuQ,KAAK,CAAC,YAAA6nB,qBAAA,GAAIp4B,CAAC,CAACuQ,KAAAA;AAC7D,OAAC,CAAC,CAAA;AACJ,KAAC,CAAC,CAAA;IAEF,MAAMqnB,aAAa,GAAG9nB,aAAa,CAAC/I,GAAG,CAAC/G,CAAC,IAAIA,CAAC,CAACmE,EAAE,CAAC,CAAA;AAElD,IAAA,MAAMqe,cAAc,GAAG9e,KAAK,CAACqf,iBAAiB,EAAE,CAAA;AAEhD,IAAA,MAAMsV,yBAAyB,GAAG30B,KAAK,CACpCgJ,iBAAiB,EAAE,CACnB5E,MAAM,CAAC7I,MAAM,IAAIA,MAAM,CAAC0jB,kBAAkB,EAAE,CAAC,CAAA;AAEhD,IAAA,IACEL,YAAY,IACZE,cAAc,IACd6V,yBAAyB,CAACp3B,MAAM,EAChC;AACA22B,MAAAA,aAAa,CAAC72B,IAAI,CAAC,YAAY,CAAC,CAAA;AAEhCs3B,MAAAA,yBAAyB,CAACx3B,OAAO,CAAC5B,MAAM,IAAI;AAAA,QAAA,IAAAq5B,qBAAA,CAAA;QAC1CH,qBAAqB,CAACp3B,IAAI,CAAC;UACzBoD,EAAE,EAAElF,MAAM,CAACkF,EAAE;AACbwL,UAAAA,QAAQ,EAAE6S,cAAc;AACxB6J,UAAAA,aAAa,EAAAiM,CAAAA,qBAAA,GACX9V,cAAc,CAAC1T,kBAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAjC0T,cAAc,CAAC1T,kBAAkB,CAAGwT,YAAY,CAAC,KAAA,IAAA,GAAAgW,qBAAA,GACjDhW,YAAAA;AACJ,SAAC,CAAC,CAAA;AACJ,OAAC,CAAC,CAAA;AACJ,KAAA;AAEA,IAAA,IAAIiW,mBAAmB,CAAA;AACvB,IAAA,IAAIC,mBAAmB,CAAA;;AAEvB;AACA,IAAA,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnK,QAAQ,CAACpc,QAAQ,CAACrP,MAAM,EAAE41B,CAAC,EAAE,EAAE;AACjD,MAAA,MAAMlzB,GAAG,GAAG+oB,QAAQ,CAACpc,QAAQ,CAACumB,CAAC,CAAE,CAAA;AAEjClzB,MAAAA,GAAG,CAACmM,aAAa,GAAG,EAAE,CAAA;MAEtB,IAAIooB,qBAAqB,CAACj3B,MAAM,EAAE;AAChC,QAAA,KAAK,IAAI8L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmrB,qBAAqB,CAACj3B,MAAM,EAAE8L,CAAC,EAAE,EAAE;AACrDwrB,UAAAA,mBAAmB,GAAGL,qBAAqB,CAACnrB,CAAC,CAAE,CAAA;AAC/C,UAAA,MAAM5I,EAAE,GAAGo0B,mBAAmB,CAACp0B,EAAE,CAAA;;AAEjC;AACAR,UAAAA,GAAG,CAACmM,aAAa,CAAC3L,EAAE,CAAC,GAAGo0B,mBAAmB,CAAC5oB,QAAQ,CAClDhM,GAAG,EACHQ,EAAE,EACFo0B,mBAAmB,CAAClM,aAAa,EACjCoM,UAAU,IAAI;AACZ90B,YAAAA,GAAG,CAACuO,iBAAiB,CAAC/N,EAAE,CAAC,GAAGs0B,UAAU,CAAA;AACxC,WACF,CAAC,CAAA;AACH,SAAA;AACF,OAAA;MAEA,IAAIN,qBAAqB,CAACl3B,MAAM,EAAE;AAChC,QAAA,KAAK,IAAI8L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGorB,qBAAqB,CAACl3B,MAAM,EAAE8L,CAAC,EAAE,EAAE;AACrDyrB,UAAAA,mBAAmB,GAAGL,qBAAqB,CAACprB,CAAC,CAAE,CAAA;AAC/C,UAAA,MAAM5I,EAAE,GAAGq0B,mBAAmB,CAACr0B,EAAE,CAAA;AACjC;AACA,UAAA,IACEq0B,mBAAmB,CAAC7oB,QAAQ,CAC1BhM,GAAG,EACHQ,EAAE,EACFq0B,mBAAmB,CAACnM,aAAa,EACjCoM,UAAU,IAAI;AACZ90B,YAAAA,GAAG,CAACuO,iBAAiB,CAAC/N,EAAE,CAAC,GAAGs0B,UAAU,CAAA;AACxC,WACF,CAAC,EACD;AACA90B,YAAAA,GAAG,CAACmM,aAAa,CAAC4oB,UAAU,GAAG,IAAI,CAAA;AACnC,YAAA,MAAA;AACF,WAAA;AACF,SAAA;AAEA,QAAA,IAAI/0B,GAAG,CAACmM,aAAa,CAAC4oB,UAAU,KAAK,IAAI,EAAE;AACzC/0B,UAAAA,GAAG,CAACmM,aAAa,CAAC4oB,UAAU,GAAG,KAAK,CAAA;AACtC,SAAA;AACF,OAAA;AACF,KAAA;IAEA,MAAMb,cAAc,GAAIl0B,GAAe,IAAK;AAC1C;AACA,MAAA,KAAK,IAAIoJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6qB,aAAa,CAAC32B,MAAM,EAAE8L,CAAC,EAAE,EAAE;QAC7C,IAAIpJ,GAAG,CAACmM,aAAa,CAAC8nB,aAAa,CAAC7qB,CAAC,CAAC,CAAE,KAAK,KAAK,EAAE;AAClD,UAAA,OAAO,KAAK,CAAA;AACd,SAAA;AACF,OAAA;AACA,MAAA,OAAO,IAAI,CAAA;KACZ,CAAA;;AAED;IACA,OAAO+pB,UAAU,CAACpK,QAAQ,CAAC3D,IAAI,EAAE8O,cAAc,EAAEn0B,KAAK,CAAC,CAAA;AACzD,GAAC,EACDT,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,qBAAqB,EAAE,MACjEP,KAAK,CAAC+iB,mBAAmB,EAC3B,CACF,CAAC,CAAA;AACL;;AClJO,SAASrQ,kBAAkBA,GAEP;EACzB,OAAO1S,KAAK,IACVxC,IAAI,CACF,MAAM,CAACwC,KAAK,CAAC2D,QAAQ,EAAE,CAACsN,QAAQ,EAAEjR,KAAK,CAACyS,qBAAqB,EAAE,CAAC,EAChE,CAACxB,QAAQ,EAAE+X,QAAQ,KAAK;IACtB,IAAI,CAACA,QAAQ,CAAC3D,IAAI,CAAC9nB,MAAM,IAAI,CAAC0T,QAAQ,CAAC1T,MAAM,EAAE;AAC7C,MAAA,OAAOyrB,QAAQ,CAAA;AACjB,KAAA;;AAEA;AACA,IAAA,MAAMiM,gBAAgB,GAAGhkB,QAAQ,CAAC7M,MAAM,CAAClE,QAAQ,IAC/CF,KAAK,CAACqI,SAAS,CAACnI,QAAQ,CAC1B,CAAC,CAAA;IAED,MAAMg1B,eAA6B,GAAG,EAAE,CAAA;IACxC,MAAMC,eAA2C,GAAG,EAAE,CAAA;AACtD;AACA;AACA;AACA;;AAEA;IACA,MAAMC,kBAAkB,GAAG,UACzB/P,IAAkB,EAClBrkB,KAAK,EACLiH,QAAiB,EACd;AAAA,MAAA,IAFHjH,KAAK,KAAA,KAAA,CAAA,EAAA;AAALA,QAAAA,KAAK,GAAG,CAAC,CAAA;AAAA,OAAA;AAGT;AACA;AACA,MAAA,IAAIA,KAAK,IAAIi0B,gBAAgB,CAAC13B,MAAM,EAAE;AACpC,QAAA,OAAO8nB,IAAI,CAAChiB,GAAG,CAACpD,GAAG,IAAI;UACrBA,GAAG,CAACe,KAAK,GAAGA,KAAK,CAAA;AAEjBk0B,UAAAA,eAAe,CAAC73B,IAAI,CAAC4C,GAAG,CAAC,CAAA;AACzBk1B,UAAAA,eAAe,CAACl1B,GAAG,CAACQ,EAAE,CAAC,GAAGR,GAAG,CAAA;UAE7B,IAAIA,GAAG,CAAC+H,OAAO,EAAE;AACf/H,YAAAA,GAAG,CAAC+H,OAAO,GAAGotB,kBAAkB,CAACn1B,GAAG,CAAC+H,OAAO,EAAEhH,KAAK,GAAG,CAAC,EAAEf,GAAG,CAACQ,EAAE,CAAC,CAAA;AAClE,WAAA;AAEA,UAAA,OAAOR,GAAG,CAAA;AACZ,SAAC,CAAC,CAAA;AACJ,OAAA;AAEA,MAAA,MAAMC,QAAgB,GAAG+0B,gBAAgB,CAACj0B,KAAK,CAAE,CAAA;;AAEjD;AACA,MAAA,MAAMq0B,YAAY,GAAGC,OAAO,CAACjQ,IAAI,EAAEnlB,QAAQ,CAAC,CAAA;;AAE5C;AACA,MAAA,MAAMq1B,qBAAqB,GAAG94B,KAAK,CAAC4T,IAAI,CAACglB,YAAY,CAACG,OAAO,EAAE,CAAC,CAACnyB,GAAG,CAClE,CAAAnC,IAAA,EAA+B3C,KAAK,KAAK;AAAA,QAAA,IAAxC,CAACk3B,aAAa,EAAEC,WAAW,CAAC,GAAAx0B,IAAA,CAAA;AAC3B,QAAA,IAAIT,EAAE,GAAI,CAAA,EAAEP,QAAS,CAAA,CAAA,EAAGu1B,aAAc,CAAC,CAAA,CAAA;QACvCh1B,EAAE,GAAGwH,QAAQ,GAAI,CAAA,EAAEA,QAAS,CAAGxH,CAAAA,EAAAA,EAAG,CAAC,CAAA,GAAGA,EAAE,CAAA;;AAExC;QACA,MAAMuH,OAAO,GAAGotB,kBAAkB,CAACM,WAAW,EAAE10B,KAAK,GAAG,CAAC,EAAEP,EAAE,CAAC,CAAA;;AAE9D;AACA,QAAA,MAAMiP,QAAQ,GAAG1O,KAAK,GAClBnE,SAAS,CAAC64B,WAAW,EAAEz1B,GAAG,IAAIA,GAAG,CAAC+H,OAAO,CAAC,GAC1C0tB,WAAW,CAAA;QAEf,MAAMz1B,GAAG,GAAG4H,SAAS,CACnB7H,KAAK,EACLS,EAAE,EACFiP,QAAQ,CAAC,CAAC,CAAC,CAAE5H,QAAQ,EACrBvJ,KAAK,EACLyC,KAAK,EACLQ,SAAS,EACTyG,QACF,CAAC,CAAA;AAEDgK,QAAAA,MAAM,CAACse,MAAM,CAACtwB,GAAG,EAAE;AACjB4S,UAAAA,gBAAgB,EAAE3S,QAAQ;UAC1Bu1B,aAAa;UACbztB,OAAO;UACP0H,QAAQ;UACRpP,QAAQ,EAAGJ,QAAgB,IAAK;AAC9B;AACA,YAAA,IAAI+0B,gBAAgB,CAACvzB,QAAQ,CAACxB,QAAQ,CAAC,EAAE;cACvC,IAAID,GAAG,CAACiI,YAAY,CAACE,cAAc,CAAClI,QAAQ,CAAC,EAAE;AAC7C,gBAAA,OAAOD,GAAG,CAACiI,YAAY,CAAChI,QAAQ,CAAC,CAAA;AACnC,eAAA;AAEA,cAAA,IAAIw1B,WAAW,CAAC,CAAC,CAAC,EAAE;AAAA,gBAAA,IAAAC,qBAAA,CAAA;gBAClB11B,GAAG,CAACiI,YAAY,CAAChI,QAAQ,CAAC,GAAAy1B,CAAAA,qBAAA,GACxBD,WAAW,CAAC,CAAC,CAAC,CAACp1B,QAAQ,CAACJ,QAAQ,CAAC,KAAAy1B,IAAAA,GAAAA,qBAAA,GAAIn0B,SAAS,CAAA;AAClD,eAAA;AAEA,cAAA,OAAOvB,GAAG,CAACiI,YAAY,CAAChI,QAAQ,CAAC,CAAA;AACnC,aAAA;YAEA,IAAID,GAAG,CAAC6S,oBAAoB,CAAC1K,cAAc,CAAClI,QAAQ,CAAC,EAAE;AACrD,cAAA,OAAOD,GAAG,CAAC6S,oBAAoB,CAAC5S,QAAQ,CAAC,CAAA;AAC3C,aAAA;;AAEA;AACA,YAAA,MAAM3E,MAAM,GAAGyE,KAAK,CAACqI,SAAS,CAACnI,QAAQ,CAAC,CAAA;YACxC,MAAM01B,WAAW,GAAGr6B,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAE6W,gBAAgB,EAAE,CAAA;AAE9C,YAAA,IAAIwjB,WAAW,EAAE;AACf31B,cAAAA,GAAG,CAAC6S,oBAAoB,CAAC5S,QAAQ,CAAC,GAAG01B,WAAW,CAC9C11B,QAAQ,EACRwP,QAAQ,EACRgmB,WACF,CAAC,CAAA;AAED,cAAA,OAAOz1B,GAAG,CAAC6S,oBAAoB,CAAC5S,QAAQ,CAAC,CAAA;AAC3C,aAAA;AACF,WAAA;AACF,SAAC,CAAC,CAAA;AAEF8H,QAAAA,OAAO,CAAC7K,OAAO,CAACmuB,MAAM,IAAI;AACxB4J,UAAAA,eAAe,CAAC73B,IAAI,CAACiuB,MAAM,CAAC,CAAA;AAC5B6J,UAAAA,eAAe,CAAC7J,MAAM,CAAC7qB,EAAE,CAAC,GAAG6qB,MAAM,CAAA;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACF,SAAC,CAAC,CAAA;AAEF,QAAA,OAAOrrB,GAAG,CAAA;AACZ,OACF,CAAC,CAAA;AAED,MAAA,OAAOs1B,qBAAqB,CAAA;KAC7B,CAAA;IAED,MAAMG,WAAW,GAAGN,kBAAkB,CAACpM,QAAQ,CAAC3D,IAAI,EAAE,CAAC,CAAC,CAAA;AAExDqQ,IAAAA,WAAW,CAACv4B,OAAO,CAACmuB,MAAM,IAAI;AAC5B4J,MAAAA,eAAe,CAAC73B,IAAI,CAACiuB,MAAM,CAAC,CAAA;AAC5B6J,MAAAA,eAAe,CAAC7J,MAAM,CAAC7qB,EAAE,CAAC,GAAG6qB,MAAM,CAAA;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACF,KAAC,CAAC,CAAA;IAEF,OAAO;AACLjG,MAAAA,IAAI,EAAEqQ,WAAW;AACjB9oB,MAAAA,QAAQ,EAAEsoB,eAAe;AACzB/T,MAAAA,QAAQ,EAAEgU,eAAAA;KACX,CAAA;GACF,EACD51B,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,oBAAoB,EAAE,MAAM;IACtEP,KAAK,CAACggB,MAAM,CAAC,MAAM;MACjBhgB,KAAK,CAAC8f,kBAAkB,EAAE,CAAA;MAC1B9f,KAAK,CAAC+iB,mBAAmB,EAAE,CAAA;AAC7B,KAAC,CAAC,CAAA;AACJ,GAAC,CACH,CAAC,CAAA;AACL,CAAA;AAEA,SAASuS,OAAOA,CAAwBjQ,IAAkB,EAAEnlB,QAAgB,EAAE;AAC5E,EAAA,MAAM21B,QAAQ,GAAG,IAAIjsB,GAAG,EAAqB,CAAA;EAE7C,OAAOyb,IAAI,CAAClc,MAAM,CAAC,CAAC9F,GAAG,EAAEpD,GAAG,KAAK;IAC/B,MAAM61B,MAAM,GAAI,CAAE71B,EAAAA,GAAG,CAACuR,gBAAgB,CAACtR,QAAQ,CAAE,CAAC,CAAA,CAAA;AAClD,IAAA,MAAM61B,QAAQ,GAAG1yB,GAAG,CAACkxB,GAAG,CAACuB,MAAM,CAAC,CAAA;IAChC,IAAI,CAACC,QAAQ,EAAE;MACb1yB,GAAG,CAACixB,GAAG,CAACwB,MAAM,EAAE,CAAC71B,GAAG,CAAC,CAAC,CAAA;AACxB,KAAC,MAAM;AACL81B,MAAAA,QAAQ,CAAC14B,IAAI,CAAC4C,GAAG,CAAC,CAAA;AACpB,KAAA;AACA,IAAA,OAAOoD,GAAG,CAAA;GACX,EAAEwyB,QAAQ,CAAC,CAAA;AACd;;AChLO,SAAS/Q,qBAAqBA,CAAwBnnB,IAE5D,EAAkD;AACjD,EAAA,OAAOqC,KAAK,IACVxC,IAAI,CACF,MAAM,CACJwC,KAAK,CAAC2D,QAAQ,EAAE,CAACkf,UAAU,EAC3B7iB,KAAK,CAAC0gB,wBAAwB,EAAE,EAChC1gB,KAAK,CAACO,OAAO,CAACof,oBAAoB,GAC9Bne,SAAS,GACTxB,KAAK,CAAC2D,QAAQ,EAAE,CAAC8b,QAAQ,CAC9B,EACD,CAACoD,UAAU,EAAEmG,QAAQ,KAAK;AACxB,IAAA,IAAI,CAACA,QAAQ,CAAC3D,IAAI,CAAC9nB,MAAM,EAAE;AACzB,MAAA,OAAOyrB,QAAQ,CAAA;AACjB,KAAA;IAEA,MAAM;MAAErG,QAAQ;AAAED,MAAAA,SAAAA;AAAU,KAAC,GAAGG,UAAU,CAAA;IAC1C,IAAI;MAAEwC,IAAI;MAAEzY,QAAQ;AAAEuU,MAAAA,QAAAA;AAAS,KAAC,GAAG6H,QAAQ,CAAA;AAC3C,IAAA,MAAMgN,SAAS,GAAGrT,QAAQ,GAAGD,SAAS,CAAA;AACtC,IAAA,MAAMuT,OAAO,GAAGD,SAAS,GAAGrT,QAAQ,CAAA;IAEpC0C,IAAI,GAAGA,IAAI,CAACjN,KAAK,CAAC4d,SAAS,EAAEC,OAAO,CAAC,CAAA;AAErC,IAAA,IAAIC,iBAAkC,CAAA;AAEtC,IAAA,IAAI,CAACl2B,KAAK,CAACO,OAAO,CAACof,oBAAoB,EAAE;MACvCuW,iBAAiB,GAAGvD,UAAU,CAAC;QAC7BtN,IAAI;QACJzY,QAAQ;AACRuU,QAAAA,QAAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAC,MAAM;AACL+U,MAAAA,iBAAiB,GAAG;QAClB7Q,IAAI;QACJzY,QAAQ;AACRuU,QAAAA,QAAAA;OACD,CAAA;AACH,KAAA;IAEA+U,iBAAiB,CAACtpB,QAAQ,GAAG,EAAE,CAAA;IAE/B,MAAMimB,SAAS,GAAI5yB,GAAe,IAAK;AACrCi2B,MAAAA,iBAAiB,CAACtpB,QAAQ,CAACvP,IAAI,CAAC4C,GAAG,CAAC,CAAA;AACpC,MAAA,IAAIA,GAAG,CAAC+H,OAAO,CAACzK,MAAM,EAAE;AACtB0C,QAAAA,GAAG,CAAC+H,OAAO,CAAC7K,OAAO,CAAC01B,SAAS,CAAC,CAAA;AAChC,OAAA;KACD,CAAA;AAEDqD,IAAAA,iBAAiB,CAAC7Q,IAAI,CAACloB,OAAO,CAAC01B,SAAS,CAAC,CAAA;AAEzC,IAAA,OAAOqD,iBAAiB,CAAA;GACzB,EACD32B,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,uBAAuB,CACrE,CAAC,CAAA;AACL;;ACvDO,SAAS+gB,iBAAiBA,GAEN;EACzB,OAAOthB,KAAK,IACVxC,IAAI,CACF,MAAM,CAACwC,KAAK,CAAC2D,QAAQ,EAAE,CAACkpB,OAAO,EAAE7sB,KAAK,CAAC+vB,oBAAoB,EAAE,CAAC,EAC9D,CAAClD,OAAO,EAAE7D,QAAQ,KAAK;AACrB,IAAA,IAAI,CAACA,QAAQ,CAAC3D,IAAI,CAAC9nB,MAAM,IAAI,EAACsvB,OAAO,IAAA,IAAA,IAAPA,OAAO,CAAEtvB,MAAM,CAAE,EAAA;AAC7C,MAAA,OAAOyrB,QAAQ,CAAA;AACjB,KAAA;IAEA,MAAMmN,YAAY,GAAGn2B,KAAK,CAAC2D,QAAQ,EAAE,CAACkpB,OAAO,CAAA;IAE7C,MAAMuJ,cAA4B,GAAG,EAAE,CAAA;;AAEvC;AACA,IAAA,MAAMC,gBAAgB,GAAGF,YAAY,CAAC/xB,MAAM,CAC1C6L,IAAI,IAAA;AAAA,MAAA,IAAA6iB,gBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,gBAAA,GAAI9yB,KAAK,CAACqI,SAAS,CAAC4H,IAAI,CAACxP,EAAE,CAAC,KAAxBqyB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAA,CAA0B7D,UAAU,EAAE,CAAA;AAAA,KAChD,CAAC,CAAA;IAED,MAAMqH,cAOL,GAAG,EAAE,CAAA;AAEND,IAAAA,gBAAgB,CAACl5B,OAAO,CAACo5B,SAAS,IAAI;MACpC,MAAMh7B,MAAM,GAAGyE,KAAK,CAACqI,SAAS,CAACkuB,SAAS,CAAC91B,EAAE,CAAC,CAAA;MAC5C,IAAI,CAAClF,MAAM,EAAE,OAAA;AAEb+6B,MAAAA,cAAc,CAACC,SAAS,CAAC91B,EAAE,CAAC,GAAG;AAC7BssB,QAAAA,aAAa,EAAExxB,MAAM,CAACwF,SAAS,CAACgsB,aAAa;AAC7CyJ,QAAAA,aAAa,EAAEj7B,MAAM,CAACwF,SAAS,CAACy1B,aAAa;AAC7C1J,QAAAA,SAAS,EAAEvxB,MAAM,CAACgyB,YAAY,EAAC;OAChC,CAAA;AACH,KAAC,CAAC,CAAA;IAEF,MAAMkJ,QAAQ,GAAIpR,IAAkB,IAAK;AACvC;AACA;AACA,MAAA,MAAMqR,UAAU,GAAGrR,IAAI,CAAChiB,GAAG,CAACpD,GAAG,KAAK;QAAE,GAAGA,GAAAA;AAAI,OAAC,CAAC,CAAC,CAAA;AAEhDy2B,MAAAA,UAAU,CAACzmB,IAAI,CAAC,CAACyb,IAAI,EAAEC,IAAI,KAAK;AAC9B,QAAA,KAAK,IAAItiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgtB,gBAAgB,CAAC94B,MAAM,EAAE8L,CAAC,IAAI,CAAC,EAAE;AAAA,UAAA,IAAAstB,eAAA,CAAA;AACnD,UAAA,MAAMJ,SAAS,GAAGF,gBAAgB,CAAChtB,CAAC,CAAE,CAAA;AACtC,UAAA,MAAMutB,UAAU,GAAGN,cAAc,CAACC,SAAS,CAAC91B,EAAE,CAAE,CAAA;AAChD,UAAA,MAAMssB,aAAa,GAAG6J,UAAU,CAAC7J,aAAa,CAAA;AAC9C,UAAA,MAAM8J,MAAM,GAAA,CAAAF,eAAA,GAAGJ,SAAS,IAAA,IAAA,GAAA,KAAA,CAAA,GAATA,SAAS,CAAE5I,IAAI,KAAA,IAAA,GAAAgJ,eAAA,GAAI,KAAK,CAAA;UAEvC,IAAIG,OAAO,GAAG,CAAC,CAAA;;AAEf;AACA,UAAA,IAAI/J,aAAa,EAAE;YACjB,MAAMgK,MAAM,GAAGrL,IAAI,CAACprB,QAAQ,CAACi2B,SAAS,CAAC91B,EAAE,CAAC,CAAA;YAC1C,MAAMu2B,MAAM,GAAGrL,IAAI,CAACrrB,QAAQ,CAACi2B,SAAS,CAAC91B,EAAE,CAAC,CAAA;AAE1C,YAAA,MAAMw2B,UAAU,GAAGF,MAAM,KAAKv1B,SAAS,CAAA;AACvC,YAAA,MAAM01B,UAAU,GAAGF,MAAM,KAAKx1B,SAAS,CAAA;YAEvC,IAAIy1B,UAAU,IAAIC,UAAU,EAAE;cAC5B,IAAInK,aAAa,KAAK,OAAO,EAAE,OAAOkK,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;cACzD,IAAIlK,aAAa,KAAK,MAAM,EAAE,OAAOkK,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;AACxDH,cAAAA,OAAO,GACLG,UAAU,IAAIC,UAAU,GACpB,CAAC,GACDD,UAAU,GACRlK,aAAa,GACb,CAACA,aAAa,CAAA;AACxB,aAAA;AACF,WAAA;UAEA,IAAI+J,OAAO,KAAK,CAAC,EAAE;AACjBA,YAAAA,OAAO,GAAGF,UAAU,CAAC9J,SAAS,CAACpB,IAAI,EAAEC,IAAI,EAAE4K,SAAS,CAAC91B,EAAE,CAAC,CAAA;AAC1D,WAAA;;AAEA;UACA,IAAIq2B,OAAO,KAAK,CAAC,EAAE;AACjB,YAAA,IAAID,MAAM,EAAE;cACVC,OAAO,IAAI,CAAC,CAAC,CAAA;AACf,aAAA;YAEA,IAAIF,UAAU,CAACJ,aAAa,EAAE;cAC5BM,OAAO,IAAI,CAAC,CAAC,CAAA;AACf,aAAA;AAEA,YAAA,OAAOA,OAAO,CAAA;AAChB,WAAA;AACF,SAAA;AAEA,QAAA,OAAOpL,IAAI,CAACntB,KAAK,GAAGotB,IAAI,CAACptB,KAAK,CAAA;AAChC,OAAC,CAAC,CAAA;;AAEF;AACAm4B,MAAAA,UAAU,CAACv5B,OAAO,CAAC8C,GAAG,IAAI;AAAA,QAAA,IAAAgT,YAAA,CAAA;AACxBmjB,QAAAA,cAAc,CAAC/4B,IAAI,CAAC4C,GAAG,CAAC,CAAA;QACxB,IAAAgT,CAAAA,YAAA,GAAIhT,GAAG,CAAC+H,OAAO,KAAXiL,IAAAA,IAAAA,YAAA,CAAa1V,MAAM,EAAE;UACvB0C,GAAG,CAAC+H,OAAO,GAAGyuB,QAAQ,CAACx2B,GAAG,CAAC+H,OAAO,CAAC,CAAA;AACrC,SAAA;AACF,OAAC,CAAC,CAAA;AAEF,MAAA,OAAO0uB,UAAU,CAAA;KAClB,CAAA;IAED,OAAO;AACLrR,MAAAA,IAAI,EAAEoR,QAAQ,CAACzN,QAAQ,CAAC3D,IAAI,CAAC;AAC7BzY,MAAAA,QAAQ,EAAEwpB,cAAc;MACxBjV,QAAQ,EAAE6H,QAAQ,CAAC7H,QAAAA;KACpB,CAAA;AACH,GAAC,EACD5hB,cAAc,CAACS,KAAK,CAACO,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,MAC/DP,KAAK,CAAC+iB,mBAAmB,EAC3B,CACF,CAAC,CAAA;AACL;;;;"}