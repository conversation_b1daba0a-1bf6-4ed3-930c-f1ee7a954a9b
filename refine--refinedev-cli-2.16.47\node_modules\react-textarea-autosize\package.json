{"name": "react-textarea-autosize", "description": "textarea component for React which grows with content", "version": "8.3.4", "keywords": ["autosize", "grow", "react", "react-component", "textarea"], "repository": {"type": "git", "url": "git+https://github.com/Andarist/react-textarea-autosize.git"}, "license": "MIT", "main": "dist/react-textarea-autosize.cjs.js", "module": "dist/react-textarea-autosize.esm.js", "browser": {"./dist/react-textarea-autosize.cjs.js": "./dist/react-textarea-autosize.browser.cjs.js", "./dist/react-textarea-autosize.esm.js": "./dist/react-textarea-autosize.browser.esm.js"}, "sideEffects": false, "files": ["dist"], "author": "<PERSON><PERSON> <<EMAIL>> (httsps://andreypopp.com/)", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/Andarist)"], "scripts": {"prebuild": "npm run clean", "build": "preconstruct build", "docs:dev": "npm run dev --prefix example", "docs:build": "npm run build --prefix example", "docs:publish": "npm run docs:build && cd ./example/dist && git init && git commit --allow-empty -m 'update docs' && git checkout -b gh-pages && touch .nojekyll && git add . && git commit -am 'update docs' && <NAME_EMAIL>:Andarist/react-textarea-autosize gh-pages --force", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint --ext .js,.ts,.tsx src", "prepare": "npm run build", "changeset": "changeset", "prerelease": "npm run lint && npm test", "release": "changeset publish", "test": "jest", "test:watch": "npm test -- --watch"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "dependencies": {"@babel/runtime": "^7.10.2", "use-composed-ref": "^1.3.0", "use-latest": "^1.2.1"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/plugin-proposal-object-rest-spread": "^7.10.4", "@babel/plugin-transform-runtime": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/preset-react": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@changesets/changelog-github": "^0.4.4", "@changesets/cli": "^2.22.0", "@preconstruct/cli": "^2.0.5", "@testing-library/jest-dom": "^5.9.0", "@testing-library/react": "^10.1.0", "@types/react": "^16.9.35", "@types/react-dom": "^16.9.8", "@typescript-eslint/eslint-plugin": "^3.1.0", "@typescript-eslint/parser": "^3.1.0", "babel-eslint": "11.0.0-beta.2", "bytes": "^3.1.0", "cross-env": "^7.0.2", "eslint": "^7.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-react": "^7.20.0", "husky": "^4.2.5", "jest": "^26.0.1", "lint-staged": "^10.2.8", "parcel": "2.0.0-nightly.454", "prettier": "^2.0.5", "react": "^16.13.1", "react-dom": "^16.13.1", "rimraf": "^3.0.2", "terser": "^4.7.0", "typescript": "^3.9.3"}, "engines": {"node": ">=10"}, "packageManager": "yarn@3.1.1"}