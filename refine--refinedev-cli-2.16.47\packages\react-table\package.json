{"name": "@refinedev/react-table", "version": "5.6.17", "private": false, "description": "refine offers a React Table adapter that allows you to use the React Table library with refine.", "repository": {"type": "git", "url": "https://github.com/refinedev/refine.git", "directory": "packages/react-table"}, "license": "MIT", "author": "refine", "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "typings": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"attw": "attw --pack .", "build": "tsup && node ../shared/generate-declarations.js", "dev": "tsup --watch", "prepare": "pnpm build", "publint": "publint --strict=true --level=suggestion", "test": "jest --passWithNoTests --runInBand", "types": "node ../shared/generate-declarations.js"}, "dependencies": {"@tanstack/react-table": "^8.2.6", "lodash": "^4.17.21", "lodash-es": "^4.17.21"}, "devDependencies": {"@esbuild-plugins/node-resolve": "^0.1.4", "@refinedev/core": "^4.57.1", "@types/jest": "^29.2.4", "@types/lodash": "^4.14.171", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "react-router": "^7.0.2", "ts-jest": "^29.1.2", "tslib": "^2.6.2", "tsup": "^6.7.0", "typescript": "^5.4.2"}, "peerDependencies": {"@refinedev/core": "^4.46.1", "@tanstack/react-table": "^8.2.6", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "engines": {"node": ">=10"}, "publishConfig": {"access": "public"}}