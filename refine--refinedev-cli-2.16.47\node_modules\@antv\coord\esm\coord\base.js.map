{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../src/coord/base.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AAQpC;;GAEG;AACH;IA+BE,oBAAY,GAAkB;QA9B9B,OAAO;QACS,SAAI,GAAW,YAAY,CAAC;QAC5B,WAAM,GAAY,KAAK,CAAC;QACxB,YAAO,GAAY,KAAK,CAAC;QACzB,YAAO,GAAY,KAAK,CAAC;QAqBjC,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QAKjB,IAAA,KAAK,GAAsE,GAAG,MAAzE,EAAE,GAAG,GAAiE,GAAG,IAApE,EAAE,KAA+D,GAAG,OAA9B,EAApC,MAAM,mBAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAA,EAAE,KAAyB,GAAG,aAAR,EAApB,YAAY,mBAAG,KAAK,KAAA,CAAS;QACvF,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,MAAiB,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,kBAAI,MAAM,CAAY,CAAC,CAAC,OAAO;QACrD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,4BAAO,GAAd;QACE,sBAAsB;QACtB,IAAI,CAAC,MAAM,GAAG;YACZ,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAClC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;SACnC,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;;OAGG;IACI,2BAAM,GAAb,UAAc,GAAkB;QAC9B,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAEM,+BAAU,GAAjB,UAAkB,OAAe,EAAE,GAAW;;QACxC,IAAA,KAAiB,IAAI,CAAC,GAAG,CAAC,EAAxB,KAAK,WAAA,EAAE,GAAG,SAAc,CAAC;QAE/B,KAAK;QACL,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvB,KAAe,CAAC,GAAG,EAAE,KAAK,CAAC,EAA1B,KAAK,QAAA,EAAE,GAAG,QAAA,CAAiB;SAC7B;QAED,OAAO,KAAK,GAAG,OAAO,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;IACzC,CAAC;IAEM,8BAAS,GAAhB,UAAiB,KAAa,EAAE,GAAW;;QACrC,IAAA,KAAiB,IAAI,CAAC,GAAG,CAAC,EAAxB,KAAK,WAAA,EAAE,GAAG,SAAc,CAAC;QAC/B,KAAK;QACL,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvB,KAAe,CAAC,GAAG,EAAE,KAAK,CAAC,EAA1B,KAAK,QAAA,EAAE,GAAG,QAAA,CAAiB;SAC7B;QAED,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;;;;;OAMG;IACI,gCAAW,GAAlB,UAAmB,CAAS,EAAE,CAAS,EAAE,GAAe;QAAf,oBAAA,EAAA,OAAe;QACtD,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAM,MAAM,GAAY,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,iCAAY,GAAnB,UAAoB,CAAS,EAAE,CAAS,EAAE,GAAe;QAAf,oBAAA,EAAA,OAAe;QACvD,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAClE,IAAM,MAAM,GAAY,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACpC,IAAI,QAAQ,EAAE;YACZ,oBAAoB;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;SAC9C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,4BAAO,GAAd,UAAe,KAAY;QACnB,IAAA,KAAW,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAjC,CAAC,OAAA,EAAE,CAAC,OAA6B,CAAC;QAC1C,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,OAAO;YACL,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;YACZ,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;SACb,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,2BAAM,GAAb,UAAc,KAAY;QACxB,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;YACZ,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;SACb,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,2BAAM,GAAb,UAAc,MAAc;QAC1B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QACvC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACI,4BAAO,GAAd,UAAe,GAAW;QACxB,IAAI,GAAG,KAAK,GAAG,EAAE;YACf,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;SACpC;aAAM;YACL,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;SACpC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACI,0BAAK,GAAZ,UAAa,EAAU,EAAE,EAAU;QACjC,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACxC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACI,8BAAS,GAAhB,UAAiB,CAAS,EAAE,CAAS;QACnC,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACI,8BAAS,GAAhB;QACE,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,8BAAS,GAAhB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEM,6BAAQ,GAAf;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAEM,8BAAS,GAAhB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEM,8BAAS,GAAhB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;OAGG;IACI,8BAAS,GAAhB,UAAiB,GAAW;QAC1B,OAAO,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;IACzD,CAAC;IAED;;;OAGG;IACI,gCAAW,GAAlB,UAAmB,MAAgB;QACjC,SAAS;QACT,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAE,kBAAI,IAAI,CAAC,cAAc,CAAa,CAAC;IACxE,CAAC;IAaH,iBAAC;AAAD,CAAC,AA3PD,IA2PC"}