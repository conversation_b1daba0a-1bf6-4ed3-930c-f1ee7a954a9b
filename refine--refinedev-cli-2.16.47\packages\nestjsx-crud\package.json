{"name": "@refinedev/nestjsx-crud", "version": "5.0.13", "private": false, "description": "“Nestjsx CRUD Data Provider for Refine, enabling easy integration with NestJS CRUD operations.", "repository": {"type": "git", "url": "https://github.com/refinedev/refine.git", "directory": "packages/nestjsx-crud"}, "license": "MIT", "author": "refine", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "typings": "dist/index.d.ts", "files": ["dist", "src", "./refine.config.js"], "scripts": {"attw": "attw --pack .", "build": "tsup && node ../shared/generate-declarations.js", "dev": "tsup --watch", "prepare": "pnpm build", "publint": "publint --strict=true --level=suggestion", "test": "jest --passWithNoTests --runInBand", "types": "node ../shared/generate-declarations.js"}, "dependencies": {"@nestjsx/crud-request": "^5.0.0-alpha.3", "axios": "^1.11.0", "query-string": "^7.1.1"}, "devDependencies": {"@esbuild-plugins/node-resolve": "^0.1.4", "@refinedev/cli": "^2.16.47", "@refinedev/core": "^4.57.11", "@types/jest": "^29.2.4", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "nock": "^13.4.0", "ts-jest": "^29.1.2", "tslib": "^2.6.2", "tsup": "^6.7.0", "typescript": "^5.4.2"}, "peerDependencies": {"@refinedev/core": "^4.46.1"}, "engines": {"node": ">=10"}, "publishConfig": {"access": "public"}}