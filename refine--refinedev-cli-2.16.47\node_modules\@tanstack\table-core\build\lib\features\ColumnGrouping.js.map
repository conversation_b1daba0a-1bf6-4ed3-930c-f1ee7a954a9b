{"version": 3, "file": "ColumnGrouping.js", "sources": ["../../../src/features/ColumnGrouping.ts"], "sourcesContent": ["import { RowModel } from '..'\nimport { BuiltInAggregationFn, aggregationFns } from '../aggregationFns'\nimport {\n  AggregationFns,\n  Cell,\n  Column,\n  ColumnDefTemplate,\n  OnChangeFn,\n  Row,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type GroupingState = string[]\n\nexport interface GroupingTableState {\n  grouping: GroupingState\n}\n\nexport type AggregationFn<TData extends RowData> = (\n  columnId: string,\n  leafRows: Row<TData>[],\n  childRows: Row<TData>[]\n) => any\n\nexport type CustomAggregationFns = Record<string, AggregationFn<any>>\n\nexport type AggregationFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof AggregationFns\n  | BuiltInAggregationFn\n  | AggregationFn<TData>\n\nexport interface GroupingColumnDef<TData extends RowData, TValue> {\n  /**\n   * The cell to display each row for the column if the cell is an aggregate. If a function is passed, it will be passed a props object with the context of the cell and should return the property type for your adapter (the exact type depends on the adapter being used).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregatedcell)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregatedCell?: ColumnDefTemplate<\n    ReturnType<Cell<TData, TValue>['getContext']>\n  >\n  /**\n   * The resolved aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregationFn?: AggregationFnOption<TData>\n  /**\n   * Enables/disables grouping for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Specify a value to be used for grouping rows on this column. If this option is not specified, the value derived from `accessorKey` / `accessorFn` will be used instead.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue?: (row: TData) => any\n}\n\nexport interface GroupingColumn<TData extends RowData> {\n  /**\n   * Returns the aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns the automatically inferred aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getautoaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAutoAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getcangroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getCanGroup: () => boolean\n  /**\n   * Returns the index of the column in the grouping state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedIndex: () => number\n  /**\n   * Returns whether or not the column is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns a function that toggles the grouping state of the column. This is useful for passing to the `onClick` prop of a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#gettogglegroupinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getToggleGroupingHandler: () => () => void\n  /**\n   * Toggles the grouping state of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#togglegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  toggleGrouping: () => void\n}\n\nexport interface GroupingRow {\n  _groupingValuesCache: Record<string, any>\n  /**\n   * Returns the grouping value for any row and column (including leaf rows).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue: (columnId: string) => unknown\n  /**\n   * Returns whether or not the row is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * If this row is grouped, this is the id of the column that this row is grouped by.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingcolumnid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingColumnId?: string\n  /**\n   * If this row is grouped, this is the unique/shared value for the `groupingColumnId` for all of the rows in this group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingValue?: unknown\n}\n\nexport interface GroupingCell {\n  /**\n   * Returns whether or not the cell is currently aggregated.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisaggregated)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsAggregated: () => boolean\n  /**\n   * Returns whether or not the cell is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns whether or not the cell is currently a placeholder cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsPlaceholder: () => boolean\n}\n\nexport interface ColumnDefaultOptions {\n  enableGrouping: boolean\n  onGroupingChange: OnChangeFn<GroupingState>\n}\n\ninterface GroupingOptionsBase {\n  /**\n   * Enables/disables grouping for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Returns the row model after grouping has taken place, but no further.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Grouping columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupedcolumnmode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupedColumnMode?: false | 'reorder' | 'remove'\n  /**\n   * Enables manual grouping. If this option is set to `true`, the table will not automatically group rows using `getGroupedRowModel()` and instead will expect you to manually group the rows before passing them to the table. This is useful if you are doing server-side grouping and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#manualgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  manualGrouping?: boolean\n  /**\n   * If this function is provided, it will be called when the grouping state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.grouping` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#ongroupingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  onGroupingChange?: OnChangeFn<GroupingState>\n}\n\ntype ResolvedAggregationFns = keyof AggregationFns extends never\n  ? {\n      aggregationFns?: Record<string, AggregationFn<any>>\n    }\n  : {\n      aggregationFns: Record<keyof AggregationFns, AggregationFn<any>>\n    }\n\nexport interface GroupingOptions\n  extends GroupingOptionsBase,\n    ResolvedAggregationFns {}\n\nexport type GroupingColumnMode = false | 'reorder' | 'remove'\n\nexport interface GroupingInstance<TData extends RowData> {\n  _getGroupedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getpregroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getPreGroupedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **grouping** state to `initialState.grouping`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#resetgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  resetGrouping: (defaultState?: boolean) => void\n  /**\n   * Updates the grouping state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#setgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  setGrouping: (updater: Updater<GroupingState>) => void\n}\n\n//\n\nexport const ColumnGrouping: TableFeature = {\n  getDefaultColumnDef: <TData extends RowData>(): GroupingColumnDef<\n    TData,\n    unknown\n  > => {\n    return {\n      aggregatedCell: props => (props.getValue() as any)?.toString?.() ?? null,\n      aggregationFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): GroupingTableState => {\n    return {\n      grouping: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GroupingOptions => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder',\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleGrouping = () => {\n      table.setGrouping(old => {\n        // Find any existing grouping for this column\n        if (old?.includes(column.id)) {\n          return old.filter(d => d !== column.id)\n        }\n\n        return [...(old ?? []), column.id]\n      })\n    }\n\n    column.getCanGroup = () => {\n      return (\n        (column.columnDef.enableGrouping ?? true) &&\n        (table.options.enableGrouping ?? true) &&\n        (!!column.accessorFn || !!column.columnDef.getGroupingValue)\n      )\n    }\n\n    column.getIsGrouped = () => {\n      return table.getState().grouping?.includes(column.id)\n    }\n\n    column.getGroupedIndex = () => table.getState().grouping?.indexOf(column.id)\n\n    column.getToggleGroupingHandler = () => {\n      const canGroup = column.getCanGroup()\n\n      return () => {\n        if (!canGroup) return\n        column.toggleGrouping()\n      }\n    }\n    column.getAutoAggregationFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'number') {\n        return aggregationFns.sum\n      }\n\n      if (Object.prototype.toString.call(value) === '[object Date]') {\n        return aggregationFns.extent\n      }\n    }\n    column.getAggregationFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.aggregationFn)\n        ? column.columnDef.aggregationFn\n        : column.columnDef.aggregationFn === 'auto'\n          ? column.getAutoAggregationFn()\n          : table.options.aggregationFns?.[\n              column.columnDef.aggregationFn as string\n            ] ??\n            aggregationFns[\n              column.columnDef.aggregationFn as BuiltInAggregationFn\n            ]\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setGrouping = updater => table.options.onGroupingChange?.(updater)\n\n    table.resetGrouping = defaultState => {\n      table.setGrouping(defaultState ? [] : table.initialState?.grouping ?? [])\n    }\n\n    table.getPreGroupedRowModel = () => table.getFilteredRowModel()\n    table.getGroupedRowModel = () => {\n      if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n        table._getGroupedRowModel = table.options.getGroupedRowModel(table)\n      }\n\n      if (table.options.manualGrouping || !table._getGroupedRowModel) {\n        return table.getPreGroupedRowModel()\n      }\n\n      return table._getGroupedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getIsGrouped = () => !!row.groupingColumnId\n    row.getGroupingValue = columnId => {\n      if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n        return row._groupingValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.columnDef.getGroupingValue) {\n        return row.getValue(columnId)\n      }\n\n      row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(\n        row.original\n      )\n\n      return row._groupingValuesCache[columnId]\n    }\n    row._groupingValuesCache = {}\n  },\n\n  createCell: <TData extends RowData, TValue>(\n    cell: Cell<TData, TValue>,\n    column: Column<TData, TValue>,\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    const getRenderValue = () =>\n      cell.getValue() ?? table.options.renderFallbackValue\n\n    cell.getIsGrouped = () =>\n      column.getIsGrouped() && column.id === row.groupingColumnId\n    cell.getIsPlaceholder = () => !cell.getIsGrouped() && column.getIsGrouped()\n    cell.getIsAggregated = () =>\n      !cell.getIsGrouped() && !cell.getIsPlaceholder() && !!row.subRows?.length\n  },\n}\n\nexport function orderColumns<TData extends RowData>(\n  leafColumns: Column<TData, unknown>[],\n  grouping: string[],\n  groupedColumnMode?: GroupingColumnMode\n) {\n  if (!grouping?.length || !groupedColumnMode) {\n    return leafColumns\n  }\n\n  const nonGroupingColumns = leafColumns.filter(\n    col => !grouping.includes(col.id)\n  )\n\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns\n  }\n\n  const groupingColumns = grouping\n    .map(g => leafColumns.find(col => col.id === g)!)\n    .filter(Boolean)\n\n  return [...groupingColumns, ...nonGroupingColumns]\n}\n"], "names": ["ColumnGrouping", "getDefaultColumnDef", "aggregatedCell", "props", "_toString", "_props$getValue", "getValue", "toString", "aggregationFn", "getInitialState", "state", "grouping", "getDefaultOptions", "table", "onGroupingChange", "makeStateUpdater", "groupedColumnMode", "createColumn", "column", "toggleGrouping", "setGrouping", "old", "includes", "id", "filter", "d", "getCanGroup", "_column$columnDef$ena", "_table$options$enable", "columnDef", "enableGrouping", "options", "accessorFn", "getGroupingValue", "getIsGrouped", "_table$getState$group", "getState", "getGroupedIndex", "_table$getState$group2", "indexOf", "getToggleGroupingHandler", "canGroup", "getAutoAggregationFn", "firstRow", "getCoreRowModel", "flatRows", "value", "aggregationFns", "sum", "Object", "prototype", "call", "extent", "getAggregationFn", "_table$options$aggreg", "_table$options$aggreg2", "Error", "isFunction", "createTable", "updater", "resetGrouping", "defaultState", "_table$initialState$g", "_table$initialState", "initialState", "getPreGroupedRowModel", "getFilteredRowModel", "getGroupedRowModel", "_getGroupedRowModel", "manualGrouping", "createRow", "row", "groupingColumnId", "columnId", "_groupingValuesCache", "hasOwnProperty", "getColumn", "original", "createCell", "cell", "getIsPlaceholder", "getIsAggregated", "_row$subRows", "subRows", "length", "orderColumns", "leafColumns", "nonGroupingColumns", "col", "groupingColumns", "map", "g", "find", "Boolean"], "mappings": ";;;;;;;;;;;;;;;AA+OA;;AAEO,MAAMA,cAA4B,GAAG;EAC1CC,mBAAmB,EAAEA,MAGhB;IACH,OAAO;AACLC,MAAAA,cAAc,EAAEC,KAAK,IAAA;QAAA,IAAAC,SAAA,EAAAC,eAAA,CAAA;QAAA,OAAAD,CAAAA,SAAA,IAAAC,eAAA,GAAKF,KAAK,CAACG,QAAQ,EAAE,KAAjBD,IAAAA,IAAAA,eAAA,CAA2BE,QAAQ,IAAA,IAAA,GAAA,KAAA,CAAA,GAAnCF,eAAA,CAA2BE,QAAQ,EAAI,KAAA,IAAA,GAAAH,SAAA,GAAI,IAAI,CAAA;AAAA,OAAA;AACxEI,MAAAA,aAAa,EAAE,MAAA;KAChB,CAAA;GACF;EAEDC,eAAe,EAAGC,KAAK,IAAyB;IAC9C,OAAO;AACLC,MAAAA,QAAQ,EAAE,EAAE;MACZ,GAAGD,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfC,KAAmB,IACC;IACpB,OAAO;AACLC,MAAAA,gBAAgB,EAAEC,sBAAgB,CAAC,UAAU,EAAEF,KAAK,CAAC;AACrDG,MAAAA,iBAAiB,EAAE,SAAA;KACpB,CAAA;GACF;AAEDC,EAAAA,YAAY,EAAEA,CACZC,MAA6B,EAC7BL,KAAmB,KACV;IACTK,MAAM,CAACC,cAAc,GAAG,MAAM;AAC5BN,MAAAA,KAAK,CAACO,WAAW,CAACC,GAAG,IAAI;AACvB;QACA,IAAIA,GAAG,IAAHA,IAAAA,IAAAA,GAAG,CAAEC,QAAQ,CAACJ,MAAM,CAACK,EAAE,CAAC,EAAE;UAC5B,OAAOF,GAAG,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKP,MAAM,CAACK,EAAE,CAAC,CAAA;AACzC,SAAA;AAEA,QAAA,OAAO,CAAC,IAAIF,GAAG,IAAA,IAAA,GAAHA,GAAG,GAAI,EAAE,GAAGH,MAAM,CAACK,EAAE,CAAC,CAAA;AACpC,OAAC,CAAC,CAAA;KACH,CAAA;IAEDL,MAAM,CAACQ,WAAW,GAAG,MAAM;MAAA,IAAAC,qBAAA,EAAAC,qBAAA,CAAA;AACzB,MAAA,OACE,EAAAD,qBAAA,GAACT,MAAM,CAACW,SAAS,CAACC,cAAc,KAAAH,IAAAA,GAAAA,qBAAA,GAAI,IAAI,MAAA,CAAAC,qBAAA,GACvCf,KAAK,CAACkB,OAAO,CAACD,cAAc,KAAA,IAAA,GAAAF,qBAAA,GAAI,IAAI,CAAC,KACrC,CAAC,CAACV,MAAM,CAACc,UAAU,IAAI,CAAC,CAACd,MAAM,CAACW,SAAS,CAACI,gBAAgB,CAAC,CAAA;KAE/D,CAAA;IAEDf,MAAM,CAACgB,YAAY,GAAG,MAAM;AAAA,MAAA,IAAAC,qBAAA,CAAA;AAC1B,MAAA,OAAA,CAAAA,qBAAA,GAAOtB,KAAK,CAACuB,QAAQ,EAAE,CAACzB,QAAQ,KAAA,IAAA,GAAA,KAAA,CAAA,GAAzBwB,qBAAA,CAA2Bb,QAAQ,CAACJ,MAAM,CAACK,EAAE,CAAC,CAAA;KACtD,CAAA;IAEDL,MAAM,CAACmB,eAAe,GAAG,MAAA;AAAA,MAAA,IAAAC,sBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,sBAAA,GAAMzB,KAAK,CAACuB,QAAQ,EAAE,CAACzB,QAAQ,KAAA,IAAA,GAAA,KAAA,CAAA,GAAzB2B,sBAAA,CAA2BC,OAAO,CAACrB,MAAM,CAACK,EAAE,CAAC,CAAA;AAAA,KAAA,CAAA;IAE5EL,MAAM,CAACsB,wBAAwB,GAAG,MAAM;AACtC,MAAA,MAAMC,QAAQ,GAAGvB,MAAM,CAACQ,WAAW,EAAE,CAAA;AAErC,MAAA,OAAO,MAAM;QACX,IAAI,CAACe,QAAQ,EAAE,OAAA;QACfvB,MAAM,CAACC,cAAc,EAAE,CAAA;OACxB,CAAA;KACF,CAAA;IACDD,MAAM,CAACwB,oBAAoB,GAAG,MAAM;MAClC,MAAMC,QAAQ,GAAG9B,KAAK,CAAC+B,eAAe,EAAE,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAA;MAEpD,MAAMC,KAAK,GAAGH,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAErC,QAAQ,CAACY,MAAM,CAACK,EAAE,CAAC,CAAA;AAE3C,MAAA,IAAI,OAAOuB,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOC,6BAAc,CAACC,GAAG,CAAA;AAC3B,OAAA;AAEA,MAAA,IAAIC,MAAM,CAACC,SAAS,CAAC3C,QAAQ,CAAC4C,IAAI,CAACL,KAAK,CAAC,KAAK,eAAe,EAAE;QAC7D,OAAOC,6BAAc,CAACK,MAAM,CAAA;AAC9B,OAAA;KACD,CAAA;IACDlC,MAAM,CAACmC,gBAAgB,GAAG,MAAM;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAC9B,IAAI,CAACrC,MAAM,EAAE;QACX,MAAM,IAAIsC,KAAK,EAAE,CAAA;AACnB,OAAA;MAEA,OAAOC,gBAAU,CAACvC,MAAM,CAACW,SAAS,CAACrB,aAAa,CAAC,GAC7CU,MAAM,CAACW,SAAS,CAACrB,aAAa,GAC9BU,MAAM,CAACW,SAAS,CAACrB,aAAa,KAAK,MAAM,GACvCU,MAAM,CAACwB,oBAAoB,EAAE,IAAAY,qBAAA,GAAA,CAAAC,sBAAA,GAC7B1C,KAAK,CAACkB,OAAO,CAACgB,cAAc,KAAA,IAAA,GAAA,KAAA,CAAA,GAA5BQ,sBAAA,CACErC,MAAM,CAACW,SAAS,CAACrB,aAAa,CAC/B,KAAA8C,IAAAA,GAAAA,qBAAA,GACDP,6BAAc,CACZ7B,MAAM,CAACW,SAAS,CAACrB,aAAa,CAC/B,CAAA;KACR,CAAA;GACF;EAEDkD,WAAW,EAA0B7C,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACO,WAAW,GAAGuC,OAAO,IAAI9C,KAAK,CAACkB,OAAO,CAACjB,gBAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAA9BD,KAAK,CAACkB,OAAO,CAACjB,gBAAgB,CAAG6C,OAAO,CAAC,CAAA;AAExE9C,IAAAA,KAAK,CAAC+C,aAAa,GAAGC,YAAY,IAAI;MAAA,IAAAC,qBAAA,EAAAC,mBAAA,CAAA;MACpClD,KAAK,CAACO,WAAW,CAACyC,YAAY,GAAG,EAAE,GAAA,CAAAC,qBAAA,GAAA,CAAAC,mBAAA,GAAGlD,KAAK,CAACmD,YAAY,qBAAlBD,mBAAA,CAAoBpD,QAAQ,KAAAmD,IAAAA,GAAAA,qBAAA,GAAI,EAAE,CAAC,CAAA;KAC1E,CAAA;IAEDjD,KAAK,CAACoD,qBAAqB,GAAG,MAAMpD,KAAK,CAACqD,mBAAmB,EAAE,CAAA;IAC/DrD,KAAK,CAACsD,kBAAkB,GAAG,MAAM;MAC/B,IAAI,CAACtD,KAAK,CAACuD,mBAAmB,IAAIvD,KAAK,CAACkB,OAAO,CAACoC,kBAAkB,EAAE;QAClEtD,KAAK,CAACuD,mBAAmB,GAAGvD,KAAK,CAACkB,OAAO,CAACoC,kBAAkB,CAACtD,KAAK,CAAC,CAAA;AACrE,OAAA;MAEA,IAAIA,KAAK,CAACkB,OAAO,CAACsC,cAAc,IAAI,CAACxD,KAAK,CAACuD,mBAAmB,EAAE;AAC9D,QAAA,OAAOvD,KAAK,CAACoD,qBAAqB,EAAE,CAAA;AACtC,OAAA;AAEA,MAAA,OAAOpD,KAAK,CAACuD,mBAAmB,EAAE,CAAA;KACnC,CAAA;GACF;AAEDE,EAAAA,SAAS,EAAEA,CACTC,GAAe,EACf1D,KAAmB,KACV;IACT0D,GAAG,CAACrC,YAAY,GAAG,MAAM,CAAC,CAACqC,GAAG,CAACC,gBAAgB,CAAA;AAC/CD,IAAAA,GAAG,CAACtC,gBAAgB,GAAGwC,QAAQ,IAAI;MACjC,IAAIF,GAAG,CAACG,oBAAoB,CAACC,cAAc,CAACF,QAAQ,CAAC,EAAE;AACrD,QAAA,OAAOF,GAAG,CAACG,oBAAoB,CAACD,QAAQ,CAAC,CAAA;AAC3C,OAAA;AAEA,MAAA,MAAMvD,MAAM,GAAGL,KAAK,CAAC+D,SAAS,CAACH,QAAQ,CAAC,CAAA;MAExC,IAAI,EAACvD,MAAM,IAANA,IAAAA,IAAAA,MAAM,CAAEW,SAAS,CAACI,gBAAgB,CAAE,EAAA;AACvC,QAAA,OAAOsC,GAAG,CAACjE,QAAQ,CAACmE,QAAQ,CAAC,CAAA;AAC/B,OAAA;AAEAF,MAAAA,GAAG,CAACG,oBAAoB,CAACD,QAAQ,CAAC,GAAGvD,MAAM,CAACW,SAAS,CAACI,gBAAgB,CACpEsC,GAAG,CAACM,QACN,CAAC,CAAA;AAED,MAAA,OAAON,GAAG,CAACG,oBAAoB,CAACD,QAAQ,CAAC,CAAA;KAC1C,CAAA;AACDF,IAAAA,GAAG,CAACG,oBAAoB,GAAG,EAAE,CAAA;GAC9B;EAEDI,UAAU,EAAEA,CACVC,IAAyB,EACzB7D,MAA6B,EAC7BqD,GAAe,EACf1D,KAAmB,KACV;AAITkE,IAAAA,IAAI,CAAC7C,YAAY,GAAG,MAClBhB,MAAM,CAACgB,YAAY,EAAE,IAAIhB,MAAM,CAACK,EAAE,KAAKgD,GAAG,CAACC,gBAAgB,CAAA;AAC7DO,IAAAA,IAAI,CAACC,gBAAgB,GAAG,MAAM,CAACD,IAAI,CAAC7C,YAAY,EAAE,IAAIhB,MAAM,CAACgB,YAAY,EAAE,CAAA;IAC3E6C,IAAI,CAACE,eAAe,GAAG,MAAA;AAAA,MAAA,IAAAC,YAAA,CAAA;MAAA,OACrB,CAACH,IAAI,CAAC7C,YAAY,EAAE,IAAI,CAAC6C,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAAC,EAAAE,CAAAA,YAAA,GAACX,GAAG,CAACY,OAAO,KAAA,IAAA,IAAXD,YAAA,CAAaE,MAAM,CAAA,CAAA;AAAA,KAAA,CAAA;AAC7E,GAAA;AACF,EAAC;AAEM,SAASC,YAAYA,CAC1BC,WAAqC,EACrC3E,QAAkB,EAClBK,iBAAsC,EACtC;EACA,IAAI,EAACL,QAAQ,IAARA,IAAAA,IAAAA,QAAQ,CAAEyE,MAAM,CAAA,IAAI,CAACpE,iBAAiB,EAAE;AAC3C,IAAA,OAAOsE,WAAW,CAAA;AACpB,GAAA;AAEA,EAAA,MAAMC,kBAAkB,GAAGD,WAAW,CAAC9D,MAAM,CAC3CgE,GAAG,IAAI,CAAC7E,QAAQ,CAACW,QAAQ,CAACkE,GAAG,CAACjE,EAAE,CAClC,CAAC,CAAA;EAED,IAAIP,iBAAiB,KAAK,QAAQ,EAAE;AAClC,IAAA,OAAOuE,kBAAkB,CAAA;AAC3B,GAAA;EAEA,MAAME,eAAe,GAAG9E,QAAQ,CAC7B+E,GAAG,CAACC,CAAC,IAAIL,WAAW,CAACM,IAAI,CAACJ,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAKoE,CAAC,CAAE,CAAC,CAChDnE,MAAM,CAACqE,OAAO,CAAC,CAAA;AAElB,EAAA,OAAO,CAAC,GAAGJ,eAAe,EAAE,GAAGF,kBAAkB,CAAC,CAAA;AACpD;;;;;"}