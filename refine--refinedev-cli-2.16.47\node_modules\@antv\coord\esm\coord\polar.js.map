{"version": 3, "file": "polar.js", "sourceRoot": "", "sources": ["../../src/coord/polar.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAE3C,OAAO,UAAyC,MAAM,QAAQ,CAAC;AAE/D;IAAmC,yBAAU;IAS3C,eAAY,GAAa;QAAzB,YACE,kBAAM,GAAG,CAAC,SASX;QAlBe,aAAO,GAAY,IAAI,CAAC;QACxB,UAAI,GAAW,OAAO,CAAC;QAU7B,IAAA,KAAqF,GAAG,WAA/D,EAAzB,UAAU,mBAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAA,EAAE,KAA0D,GAAG,SAAjC,EAA5B,QAAQ,mBAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,KAAA,EAAE,KAA4B,GAAG,YAAhB,EAAf,WAAW,mBAAG,CAAC,KAAA,EAAE,MAAM,GAAK,GAAG,OAAR,CAAS;QACjG,KAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,KAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,KAAI,CAAC,OAAO,EAAE,CAAC;;IACjB,CAAC;IAEM,uBAAO,GAAd;QACE,iBAAM,OAAO,WAAE,CAAC;QAEhB,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE;YACtC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;SAC9B;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEhC,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QAC3C,IAAM,SAAS,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QAE5C,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;QAC9C,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;QAE9C,IAAI,SAAiB,CAAC;QAEtB,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC,KAAK,GAAG,QAAQ,EAAE;YACnD,WAAW;YACX,SAAS,GAAG,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YAClC,IAAI,CAAC,YAAY,GAAG;gBAClB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK;gBAC5C,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;aACvD,CAAC;SACH;aAAM;YACL,YAAY;YACZ,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;YACpC,IAAI,CAAC,YAAY,GAAG;gBAClB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,QAAQ;gBACtD,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM;aAC7C,CAAC;SACH;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;SAC9B;aAAM,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;YAC9C,IAAI,CAAC,WAAW,GAAG,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;SAC5C;aAAM,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,EAAE;YACtD,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;SAC9B;QAED,IAAI,CAAC,CAAC,GAAG;YACP,KAAK,EAAE,IAAI,CAAC,UAAU;YACtB,GAAG,EAAE,IAAI,CAAC,QAAQ;SACnB,CAAC;QAEF,IAAI,CAAC,CAAC,GAAG;YACP,KAAK,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW;YAC1C,GAAG,EAAE,IAAI,CAAC,WAAW;SACtB,CAAC;IACJ,CAAC;IAEM,yBAAS,GAAhB;QACE,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAEM,4BAAY,GAAnB,UAAoB,KAAY;;QAC9B,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE1B,IAAA,CAAC,GAAQ,KAAK,EAAb,EAAE,CAAC,GAAK,KAAK,EAAV,CAAW;QAErB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,KAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAd,CAAC,QAAA,EAAE,CAAC,QAAA,CAAW;SACjB;QAED,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC5B,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAE5B,OAAO;YACL,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAC7B,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;SAC9B,CAAC;IACJ,CAAC;IAEM,2BAAW,GAAlB,UAAmB,KAAY;;QAC7B,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAM,MAAM,GAAY,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAE7D,IAAA,KAA2B,IAAI,EAA7B,UAAU,gBAAA,EAAE,QAAQ,cAAS,CAAC;QACpC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvB,KAAyB,CAAC,QAAQ,EAAE,UAAU,CAAC,EAA9C,UAAU,QAAA,EAAE,QAAQ,QAAA,CAA2B;SACjD;QAED,IAAM,CAAC,GAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/C,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;QAEjC,IAAM,OAAO,GAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QACxC,IAAM,OAAO,GAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,IAAI,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,GAAG,UAAU,CAAC,CAAC;QAChE,IAAI,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;YACrC,KAAK,GAAG,CAAC,CAAC;SACX;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEnC,IAAI,QAAQ,GAAG,KAAK,GAAG,CAAC,QAAQ,GAAG,UAAU,CAAC,CAAC;QAC/C,QAAQ,GAAG,QAAQ,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAE5D,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC7C,IAAM,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;QAChD,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;QAChD,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,yBAAS,GAAhB;QACE,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAEO,yBAAS,GAAjB;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,UAAU,CAAC,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE;YAClD,OAAO;gBACL,IAAI,EAAE,CAAC,CAAC;gBACR,IAAI,EAAE,CAAC;gBACP,IAAI,EAAE,CAAC,CAAC;gBACR,IAAI,EAAE,CAAC;aACR,CAAC;SACH;QACD,IAAM,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QACzD,IAAM,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEzD,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;YAClG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACtB;QAED,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,EAAE,CAAC;YACrB,IAAI,EAAE,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,EAAE,CAAC;YACrB,IAAI,EAAE,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,EAAE,CAAC;YACrB,IAAI,EAAE,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,EAAE,CAAC;SACtB,CAAC;IACJ,CAAC;IACH,YAAC;AAAD,CAAC,AA7JD,CAAmC,UAAU,GA6J5C"}