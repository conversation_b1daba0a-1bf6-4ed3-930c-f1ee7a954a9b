{"version": 3, "file": "columnHelper.js", "sources": ["../../src/columnHelper.ts"], "sourcesContent": ["import {\n  AccessorFn,\n  AccessorFnColumnDef,\n  AccessorKeyColumnDef,\n  DisplayColumnDef,\n  GroupColumnDef,\n  IdentifiedColumnDef,\n  RowData,\n} from './types'\nimport { DeepKeys, DeepValue } from './utils'\n\n// type Person = {\n//   firstName: string\n//   lastName: string\n//   age: number\n//   visits: number\n//   status: string\n//   progress: number\n//   createdAt: Date\n//   nested: {\n//     foo: [\n//       {\n//         bar: 'bar'\n//       }\n//     ]\n//     bar: { subBar: boolean }[]\n//     baz: {\n//       foo: 'foo'\n//       bar: {\n//         baz: 'baz'\n//       }\n//     }\n//   }\n// }\n\n// const test: DeepKeys<Person> = 'nested.foo.0.bar'\n// const test2: DeepKeys<Person> = 'nested.bar'\n\n// const helper = createColumnHelper<Person>()\n\n// helper.accessor('nested.foo', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.foo.0.bar', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.bar', {\n//   cell: info => info.getValue(),\n// })\n\nexport type ColumnHelper<TData extends RowData> = {\n  accessor: <\n    TAccessor extends AccessorFn<TData> | DeepKeys<TData>,\n    TValue extends TAccessor extends AccessorFn<TData, infer TReturn>\n      ? TReturn\n      : TAccessor extends DeepKeys<TData>\n        ? DeepValue<TData, TAccessor>\n        : never,\n  >(\n    accessor: TAccessor,\n    column: TAccessor extends AccessorFn<TData>\n      ? DisplayColumnDef<TData, TValue>\n      : IdentifiedColumnDef<TData, TValue>\n  ) => TAccessor extends AccessorFn<TData>\n    ? AccessorFnColumnDef<TData, TValue>\n    : AccessorKeyColumnDef<TData, TValue>\n  display: (column: DisplayColumnDef<TData>) => DisplayColumnDef<TData, unknown>\n  group: (column: GroupColumnDef<TData>) => GroupColumnDef<TData, unknown>\n}\n\nexport function createColumnHelper<\n  TData extends RowData,\n>(): ColumnHelper<TData> {\n  return {\n    accessor: (accessor, column) => {\n      return typeof accessor === 'function'\n        ? ({\n            ...column,\n            accessorFn: accessor,\n          } as any)\n        : {\n            ...column,\n            accessorKey: accessor,\n          }\n    },\n    display: column => column,\n    group: column => column,\n  }\n}\n"], "names": ["createColumnHelper", "accessor", "column", "accessorFn", "accessorKey", "display", "group"], "mappings": ";;;;;;;;;;;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAsBO,SAASA,kBAAkBA,GAET;EACvB,OAAO;AACLC,IAAAA,QAAQ,EAAEA,CAACA,QAAQ,EAAEC,MAAM,KAAK;AAC9B,MAAA,OAAO,OAAOD,QAAQ,KAAK,UAAU,GAChC;AACC,QAAA,GAAGC,MAAM;AACTC,QAAAA,UAAU,EAAEF,QAAAA;AACd,OAAC,GACD;AACE,QAAA,GAAGC,MAAM;AACTE,QAAAA,WAAW,EAAEH,QAAAA;OACd,CAAA;KACN;IACDI,OAAO,EAAEH,MAAM,IAAIA,MAAM;IACzBI,KAAK,EAAEJ,MAAM,IAAIA,MAAAA;GAClB,CAAA;AACH;;;;"}