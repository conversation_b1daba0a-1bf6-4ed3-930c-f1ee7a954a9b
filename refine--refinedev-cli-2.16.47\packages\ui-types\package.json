{"name": "@refinedev/ui-types", "version": "1.24.2", "private": false, "description": "Type definitions package for Refine UI components, enhancing TypeScript support.", "license": "MIT", "author": "refine", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "typings": "dist/index.d.ts", "scripts": {"attw": "attw --pack .", "build": "tsup && node ../shared/generate-declarations.js", "dev": "tsup --watch", "prepare": "pnpm build", "publint": "publint --strict=true --level=suggestion", "test": "echo \"no tests\"", "types": "node ../shared/generate-declarations.js"}, "dependencies": {"@refinedev/core": "^4.57.9", "dayjs": "^1.10.7", "tslib": "^2.6.2"}, "devDependencies": {"@esbuild-plugins/node-resolve": "^0.1.4", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "tsup": "^6.7.0", "typescript": "^5.4.2"}, "peerDependencies": {"@refinedev/core": "^4.46.1", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "publishConfig": {"access": "public"}}