{"name": "@antv/coord", "version": "0.3.1", "description": "The coordinate module for G2", "author": "https://github.com/orgs/antvis/people", "license": "MIT", "main": "lib/index.js", "module": "esm/index.js", "types": "lib/index.d.ts", "files": ["src", "lib", "esm"], "repository": {"type": "git", "url": "**************:antvis/coord.git"}, "scripts": {"build": "run-s clean lib", "clean": "<PERSON><PERSON>f lib esm", "lib": "run-p lib:*", "lib:cjs": "tsc -p tsconfig.json --target ES5 --module commonjs --outDir lib", "lib:esm": "tsc -p tsconfig.json --target ES5 --module ESNext --outDir esm", "lint": "prettier ./src ./__tests__ --check", "fix": "prettier ./src ./__tests__ --write", "lint-staged": "lint-staged", "test": "jest", "test-live": "DEBUG_MODE=1 jest", "ci": "run-s lint build test"}, "husky": {"hooks": {"pre-commit": "run-s lint-staged test build"}}, "devDependencies": {"@types/jest": "^26.0.20", "husky": "^5.1.0", "jest": "^26.6.3", "lint-staged": "^10.5.4", "npm-run-all": "^4.1.5", "prettier": "^2.2.1", "rimraf": "^3.0.2", "ts-jest": "^26.5.1", "typescript": "^4.1.5"}, "dependencies": {"@antv/util": "~2.0.12", "@antv/matrix-util": "^3.1.0-beta.2", "tslib": "^2.1.0"}, "lint-staged": {"*.{js,css,json,md,ts}": ["prettier --write", "git add"]}, "jest": {"preset": "ts-jest", "collectCoverage": true, "collectCoverageFrom": ["src/**/*.{ts,js}", "!**/node_modules/**", "!**/vendor/**"], "testRegex": "/__tests__/.*spec\\.ts?$"}}