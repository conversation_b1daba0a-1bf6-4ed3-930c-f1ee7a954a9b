{"name": "@refinedev/mantine", "version": "2.36.2", "private": false, "description": "refine is a React-based framework for building internal tools, rapidly.", "repository": {"type": "git", "url": "https://github.com/refinedev/refine.git", "directory": "packages/mantine"}, "license": "MIT", "author": "refine", "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "typings": "dist/index.d.ts", "scripts": {"attw": "attw --pack .", "build": "tsup && node ../shared/generate-declarations.js", "dev": "tsup --watch", "prepare": "pnpm build", "publint": "publint --strict=true --level=suggestion", "test": "jest --passWithNoTests --runInBand", "types": "node ../shared/generate-declarations.js"}, "dependencies": {"@emotion/react": "^11.8.2", "@mantine/core": "^5.10.4", "@mantine/form": "^5.10.4", "@mantine/hooks": "^5.10.4", "@mantine/notifications": "^5.10.4", "@refinedev/ui-types": "^1.24.2", "@tabler/icons-react": "^3.1.0", "@tanstack/react-query": "^4.10.1", "dayjs": "^1.10.7", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "react-markdown": "^6.0.1", "remark-gfm": "^1.0.0", "tslib": "^2.6.2"}, "devDependencies": {"@esbuild-plugins/node-resolve": "^0.1.4", "@refinedev/cli": "^2.16.46", "@refinedev/core": "^4.57.10", "@refinedev/ui-tests": "^1.15.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.1.1", "@testing-library/react-hooks": "^8.0.0", "@testing-library/user-event": "^14.1.1", "@types/jest": "^29.2.4", "@types/lodash": "^4.14.171", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/testing-library__jest-dom": "^5.14.3", "esbuild-copy-static-files": "^0.1.0", "esbuild-plugin-inline-image": "^0.0.8", "identity-obj-proxy": "^3.0.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "react-router": "^7.0.2", "ts-jest": "^29.1.2", "tsup": "^6.7.0", "typescript": "^5.4.2"}, "peerDependencies": {"@emotion/react": "^11.8.2", "@mantine/core": "^5.10.4", "@mantine/form": "^5.10.4", "@mantine/hooks": "^5.10.4", "@mantine/notifications": "^5.10.4", "@refinedev/cli": "^2.0.0", "@refinedev/core": "^4.46.1", "@types/react": "^17.0.0 || ^18.0.0", "@types/react-dom": "^17.0.0 || ^18.0.0", "dayjs": "^1.10.7", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "publishConfig": {"access": "public"}}