import nock from "nock";

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", offset: "0", limit: "10" })
  .reply(
    206,
    [
      {
        id: 1,
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.\n\nPraesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.",
        categoryId: 7,
        images: null,
      },
      {
        id: 2,
        title: "Great Plains Flatsedge",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        images: null,
      },
      {
        id: 3,
        title: "Copperweed",
        slug: "8d9116f0-ee0e-48ec-8d76-c056d041820f",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Morbi non lectus. Aliquam sit amet diam in magna bibendum imperdiet. Nullam orci pede, venenatis non, sodales sed, tincidunt eu, felis.",
        categoryId: 1,
        images: null,
      },
      {
        id: 4,
        title: "Bastard Toadflax",
        slug: "ac41beb1-762d-43d2-a857-56d985812259",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Aenean fermentum. Donec ut mauris eget massa tempor convallis. Nulla neque libero, convallis eget, eleifend luctus, ultricies eu, nibh.\n\nQuisque id justo sit amet sapien dignissim vestibulum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Nulla dapibus dolor vel est. Donec odio justo, sollicitudin ut, suscipit a, feugiat et, eros.",
        categoryId: 6,
        images: null,
      },
      {
        id: 5,
        title: "Cottony Goldenaster",
        slug: "6ca3651b-ce0b-45c7-9bd3-ece235142da5",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Proin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.\n\nAenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.",
        categoryId: 6,
        images: null,
      },
      {
        id: 6,
        title: "Walter's Sedge",
        slug: "61a985f0-5078-49de-8ed8-23018c5381d6",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Cras mi pede, malesuada in, imperdiet et, commodo vulputate, justo. In blandit ultrices enim. Lorem ipsum dolor sit amet, consectetuer adipiscing elit.",
        categoryId: 1,
        images: null,
      },
      {
        id: 7,
        title: "Sickle Island Spleenwort",
        slug: "e4ab8737-a719-4fc1-900c-f72f2d0d7700",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.\n\nFusce consequat. Nulla nisl. Nunc nisl.",
        categoryId: 3,
        images: null,
      },
      {
        id: 8,
        title: "Oakwoods Prairie Clover",
        slug: "41ed56fc-7644-475a-9c83-ea50e1b9fc76",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Nam ultrices, libero non mattis pulvinar, nulla pede ullamcorper augue, a suscipit nulla elit ac nulla. Sed vel enim sit amet nunc viverra dapibus. Nulla suscipit ligula in lacus.",
        categoryId: 5,
        images: null,
      },
      {
        id: 9,
        title: "Basil Mountainmint",
        slug: "a9047056-a04f-4185-82c6-c7a140f0457c",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Fusce consequat. Nulla nisl. Nunc nisl.\n\nDuis bibendum, felis sed interdum venenatis, turpis enim blandit mi, in porttitor pede justo eu massa. Donec dapibus. Duis at velit eu est congue elementum.\n\nIn hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.",
        categoryId: 13,
        images: null,
      },
      {
        id: 10,
        title: "Resindot Sunflower",
        slug: "748e3d81-8bf8-4f78-b3ac-331bd8fb51c1",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Vestibulum quam sapien, varius ut, blandit non, interdum in, ante. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Duis faucibus accumsan odio. Curabitur convallis.\n\nDuis consequat dui nec nisi volutpat eleifend. Donec ut dolor. Morbi vel lectus in quam fringilla rhoncus.",
        categoryId: 1,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:57 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-9/20",
      "CF-Ray",
      "879c9b95ef71693c-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?limit=10&offset=0&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "3",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "title", offset: "0", limit: "10" })
  .reply(
    206,
    [
      { title: "Black Psorotichia Lichen" },
      { title: "Great Plains Flatsedge" },
      { title: "Copperweed" },
      { title: "Bastard Toadflax" },
      { title: "Cottony Goldenaster" },
      { title: "Walter's Sedge" },
      { title: "Sickle Island Spleenwort" },
      { title: "Oakwoods Prairie Clover" },
      { title: "Basil Mountainmint" },
      { title: "Resindot Sunflower" },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:57 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-9/20",
      "CF-Ray",
      "879c9b97093650b8-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?limit=10&offset=0&select=title",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "2",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", offset: "0", limit: "10" })
  .reply(
    206,
    [
      {
        id: 1,
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.\n\nPraesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.",
        categoryId: 7,
        images: null,
      },
      {
        id: 2,
        title: "Great Plains Flatsedge",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        images: null,
      },
      {
        id: 3,
        title: "Copperweed",
        slug: "8d9116f0-ee0e-48ec-8d76-c056d041820f",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Morbi non lectus. Aliquam sit amet diam in magna bibendum imperdiet. Nullam orci pede, venenatis non, sodales sed, tincidunt eu, felis.",
        categoryId: 1,
        images: null,
      },
      {
        id: 4,
        title: "Bastard Toadflax",
        slug: "ac41beb1-762d-43d2-a857-56d985812259",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Aenean fermentum. Donec ut mauris eget massa tempor convallis. Nulla neque libero, convallis eget, eleifend luctus, ultricies eu, nibh.\n\nQuisque id justo sit amet sapien dignissim vestibulum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Nulla dapibus dolor vel est. Donec odio justo, sollicitudin ut, suscipit a, feugiat et, eros.",
        categoryId: 6,
        images: null,
      },
      {
        id: 5,
        title: "Cottony Goldenaster",
        slug: "6ca3651b-ce0b-45c7-9bd3-ece235142da5",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Proin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.\n\nAenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.",
        categoryId: 6,
        images: null,
      },
      {
        id: 6,
        title: "Walter's Sedge",
        slug: "61a985f0-5078-49de-8ed8-23018c5381d6",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Cras mi pede, malesuada in, imperdiet et, commodo vulputate, justo. In blandit ultrices enim. Lorem ipsum dolor sit amet, consectetuer adipiscing elit.",
        categoryId: 1,
        images: null,
      },
      {
        id: 7,
        title: "Sickle Island Spleenwort",
        slug: "e4ab8737-a719-4fc1-900c-f72f2d0d7700",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.\n\nFusce consequat. Nulla nisl. Nunc nisl.",
        categoryId: 3,
        images: null,
      },
      {
        id: 8,
        title: "Oakwoods Prairie Clover",
        slug: "41ed56fc-7644-475a-9c83-ea50e1b9fc76",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Nam ultrices, libero non mattis pulvinar, nulla pede ullamcorper augue, a suscipit nulla elit ac nulla. Sed vel enim sit amet nunc viverra dapibus. Nulla suscipit ligula in lacus.",
        categoryId: 5,
        images: null,
      },
      {
        id: 9,
        title: "Basil Mountainmint",
        slug: "a9047056-a04f-4185-82c6-c7a140f0457c",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Fusce consequat. Nulla nisl. Nunc nisl.\n\nDuis bibendum, felis sed interdum venenatis, turpis enim blandit mi, in porttitor pede justo eu massa. Donec dapibus. Duis at velit eu est congue elementum.\n\nIn hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.",
        categoryId: 13,
        images: null,
      },
      {
        id: 10,
        title: "Resindot Sunflower",
        slug: "748e3d81-8bf8-4f78-b3ac-331bd8fb51c1",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Vestibulum quam sapien, varius ut, blandit non, interdum in, ante. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Duis faucibus accumsan odio. Curabitur convallis.\n\nDuis consequat dui nec nisi volutpat eleifend. Donec ut dolor. Morbi vel lectus in quam fringilla rhoncus.",
        categoryId: 1,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:57 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-9/20",
      "CF-Ray",
      "879c9b97d8085184-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?limit=10&offset=0&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "3",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", offset: "0", limit: "10", order: "title.asc" })
  .reply(
    206,
    [
      {
        id: 16,
        title: "Anopteris",
        slug: "7fd46aeb-1df9-49f3-88e0-5c790a8dab57",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Phasellus in felis. Donec semper sapien a libero. Nam dui.\n\nProin leo odio, porttitor id, consequat in, consequat ut, nulla. Sed accumsan felis. Ut at dolor quis odio consequat varius.\n\nInteger ac leo. Pellentesque ultrices mattis odio. Donec vitae nisi.",
        categoryId: 8,
        images: null,
      },
      {
        id: 9,
        title: "Basil Mountainmint",
        slug: "a9047056-a04f-4185-82c6-c7a140f0457c",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Fusce consequat. Nulla nisl. Nunc nisl.\n\nDuis bibendum, felis sed interdum venenatis, turpis enim blandit mi, in porttitor pede justo eu massa. Donec dapibus. Duis at velit eu est congue elementum.\n\nIn hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.",
        categoryId: 13,
        images: null,
      },
      {
        id: 4,
        title: "Bastard Toadflax",
        slug: "ac41beb1-762d-43d2-a857-56d985812259",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Aenean fermentum. Donec ut mauris eget massa tempor convallis. Nulla neque libero, convallis eget, eleifend luctus, ultricies eu, nibh.\n\nQuisque id justo sit amet sapien dignissim vestibulum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Nulla dapibus dolor vel est. Donec odio justo, sollicitudin ut, suscipit a, feugiat et, eros.",
        categoryId: 6,
        images: null,
      },
      {
        id: 1,
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.\n\nPraesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.",
        categoryId: 7,
        images: null,
      },
      {
        id: 19,
        title: "Bloodroot",
        slug: "95b4bc11-522b-4eb6-ae1f-d0d1d988d7ad",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content: "In congue. Etiam justo. Etiam pretium iaculis justo.",
        categoryId: 14,
        images: null,
      },
      {
        id: 17,
        title: "Changing Forget-me-not",
        slug: "7d8f66da-4e16-46df-bd0e-7f046e0334f7",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Nullam sit amet turpis elementum ligula vehicula consequat. Morbi a ipsum. Integer a nibh.\n\nIn quis justo. Maecenas rhoncus aliquam lacus. Morbi quis tortor id nulla ultrices aliquet.\n\nMaecenas leo odio, condimentum id, luctus nec, molestie sed, justo. Pellentesque viverra pede ac diam. Cras pellentesque volutpat dui.",
        categoryId: 7,
        images: null,
      },
      {
        id: 18,
        title: "Common Motherwort",
        slug: "2d82881a-fe3f-4ff0-bf08-3b1021fa4a75",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Morbi non lectus. Aliquam sit amet diam in magna bibendum imperdiet. Nullam orci pede, venenatis non, sodales sed, tincidunt eu, felis.",
        categoryId: 14,
        images: null,
      },
      {
        id: 3,
        title: "Copperweed",
        slug: "8d9116f0-ee0e-48ec-8d76-c056d041820f",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Morbi non lectus. Aliquam sit amet diam in magna bibendum imperdiet. Nullam orci pede, venenatis non, sodales sed, tincidunt eu, felis.",
        categoryId: 1,
        images: null,
      },
      {
        id: 5,
        title: "Cottony Goldenaster",
        slug: "6ca3651b-ce0b-45c7-9bd3-ece235142da5",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Proin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.\n\nAenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.",
        categoryId: 6,
        images: null,
      },
      {
        id: 13,
        title: "Dust Lichen",
        slug: "ca038c02-0c6f-417b-aaf8-6f51b777d1f5",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Fusce posuere felis sed lacus. Morbi sem mauris, laoreet ut, rhoncus aliquet, pulvinar sed, nisl. Nunc rhoncus dui vel sem.\n\nSed sagittis. Nam congue, risus semper porta volutpat, quam pede lobortis ligula, sit amet eleifend pede libero quis orci. Nullam molestie nibh in lectus.",
        categoryId: 7,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:57 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-9/20",
      "CF-Ray",
      "879c9b98fb7b514d-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?limit=10&offset=0&order=title.asc&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "2",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({
    select: "%2A",
    offset: "0",
    limit: "10",
    title: "eq.Basil%20Mountainmint",
  })
  .reply(
    200,
    [
      {
        id: 9,
        title: "Basil Mountainmint",
        slug: "a9047056-a04f-4185-82c6-c7a140f0457c",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Fusce consequat. Nulla nisl. Nunc nisl.\n\nDuis bibendum, felis sed interdum venenatis, turpis enim blandit mi, in porttitor pede justo eu massa. Donec dapibus. Duis at velit eu est congue elementum.\n\nIn hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.",
        categoryId: 13,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:57 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-0/1",
      "CF-Ray",
      "879c9b9a0db750ac-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?limit=10&offset=0&select=%2A&title=eq.Basil%20Mountainmint",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "2",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({
    select: "%2A",
    offset: "0",
    limit: "10",
    title: "neq.Basil%20Mountainmint",
  })
  .reply(
    206,
    [
      {
        id: 1,
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.\n\nPraesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.",
        categoryId: 7,
        images: null,
      },
      {
        id: 2,
        title: "Great Plains Flatsedge",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        images: null,
      },
      {
        id: 3,
        title: "Copperweed",
        slug: "8d9116f0-ee0e-48ec-8d76-c056d041820f",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Morbi non lectus. Aliquam sit amet diam in magna bibendum imperdiet. Nullam orci pede, venenatis non, sodales sed, tincidunt eu, felis.",
        categoryId: 1,
        images: null,
      },
      {
        id: 4,
        title: "Bastard Toadflax",
        slug: "ac41beb1-762d-43d2-a857-56d985812259",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Aenean fermentum. Donec ut mauris eget massa tempor convallis. Nulla neque libero, convallis eget, eleifend luctus, ultricies eu, nibh.\n\nQuisque id justo sit amet sapien dignissim vestibulum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Nulla dapibus dolor vel est. Donec odio justo, sollicitudin ut, suscipit a, feugiat et, eros.",
        categoryId: 6,
        images: null,
      },
      {
        id: 5,
        title: "Cottony Goldenaster",
        slug: "6ca3651b-ce0b-45c7-9bd3-ece235142da5",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Proin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.\n\nAenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.",
        categoryId: 6,
        images: null,
      },
      {
        id: 6,
        title: "Walter's Sedge",
        slug: "61a985f0-5078-49de-8ed8-23018c5381d6",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Cras mi pede, malesuada in, imperdiet et, commodo vulputate, justo. In blandit ultrices enim. Lorem ipsum dolor sit amet, consectetuer adipiscing elit.",
        categoryId: 1,
        images: null,
      },
      {
        id: 7,
        title: "Sickle Island Spleenwort",
        slug: "e4ab8737-a719-4fc1-900c-f72f2d0d7700",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.\n\nFusce consequat. Nulla nisl. Nunc nisl.",
        categoryId: 3,
        images: null,
      },
      {
        id: 8,
        title: "Oakwoods Prairie Clover",
        slug: "41ed56fc-7644-475a-9c83-ea50e1b9fc76",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Nam ultrices, libero non mattis pulvinar, nulla pede ullamcorper augue, a suscipit nulla elit ac nulla. Sed vel enim sit amet nunc viverra dapibus. Nulla suscipit ligula in lacus.",
        categoryId: 5,
        images: null,
      },
      {
        id: 10,
        title: "Resindot Sunflower",
        slug: "748e3d81-8bf8-4f78-b3ac-331bd8fb51c1",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Vestibulum quam sapien, varius ut, blandit non, interdum in, ante. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Duis faucibus accumsan odio. Curabitur convallis.\n\nDuis consequat dui nec nisi volutpat eleifend. Donec ut dolor. Morbi vel lectus in quam fringilla rhoncus.",
        categoryId: 1,
        images: null,
      },
      {
        id: 11,
        title: "Funck's Wart Lichen",
        slug: "9d5b7d39-2c6e-40c3-ad4e-9cc46eabe1fb",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Proin risus. Praesent lectus.\n\nVestibulum quam sapien, varius ut, blandit non, interdum in, ante. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Duis faucibus accumsan odio. Curabitur convallis.",
        categoryId: 8,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:57 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-9/19",
      "CF-Ray",
      "879c9b9aeb997790-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?limit=10&offset=0&select=%2A&title=neq.Basil%20Mountainmint",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "2",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", offset: "0", limit: "10", id: "lt.3" })
  .reply(
    200,
    [
      {
        id: 1,
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.\n\nPraesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.",
        categoryId: 7,
        images: null,
      },
      {
        id: 2,
        title: "Great Plains Flatsedge",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:58 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-1/2",
      "CF-Ray",
      "879c9b9c0b017252-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?id=lt.3&limit=10&offset=0&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "3",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", offset: "0", limit: "10", id: "gt.3" })
  .reply(
    206,
    [
      {
        id: 4,
        title: "Bastard Toadflax",
        slug: "ac41beb1-762d-43d2-a857-56d985812259",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Aenean fermentum. Donec ut mauris eget massa tempor convallis. Nulla neque libero, convallis eget, eleifend luctus, ultricies eu, nibh.\n\nQuisque id justo sit amet sapien dignissim vestibulum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Nulla dapibus dolor vel est. Donec odio justo, sollicitudin ut, suscipit a, feugiat et, eros.",
        categoryId: 6,
        images: null,
      },
      {
        id: 5,
        title: "Cottony Goldenaster",
        slug: "6ca3651b-ce0b-45c7-9bd3-ece235142da5",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Proin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.\n\nAenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.",
        categoryId: 6,
        images: null,
      },
      {
        id: 6,
        title: "Walter's Sedge",
        slug: "61a985f0-5078-49de-8ed8-23018c5381d6",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Cras mi pede, malesuada in, imperdiet et, commodo vulputate, justo. In blandit ultrices enim. Lorem ipsum dolor sit amet, consectetuer adipiscing elit.",
        categoryId: 1,
        images: null,
      },
      {
        id: 7,
        title: "Sickle Island Spleenwort",
        slug: "e4ab8737-a719-4fc1-900c-f72f2d0d7700",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.\n\nFusce consequat. Nulla nisl. Nunc nisl.",
        categoryId: 3,
        images: null,
      },
      {
        id: 8,
        title: "Oakwoods Prairie Clover",
        slug: "41ed56fc-7644-475a-9c83-ea50e1b9fc76",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Nam ultrices, libero non mattis pulvinar, nulla pede ullamcorper augue, a suscipit nulla elit ac nulla. Sed vel enim sit amet nunc viverra dapibus. Nulla suscipit ligula in lacus.",
        categoryId: 5,
        images: null,
      },
      {
        id: 9,
        title: "Basil Mountainmint",
        slug: "a9047056-a04f-4185-82c6-c7a140f0457c",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Fusce consequat. Nulla nisl. Nunc nisl.\n\nDuis bibendum, felis sed interdum venenatis, turpis enim blandit mi, in porttitor pede justo eu massa. Donec dapibus. Duis at velit eu est congue elementum.\n\nIn hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.",
        categoryId: 13,
        images: null,
      },
      {
        id: 10,
        title: "Resindot Sunflower",
        slug: "748e3d81-8bf8-4f78-b3ac-331bd8fb51c1",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Vestibulum quam sapien, varius ut, blandit non, interdum in, ante. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Duis faucibus accumsan odio. Curabitur convallis.\n\nDuis consequat dui nec nisi volutpat eleifend. Donec ut dolor. Morbi vel lectus in quam fringilla rhoncus.",
        categoryId: 1,
        images: null,
      },
      {
        id: 11,
        title: "Funck's Wart Lichen",
        slug: "9d5b7d39-2c6e-40c3-ad4e-9cc46eabe1fb",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Proin risus. Praesent lectus.\n\nVestibulum quam sapien, varius ut, blandit non, interdum in, ante. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Duis faucibus accumsan odio. Curabitur convallis.",
        categoryId: 8,
        images: null,
      },
      {
        id: 12,
        title: "Mattaponi Quillwort",
        slug: "814fecb1-261e-4633-9f84-2df699867517",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Quisque id justo sit amet sapien dignissim vestibulum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Nulla dapibus dolor vel est. Donec odio justo, sollicitudin ut, suscipit a, feugiat et, eros.\n\nVestibulum ac est lacinia nisi venenatis tristique. Fusce congue, diam id ornare imperdiet, sapien urna pretium nisl, ut volutpat sapien arcu sed augue. Aliquam erat volutpat.\n\nIn congue. Etiam justo. Etiam pretium iaculis justo.",
        categoryId: 11,
        images: null,
      },
      {
        id: 13,
        title: "Dust Lichen",
        slug: "ca038c02-0c6f-417b-aaf8-6f51b777d1f5",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Fusce posuere felis sed lacus. Morbi sem mauris, laoreet ut, rhoncus aliquet, pulvinar sed, nisl. Nunc rhoncus dui vel sem.\n\nSed sagittis. Nam congue, risus semper porta volutpat, quam pede lobortis ligula, sit amet eleifend pede libero quis orci. Nullam molestie nibh in lectus.",
        categoryId: 7,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:58 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-9/17",
      "CF-Ray",
      "879c9b9ceae751a8-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?id=gt.3&limit=10&offset=0&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "3",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", offset: "0", limit: "10", id: "lte.2" })
  .reply(
    200,
    [
      {
        id: 1,
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.\n\nPraesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.",
        categoryId: 7,
        images: null,
      },
      {
        id: 2,
        title: "Great Plains Flatsedge",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:58 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-1/2",
      "CF-Ray",
      "879c9b9debe5724f-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?id=lte.2&limit=10&offset=0&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "5",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", offset: "0", limit: "10", id: "gte.20" })
  .reply(
    200,
    [
      {
        id: 20,
        title: "Monochoria",
        slug: "4d15fbde-6efa-499f-8254-d43de80cb945",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Curabitur at ipsum ac tellus semper interdum. Mauris ullamcorper purus sit amet nulla. Quisque arcu libero, rutrum ac, lobortis vel, dapibus at, diam.",
        categoryId: 8,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:58 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-0/1",
      "CF-Ray",
      "879c9b9edd90720c-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?id=gte.20&limit=10&offset=0&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "2",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", offset: "0", limit: "10", id: "in.%282%2C3%29" })
  .reply(
    200,
    [
      {
        id: 2,
        title: "Great Plains Flatsedge",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        images: null,
      },
      {
        id: 3,
        title: "Copperweed",
        slug: "8d9116f0-ee0e-48ec-8d76-c056d041820f",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Morbi non lectus. Aliquam sit amet diam in magna bibendum imperdiet. Nullam orci pede, venenatis non, sodales sed, tincidunt eu, felis.",
        categoryId: 1,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:58 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-1/2",
      "CF-Ray",
      "879c9b9fba41722b-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?id=in.%282%2C3%29&limit=10&offset=0&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "2",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({
    select: "%2A",
    offset: "0",
    limit: "10",
    title: "ilike.%25Basil%25",
  })
  .reply(
    200,
    [
      {
        id: 9,
        title: "Basil Mountainmint",
        slug: "a9047056-a04f-4185-82c6-c7a140f0457c",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Fusce consequat. Nulla nisl. Nunc nisl.\n\nDuis bibendum, felis sed interdum venenatis, turpis enim blandit mi, in porttitor pede justo eu massa. Donec dapibus. Duis at velit eu est congue elementum.\n\nIn hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.",
        categoryId: 13,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:58 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-0/1",
      "CF-Ray",
      "879c9ba0bffd6960-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?limit=10&offset=0&select=%2A&title=ilike.%25Basil%25",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "2",
      "x-kong-upstream-latency",
      "2",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", offset: "0", limit: "10", title: "like.%25Basil%25" })
  .reply(
    200,
    [
      {
        id: 9,
        title: "Basil Mountainmint",
        slug: "a9047056-a04f-4185-82c6-c7a140f0457c",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Fusce consequat. Nulla nisl. Nunc nisl.\n\nDuis bibendum, felis sed interdum venenatis, turpis enim blandit mi, in porttitor pede justo eu massa. Donec dapibus. Duis at velit eu est congue elementum.\n\nIn hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.",
        categoryId: 13,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:58 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-0/1",
      "CF-Ray",
      "879c9ba1bc5d7240-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?limit=10&offset=0&select=%2A&title=like.%25Basil%25",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "2",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", offset: "0", limit: "10", content: "is.null" })
  .reply(
    200,
    [],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:59 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "*/0",
      "CF-Ray",
      "879c9ba288a0515f-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?content=is.null&limit=10&offset=0&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "2",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({
    select: "%2A",
    offset: "0",
    limit: "10",
    or: "%28title.eq.Dust%20Lichen%2Ctitle.eq.Black%20Psorotichia%20Lichen%29",
  })
  .reply(
    200,
    [
      {
        id: 1,
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.\n\nPraesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.",
        categoryId: 7,
        images: null,
      },
      {
        id: 13,
        title: "Dust Lichen",
        slug: "ca038c02-0c6f-417b-aaf8-6f51b777d1f5",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Fusce posuere felis sed lacus. Morbi sem mauris, laoreet ut, rhoncus aliquet, pulvinar sed, nisl. Nunc rhoncus dui vel sem.\n\nSed sagittis. Nam congue, risus semper porta volutpat, quam pede lobortis ligula, sit amet eleifend pede libero quis orci. Nullam molestie nibh in lectus.",
        categoryId: 7,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:59 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-1/2",
      "CF-Ray",
      "879c9ba35d5b68ae-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?limit=10&offset=0&or=%28title.eq.Dust%20Lichen%2Ctitle.eq.Black%20Psorotichia%20Lichen%29&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "3",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", offset: "0", limit: "10" })
  .reply(
    206,
    [
      {
        id: 1,
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.\n\nPraesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.",
        categoryId: 7,
        images: null,
      },
      {
        id: 2,
        title: "Great Plains Flatsedge",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        images: null,
      },
      {
        id: 3,
        title: "Copperweed",
        slug: "8d9116f0-ee0e-48ec-8d76-c056d041820f",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Morbi non lectus. Aliquam sit amet diam in magna bibendum imperdiet. Nullam orci pede, venenatis non, sodales sed, tincidunt eu, felis.",
        categoryId: 1,
        images: null,
      },
      {
        id: 4,
        title: "Bastard Toadflax",
        slug: "ac41beb1-762d-43d2-a857-56d985812259",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Aenean fermentum. Donec ut mauris eget massa tempor convallis. Nulla neque libero, convallis eget, eleifend luctus, ultricies eu, nibh.\n\nQuisque id justo sit amet sapien dignissim vestibulum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Nulla dapibus dolor vel est. Donec odio justo, sollicitudin ut, suscipit a, feugiat et, eros.",
        categoryId: 6,
        images: null,
      },
      {
        id: 5,
        title: "Cottony Goldenaster",
        slug: "6ca3651b-ce0b-45c7-9bd3-ece235142da5",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Proin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.\n\nAenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.",
        categoryId: 6,
        images: null,
      },
      {
        id: 6,
        title: "Walter's Sedge",
        slug: "61a985f0-5078-49de-8ed8-23018c5381d6",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Cras mi pede, malesuada in, imperdiet et, commodo vulputate, justo. In blandit ultrices enim. Lorem ipsum dolor sit amet, consectetuer adipiscing elit.",
        categoryId: 1,
        images: null,
      },
      {
        id: 7,
        title: "Sickle Island Spleenwort",
        slug: "e4ab8737-a719-4fc1-900c-f72f2d0d7700",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.\n\nFusce consequat. Nulla nisl. Nunc nisl.",
        categoryId: 3,
        images: null,
      },
      {
        id: 8,
        title: "Oakwoods Prairie Clover",
        slug: "41ed56fc-7644-475a-9c83-ea50e1b9fc76",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Nam ultrices, libero non mattis pulvinar, nulla pede ullamcorper augue, a suscipit nulla elit ac nulla. Sed vel enim sit amet nunc viverra dapibus. Nulla suscipit ligula in lacus.",
        categoryId: 5,
        images: null,
      },
      {
        id: 9,
        title: "Basil Mountainmint",
        slug: "a9047056-a04f-4185-82c6-c7a140f0457c",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Fusce consequat. Nulla nisl. Nunc nisl.\n\nDuis bibendum, felis sed interdum venenatis, turpis enim blandit mi, in porttitor pede justo eu massa. Donec dapibus. Duis at velit eu est congue elementum.\n\nIn hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.",
        categoryId: 13,
        images: null,
      },
      {
        id: 10,
        title: "Resindot Sunflower",
        slug: "748e3d81-8bf8-4f78-b3ac-331bd8fb51c1",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Vestibulum quam sapien, varius ut, blandit non, interdum in, ante. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Duis faucibus accumsan odio. Curabitur convallis.\n\nDuis consequat dui nec nisi volutpat eleifend. Donec ut dolor. Morbi vel lectus in quam fringilla rhoncus.",
        categoryId: 1,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:59 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-9/20",
      "CF-Ray",
      "879c9ba44a2a7255-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?limit=10&offset=0&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "2",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/products")
  .query({ select: "*", offset: "0", limit: "10" })
  .reply(
    406,
    {
      code: "PGRST106",
      details: null,
      hint: null,
      message: "The schema must be one of the following: public, storage",
    },
    [
      "Date",
      "Thu, 25 Apr 2024 07:26:59 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "CF-Ray",
      "879c9ba52e545153-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "0",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({
    select: "%2A",
    offset: "0",
    limit: "10",
    or: "%28id.eq.2%2Ctags.cs.%7B%22recipes%22%2C%22personal%22%2C%22food%22%7D%29",
  })
  .reply(
    200,
    [
      {
        id: 8,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "Oakwoods Prairie Clover",
        slug: "41ed56fc-7644-475a-9c83-ea50e1b9fc76",
        content:
          "Nam ultrices, libero non mattis pulvinar, nulla pede ullamcorper augue, a suscipit nulla elit ac nulla. Sed vel enim sit amet nunc viverra dapibus. Nulla suscipit ligula in lacus.",
        categoryId: 5,
        tags: ["travel", "food", "recipes", "personal"],
        images: null,
      },
      {
        id: 2,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "Samsung Galaxy S21",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        tags: ["lifestyle", "health", "travel"],
        images: null,
      },
      {
        id: 1,
        created_at: "2024-05-06T11:27:01.700396+00:00",
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        content: "test content",
        categoryId: 5,
        tags: [
          "lifestyle",
          "health",
          "travel",
          "food",
          "recipes",
          "personal",
          "technology",
          "fashion",
          "beauty",
          "skincare",
          "education",
          "mental health",
        ],
        images: "",
      },
    ],
    [
      "Date",
      "Thu, 23 May 2024 14:47:49 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-2/3",
      "CF-Ray",
      "8885d7e6c86c5aef-VIE",
      "CF-Cache-Status",
      "DYNAMIC",
      "Content-Location",
      "/posts?limit=10&offset=0&or=%28id.eq.2%2Ctags.cs.%7B%22recipes%22%2C%22personal%22%2C%22food%22%7D%29&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "content-profile",
      "public",
      "preference-applied",
      "count=exact",
      "sb-gateway-version",
      "1",
      "x-envoy-upstream-service-time",
      "14",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({
    select: "%2A",
    offset: "0",
    limit: "10",
    tags: "cs.%7Bhealth%2Ctravel%7D",
  })
  .reply(
    200,
    [
      {
        id: 6,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "Walter's Sedge",
        slug: "61a985f0-5078-49de-8ed8-23018c5381d6",
        content:
          "Cras mi pede, malesuada in, imperdiet et, commodo vulputate, justo. In blandit ultrices enim. Lorem ipsum dolor sit amet, consectetuer adipiscing elit.",
        categoryId: 1,
        tags: ["lifestyle", "personal", "health", "travel", "food"],
        images: null,
      },
      {
        id: 2,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "Samsung Galaxy S21",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        tags: ["lifestyle", "health", "travel"],
        images: null,
      },
      {
        id: 1,
        created_at: "2024-05-06T11:27:01.700396+00:00",
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        content: "test content",
        categoryId: 5,
        tags: [
          "lifestyle",
          "health",
          "travel",
          "food",
          "recipes",
          "personal",
          "technology",
          "fashion",
          "beauty",
          "skincare",
          "education",
          "mental health",
        ],
        images: "",
      },
    ],
    [
      "Date",
      "Thu, 23 May 2024 14:33:53 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-2/3",
      "CF-Ray",
      "8885c3798cb23bbd-WAW",
      "CF-Cache-Status",
      "DYNAMIC",
      "Content-Location",
      "/posts?limit=10&offset=0&select=%2A&tags=cs.%7Bhealth%2Ctravel%7D",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "content-profile",
      "public",
      "preference-applied",
      "count=exact",
      "sb-gateway-version",
      "1",
      "x-envoy-upstream-service-time",
      "2",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({
    select: "%2A",
    offset: "0",
    limit: "10",
    tags: "not.cs.%7B%22lifestyle%22%2C%22personal%22%7D",
  })
  .reply(
    200,
    [
      {
        id: 7,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "Sickle Island Spleenwort",
        slug: "e4ab8737-a719-4fc1-900c-f72f2d0d7700",
        content:
          "Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.\n\nFusce consequat. Nulla nisl. Nunc nisl.",
        categoryId: 3,
        tags: ["fashion", "beauty", "mental health"],
        images: null,
      },
      {
        id: 8,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "Oakwoods Prairie Clover",
        slug: "41ed56fc-7644-475a-9c83-ea50e1b9fc76",
        content:
          "Nam ultrices, libero non mattis pulvinar, nulla pede ullamcorper augue, a suscipit nulla elit ac nulla. Sed vel enim sit amet nunc viverra dapibus. Nulla suscipit ligula in lacus.",
        categoryId: 5,
        tags: ["travel", "food", "recipes", "personal"],
        images: null,
      },
      {
        id: 11,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "Funck's Wart Lichen",
        slug: "9d5b7d39-2c6e-40c3-ad4e-9cc46eabe1fb",
        content:
          "Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Proin risus. Praesent lectus.\n\nVestibulum quam sapien, varius ut, blandit non, interdum in, ante. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Duis faucibus accumsan odio. Curabitur convallis.",
        categoryId: 8,
        tags: ["recipes", "food"],
        images: null,
      },
      {
        id: 5,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "test",
        slug: "6ca3651b-ce0b-45c7-9bd3-ece235142da5",
        content: "test content",
        categoryId: 12,
        tags: ["technology", "education"],
        images: null,
      },
      {
        id: 2,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "Samsung Galaxy S21",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        tags: ["lifestyle", "health", "travel"],
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 23 May 2024 14:39:43 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-4/5",
      "CF-Ray",
      "8885cc0ae9abfc5b-WAW",
      "CF-Cache-Status",
      "DYNAMIC",
      "Content-Location",
      "/posts?limit=10&offset=0&select=%2A&tags=not.cs.%7B%22lifestyle%22%2C%22personal%22%7D",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "content-profile",
      "public",
      "preference-applied",
      "count=exact",
      "sb-gateway-version",
      "1",
      "x-envoy-upstream-service-time",
      "2",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({
    select: "%2A",
    offset: "0",
    limit: "10",
    or: "%28tags.cs.%7B%22technology%22%2C%22education%22%7D%2Ctags.not.cs.%7B%22lifestyle%22%2C%22personal%22%7D%29",
  })
  .reply(
    200,
    [
      {
        id: 7,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "Sickle Island Spleenwort",
        slug: "e4ab8737-a719-4fc1-900c-f72f2d0d7700",
        content:
          "Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.\n\nFusce consequat. Nulla nisl. Nunc nisl.",
        categoryId: 3,
        tags: ["fashion", "beauty", "mental health"],
        images: null,
      },
      {
        id: 8,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "Oakwoods Prairie Clover",
        slug: "41ed56fc-7644-475a-9c83-ea50e1b9fc76",
        content:
          "Nam ultrices, libero non mattis pulvinar, nulla pede ullamcorper augue, a suscipit nulla elit ac nulla. Sed vel enim sit amet nunc viverra dapibus. Nulla suscipit ligula in lacus.",
        categoryId: 5,
        tags: ["travel", "food", "recipes", "personal"],
        images: null,
      },
      {
        id: 11,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "Funck's Wart Lichen",
        slug: "9d5b7d39-2c6e-40c3-ad4e-9cc46eabe1fb",
        content:
          "Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Proin risus. Praesent lectus.\n\nVestibulum quam sapien, varius ut, blandit non, interdum in, ante. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Duis faucibus accumsan odio. Curabitur convallis.",
        categoryId: 8,
        tags: ["recipes", "food"],
        images: null,
      },
      {
        id: 5,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "test",
        slug: "6ca3651b-ce0b-45c7-9bd3-ece235142da5",
        content: "test content",
        categoryId: 12,
        tags: ["technology", "education"],
        images: null,
      },
      {
        id: 2,
        created_at: "2024-04-24T13:20:10.200327+00:00",
        title: "Samsung Galaxy S21",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        tags: ["lifestyle", "health", "travel"],
        images: null,
      },
      {
        id: 1,
        created_at: "2024-05-06T11:27:01.700396+00:00",
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        content: "test content",
        categoryId: 5,
        tags: [
          "lifestyle",
          "health",
          "travel",
          "food",
          "recipes",
          "personal",
          "technology",
          "fashion",
          "beauty",
          "skincare",
          "education",
          "mental health",
        ],
        images: "",
      },
    ],
    [
      "Date",
      "Thu, 23 May 2024 14:53:46 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-5/6",
      "CF-Ray",
      "8885e09c2872b215-WAW",
      "CF-Cache-Status",
      "DYNAMIC",
      "Content-Location",
      "/posts?limit=10&offset=0&or=%28tags.cs.%7B%22technology%22%2C%22education%22%7D%2Ctags.not.cs.%7B%22lifestyle%22%2C%22personal%22%7D%29&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "content-profile",
      "public",
      "preference-applied",
      "count=exact",
      "sb-gateway-version",
      "1",
      "x-envoy-upstream-service-time",
      "14",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({
    select: "%2A",
    offset: "0",
    limit: "10",
    or: "%28title.ilike.%22%25Black%20Psorotichia%25%22%2Ccontent.ilike.%22%25Sed%20sagittis%25%22%29",
  })
  .reply(
    200,
    [
      {
        id: 1,
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.\n\nPraesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.",
        categoryId: 7,
        images: null,
      },
      {
        id: 13,
        title: "Dust Lichen",
        slug: "ca038c02-0c6f-417b-aaf8-6f51b777d1f5",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Fusce posuere felis sed lacus. Morbi sem mauris, laoreet ut, rhoncus aliquet, pulvinar sed, nisl. Nunc rhoncus dui vel sem.\n\nSed sagittis. Nam congue, risus semper porta volutpat, quam pede lobortis ligula, sit amet eleifend pede libero quis orci. Nullam molestie nibh in lectus.",
        categoryId: 7,
        images: null,
      },
    ],
    [
      "Date",
      "Mon, 17 Mar 2025 10:29:52 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-1/2",
      "CF-Ray",
      "921bcfca7bad925f-FRA",
      "CF-Cache-Status",
      "DYNAMIC",
      "Content-Location",
      "/posts?limit=10&offset=0&or=%28title.ilike.%22%25Black%20Psorotichia%25%22%2Ccontent.ilike.%22%25Sed%20sagittis%25%22%29&select=%2A",
      "Strict-Transport-Security",
      "max-age=31536000; includeSubDomains; preload",
      "Vary",
      "Accept-Encoding",
      "content-profile",
      "public",
      "preference-applied",
      "count=exact",
      "sb-gateway-version",
      "1",
      "sb-project-ref",
      "iwdfzvfqbtokqetmbmbp",
      "X-Content-Type-Options",
      "nosniff",
      "x-envoy-attempt-count",
      "1",
      "x-envoy-upstream-service-time",
      "18",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({
    select: "%2A",
    offset: "0",
    limit: "10",
    or: "%28id.in.%28%221%22%2C%222%22%29%29",
  })
  .reply(
    200,
    [
      {
        id: 1,
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.\n\nPraesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.",
        categoryId: 7,
        images: null,
      },
      {
        id: 2,
        title: "Great Plains Flatsedge",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 20 Feb 2025 09:06:45 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-1/2",
      "CF-Ray",
      "914d57a7bac9c287-VIE",
      "CF-Cache-Status",
      "DYNAMIC",
      "Content-Location",
      "/posts?limit=10&offset=0&or=%28id.in.%28%221%22%2C%222%22%29%29&select=%2A",
      "Strict-Transport-Security",
      "max-age=31536000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "content-profile",
      "public",
      "preference-applied",
      "count=exact",
      "sb-gateway-version",
      "1",
      "sb-project-ref",
      "iwdfzvfqbtokqetmbmbp",
      "X-Content-Type-Options",
      "nosniff",
      "x-envoy-attempt-count",
      "1",
      "x-envoy-upstream-service-time",
      "34",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );
