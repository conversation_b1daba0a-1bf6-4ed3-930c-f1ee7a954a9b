{"name": "@refinedev/ui-tests", "version": "1.15.1", "private": false, "description": "UI test utilities for Refine, ensuring quality and reliability.", "license": "MIT", "author": "refine", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "typings": "dist/index.d.ts", "scripts": {"attw": "attw --pack .", "build": "tsup && node ../shared/generate-declarations.js", "dev": "tsup --watch", "prepare": "pnpm build", "publint": "publint --strict=true --level=suggestion", "test": "jest --passWithNoTests --runInBand", "types": "node ../shared/generate-declarations.js"}, "dependencies": {"@refinedev/core": "^4.57.1", "@refinedev/ui-types": "^1.23.1", "tslib": "^2.6.2"}, "devDependencies": {"@esbuild-plugins/node-resolve": "^0.1.4", "@testing-library/dom": "^8.5.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.1.1", "@testing-library/react-hooks": "^8.0.0", "@testing-library/user-event": "^14.1.1", "@types/jest": "^29.2.4", "@types/lodash": "^4.14.171", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/testing-library__jest-dom": "^5.14.3", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "react-router": "^7.0.2", "ts-jest": "^29.1.2", "tsup": "^6.7.0", "typescript": "^5.4.2"}, "peerDependencies": {"@refinedev/core": "^4.46.1", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "publishConfig": {"access": "public"}}