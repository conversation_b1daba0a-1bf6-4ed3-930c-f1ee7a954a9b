"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
__exportStar(require("./CatchClauseDefinition"), exports);
__exportStar(require("./ClassNameDefinition"), exports);
__exportStar(require("./Definition"), exports);
__exportStar(require("./DefinitionType"), exports);
__exportStar(require("./FunctionNameDefinition"), exports);
__exportStar(require("./ImplicitGlobalVariableDefinition"), exports);
__exportStar(require("./ImportBindingDefinition"), exports);
__exportStar(require("./ParameterDefinition"), exports);
__exportStar(require("./TSEnumMemberDefinition"), exports);
__exportStar(require("./TSEnumNameDefinition"), exports);
__exportStar(require("./TSModuleNameDefinition"), exports);
__exportStar(require("./TypeDefinition"), exports);
__exportStar(require("./VariableDefinition"), exports);
//# sourceMappingURL=index.js.map