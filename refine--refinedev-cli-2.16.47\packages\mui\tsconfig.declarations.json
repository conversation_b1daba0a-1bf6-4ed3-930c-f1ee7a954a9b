{"extends": "./tsconfig.json", "exclude": ["node_modules", "dist", "test", "../test/**/*", "**/*.spec.ts", "**/*.test.ts", "**/*.spec.tsx", "**/*.test.tsx"], "compilerOptions": {"outDir": "dist", "declarationDir": "dist", "declaration": true, "emitDeclarationOnly": true, "noEmit": false, "declarationMap": true, "paths": {"@components/*": ["src/components/*"], "@components": ["src/components"], "@hooks/*": ["src/hooks/*"], "@hooks": ["src/hooks"], "@definitions/*": ["src/definitions/*"], "@definitions": ["src/definitions"], "@contexts/*": ["src/contexts/*"], "@contexts": ["src/contexts"]}}}