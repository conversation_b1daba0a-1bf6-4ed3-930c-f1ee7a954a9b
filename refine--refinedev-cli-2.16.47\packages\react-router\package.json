{"name": "@refinedev/react-router", "version": "1.0.1", "private": false, "description": "React Router support for Refine, with full routing capabilities.", "repository": {"type": "git", "url": "https://github.com/refinedev/refine.git", "directory": "packages/react-router"}, "license": "MIT", "author": "refine", "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "typesVersions": {"*": {".": ["dist/index.d.ts"]}}, "typings": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"attw": "attw --pack .", "build": "tsup && node ../shared/generate-declarations.js", "dev": "tsup --watch", "prepare": "pnpm build", "publint": "publint --strict=true --level=suggestion", "test": "jest --passWithNoTests --runInBand", "types": "node ../shared/generate-declarations.js"}, "dependencies": {"qs": "^6.10.1", "react-router": "^7.0.2"}, "devDependencies": {"@esbuild-plugins/node-resolve": "^0.1.4", "@refinedev/core": "^4.57.1", "@types/jest": "^29.2.4", "@types/qs": "^6.9.7", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "ts-jest": "^29.1.2", "tslib": "^2.6.2", "tsup": "^6.7.0", "typescript": "^5.4.2"}, "peerDependencies": {"@refinedev/core": "^4.46.1", "@types/react": "^17.0.0 || ^18.0.0", "@types/react-dom": "^17.0.0 || ^18.0.0", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0", "react-router": "^7.0.2"}, "engines": {"node": ">=10"}, "publishConfig": {"access": "public"}}