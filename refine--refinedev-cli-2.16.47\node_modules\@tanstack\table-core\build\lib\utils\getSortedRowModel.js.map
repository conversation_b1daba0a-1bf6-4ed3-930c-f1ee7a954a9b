{"version": 3, "file": "getSortedRowModel.js", "sources": ["../../../src/utils/getSortedRowModel.ts"], "sourcesContent": ["import { Table, Row, RowModel, RowData } from '../types'\nimport { SortingFn } from '../features/RowSorting'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getSortedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().sorting, table.getPreSortedRowModel()],\n      (sorting, rowModel) => {\n        if (!rowModel.rows.length || !sorting?.length) {\n          return rowModel\n        }\n\n        const sortingState = table.getState().sorting\n\n        const sortedFlatRows: Row<TData>[] = []\n\n        // Filter out sortings that correspond to non existing columns\n        const availableSorting = sortingState.filter(\n          sort => table.getColumn(sort.id)?.getCanSort()\n        )\n\n        const columnInfoById: Record<\n          string,\n          {\n            sortUndefined?: false | -1 | 1 | 'first' | 'last'\n            invertSorting?: boolean\n            sortingFn: SortingFn<TData>\n          }\n        > = {}\n\n        availableSorting.forEach(sortEntry => {\n          const column = table.getColumn(sortEntry.id)\n          if (!column) return\n\n          columnInfoById[sortEntry.id] = {\n            sortUndefined: column.columnDef.sortUndefined,\n            invertSorting: column.columnDef.invertSorting,\n            sortingFn: column.getSortingFn(),\n          }\n        })\n\n        const sortData = (rows: Row<TData>[]) => {\n          // This will also perform a stable sorting using the row index\n          // if needed.\n          const sortedData = rows.map(row => ({ ...row }))\n\n          sortedData.sort((rowA, rowB) => {\n            for (let i = 0; i < availableSorting.length; i += 1) {\n              const sortEntry = availableSorting[i]!\n              const columnInfo = columnInfoById[sortEntry.id]!\n              const sortUndefined = columnInfo.sortUndefined\n              const isDesc = sortEntry?.desc ?? false\n\n              let sortInt = 0\n\n              // All sorting ints should always return in ascending order\n              if (sortUndefined) {\n                const aValue = rowA.getValue(sortEntry.id)\n                const bValue = rowB.getValue(sortEntry.id)\n\n                const aUndefined = aValue === undefined\n                const bUndefined = bValue === undefined\n\n                if (aUndefined || bUndefined) {\n                  if (sortUndefined === 'first') return aUndefined ? -1 : 1\n                  if (sortUndefined === 'last') return aUndefined ? 1 : -1\n                  sortInt =\n                    aUndefined && bUndefined\n                      ? 0\n                      : aUndefined\n                        ? sortUndefined\n                        : -sortUndefined\n                }\n              }\n\n              if (sortInt === 0) {\n                sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id)\n              }\n\n              // If sorting is non-zero, take care of desc and inversion\n              if (sortInt !== 0) {\n                if (isDesc) {\n                  sortInt *= -1\n                }\n\n                if (columnInfo.invertSorting) {\n                  sortInt *= -1\n                }\n\n                return sortInt\n              }\n            }\n\n            return rowA.index - rowB.index\n          })\n\n          // If there are sub-rows, sort them\n          sortedData.forEach(row => {\n            sortedFlatRows.push(row)\n            if (row.subRows?.length) {\n              row.subRows = sortData(row.subRows)\n            }\n          })\n\n          return sortedData\n        }\n\n        return {\n          rows: sortData(rowModel.rows),\n          flatRows: sortedFlatRows,\n          rowsById: rowModel.rowsById,\n        }\n      },\n      getMemoOptions(table.options, 'debugTable', 'getSortedRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n"], "names": ["getSortedRowModel", "table", "memo", "getState", "sorting", "getPreSortedRowModel", "rowModel", "rows", "length", "sortingState", "sortedFlatRows", "availableSorting", "filter", "sort", "_table$getColumn", "getColumn", "id", "getCanSort", "columnInfoById", "for<PERSON>ach", "sortEntry", "column", "sortUndefined", "columnDef", "invertSorting", "sortingFn", "getSortingFn", "sortData", "sortedData", "map", "row", "rowA", "rowB", "i", "_sortEntry$desc", "columnInfo", "isDesc", "desc", "sortInt", "aValue", "getValue", "bValue", "aUndefined", "undefined", "bUndefined", "index", "_row$subRows", "push", "subRows", "flatRows", "rowsById", "getMemoOptions", "options", "_autoResetPageIndex"], "mappings": ";;;;;;;;;;;;;;AAIO,SAASA,iBAAiBA,GAEN;EACzB,OAAOC,KAAK,IACVC,UAAI,CACF,MAAM,CAACD,KAAK,CAACE,QAAQ,EAAE,CAACC,OAAO,EAAEH,KAAK,CAACI,oBAAoB,EAAE,CAAC,EAC9D,CAACD,OAAO,EAAEE,QAAQ,KAAK;AACrB,IAAA,IAAI,CAACA,QAAQ,CAACC,IAAI,CAACC,MAAM,IAAI,EAACJ,OAAO,IAAA,IAAA,IAAPA,OAAO,CAAEI,MAAM,CAAE,EAAA;AAC7C,MAAA,OAAOF,QAAQ,CAAA;AACjB,KAAA;IAEA,MAAMG,YAAY,GAAGR,KAAK,CAACE,QAAQ,EAAE,CAACC,OAAO,CAAA;IAE7C,MAAMM,cAA4B,GAAG,EAAE,CAAA;;AAEvC;AACA,IAAA,MAAMC,gBAAgB,GAAGF,YAAY,CAACG,MAAM,CAC1CC,IAAI,IAAA;AAAA,MAAA,IAAAC,gBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,gBAAA,GAAIb,KAAK,CAACc,SAAS,CAACF,IAAI,CAACG,EAAE,CAAC,KAAxBF,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAA,CAA0BG,UAAU,EAAE,CAAA;AAAA,KAChD,CAAC,CAAA;IAED,MAAMC,cAOL,GAAG,EAAE,CAAA;AAENP,IAAAA,gBAAgB,CAACQ,OAAO,CAACC,SAAS,IAAI;MACpC,MAAMC,MAAM,GAAGpB,KAAK,CAACc,SAAS,CAACK,SAAS,CAACJ,EAAE,CAAC,CAAA;MAC5C,IAAI,CAACK,MAAM,EAAE,OAAA;AAEbH,MAAAA,cAAc,CAACE,SAAS,CAACJ,EAAE,CAAC,GAAG;AAC7BM,QAAAA,aAAa,EAAED,MAAM,CAACE,SAAS,CAACD,aAAa;AAC7CE,QAAAA,aAAa,EAAEH,MAAM,CAACE,SAAS,CAACC,aAAa;AAC7CC,QAAAA,SAAS,EAAEJ,MAAM,CAACK,YAAY,EAAC;OAChC,CAAA;AACH,KAAC,CAAC,CAAA;IAEF,MAAMC,QAAQ,GAAIpB,IAAkB,IAAK;AACvC;AACA;AACA,MAAA,MAAMqB,UAAU,GAAGrB,IAAI,CAACsB,GAAG,CAACC,GAAG,KAAK;QAAE,GAAGA,GAAAA;AAAI,OAAC,CAAC,CAAC,CAAA;AAEhDF,MAAAA,UAAU,CAACf,IAAI,CAAC,CAACkB,IAAI,EAAEC,IAAI,KAAK;AAC9B,QAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,gBAAgB,CAACH,MAAM,EAAEyB,CAAC,IAAI,CAAC,EAAE;AAAA,UAAA,IAAAC,eAAA,CAAA;AACnD,UAAA,MAAMd,SAAS,GAAGT,gBAAgB,CAACsB,CAAC,CAAE,CAAA;AACtC,UAAA,MAAME,UAAU,GAAGjB,cAAc,CAACE,SAAS,CAACJ,EAAE,CAAE,CAAA;AAChD,UAAA,MAAMM,aAAa,GAAGa,UAAU,CAACb,aAAa,CAAA;AAC9C,UAAA,MAAMc,MAAM,GAAA,CAAAF,eAAA,GAAGd,SAAS,IAAA,IAAA,GAAA,KAAA,CAAA,GAATA,SAAS,CAAEiB,IAAI,KAAA,IAAA,GAAAH,eAAA,GAAI,KAAK,CAAA;UAEvC,IAAII,OAAO,GAAG,CAAC,CAAA;;AAEf;AACA,UAAA,IAAIhB,aAAa,EAAE;YACjB,MAAMiB,MAAM,GAAGR,IAAI,CAACS,QAAQ,CAACpB,SAAS,CAACJ,EAAE,CAAC,CAAA;YAC1C,MAAMyB,MAAM,GAAGT,IAAI,CAACQ,QAAQ,CAACpB,SAAS,CAACJ,EAAE,CAAC,CAAA;AAE1C,YAAA,MAAM0B,UAAU,GAAGH,MAAM,KAAKI,SAAS,CAAA;AACvC,YAAA,MAAMC,UAAU,GAAGH,MAAM,KAAKE,SAAS,CAAA;YAEvC,IAAID,UAAU,IAAIE,UAAU,EAAE;cAC5B,IAAItB,aAAa,KAAK,OAAO,EAAE,OAAOoB,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;cACzD,IAAIpB,aAAa,KAAK,MAAM,EAAE,OAAOoB,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;AACxDJ,cAAAA,OAAO,GACLI,UAAU,IAAIE,UAAU,GACpB,CAAC,GACDF,UAAU,GACRpB,aAAa,GACb,CAACA,aAAa,CAAA;AACxB,aAAA;AACF,WAAA;UAEA,IAAIgB,OAAO,KAAK,CAAC,EAAE;AACjBA,YAAAA,OAAO,GAAGH,UAAU,CAACV,SAAS,CAACM,IAAI,EAAEC,IAAI,EAAEZ,SAAS,CAACJ,EAAE,CAAC,CAAA;AAC1D,WAAA;;AAEA;UACA,IAAIsB,OAAO,KAAK,CAAC,EAAE;AACjB,YAAA,IAAIF,MAAM,EAAE;cACVE,OAAO,IAAI,CAAC,CAAC,CAAA;AACf,aAAA;YAEA,IAAIH,UAAU,CAACX,aAAa,EAAE;cAC5Bc,OAAO,IAAI,CAAC,CAAC,CAAA;AACf,aAAA;AAEA,YAAA,OAAOA,OAAO,CAAA;AAChB,WAAA;AACF,SAAA;AAEA,QAAA,OAAOP,IAAI,CAACc,KAAK,GAAGb,IAAI,CAACa,KAAK,CAAA;AAChC,OAAC,CAAC,CAAA;;AAEF;AACAjB,MAAAA,UAAU,CAACT,OAAO,CAACW,GAAG,IAAI;AAAA,QAAA,IAAAgB,YAAA,CAAA;AACxBpC,QAAAA,cAAc,CAACqC,IAAI,CAACjB,GAAG,CAAC,CAAA;QACxB,IAAAgB,CAAAA,YAAA,GAAIhB,GAAG,CAACkB,OAAO,KAAXF,IAAAA,IAAAA,YAAA,CAAatC,MAAM,EAAE;UACvBsB,GAAG,CAACkB,OAAO,GAAGrB,QAAQ,CAACG,GAAG,CAACkB,OAAO,CAAC,CAAA;AACrC,SAAA;AACF,OAAC,CAAC,CAAA;AAEF,MAAA,OAAOpB,UAAU,CAAA;KAClB,CAAA;IAED,OAAO;AACLrB,MAAAA,IAAI,EAAEoB,QAAQ,CAACrB,QAAQ,CAACC,IAAI,CAAC;AAC7B0C,MAAAA,QAAQ,EAAEvC,cAAc;MACxBwC,QAAQ,EAAE5C,QAAQ,CAAC4C,QAAAA;KACpB,CAAA;AACH,GAAC,EACDC,oBAAc,CAAClD,KAAK,CAACmD,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,MAC/DnD,KAAK,CAACoD,mBAAmB,EAC3B,CACF,CAAC,CAAA;AACL;;;;"}