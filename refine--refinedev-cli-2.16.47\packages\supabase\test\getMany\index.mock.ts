import nock from "nock";

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", id: "in.%282%2C3%29" })
  .reply(
    200,
    [
      {
        id: 2,
        title: "Great Plains Flatsedge",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        images: null,
      },
      {
        id: 3,
        title: "Copperweed",
        slug: "8d9116f0-ee0e-48ec-8d76-c056d041820f",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Morbi non lectus. Aliquam sit amet diam in magna bibendum imperdiet. Nullam orci pede, venenatis non, sodales sed, tincidunt eu, felis.",
        categoryId: 1,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 09:09:30 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-1/*",
      "CF-Ray",
      "879d31d06c387218-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?id=in.%282%2C3%29&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "3",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "title", id: "in.%283%2C4%29" })
  .reply(
    200,
    [{ title: "Copperweed" }, { title: "Bastard Toadflax" }],
    [
      "Date",
      "Thu, 25 Apr 2024 09:09:30 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-1/*",
      "CF-Ray",
      "879d31d29c005142-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?id=in.%283%2C4%29&select=title",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "3",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", id: "in.%281%2C2%29" })
  .reply(
    200,
    [
      {
        id: 1,
        title: "Black Psorotichia Lichen",
        slug: "61a31089-c85d-48a0-a4be-d5dce5c96b6a",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.\n\nPraesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.\n\nMorbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.",
        categoryId: 7,
        images: null,
      },
      {
        id: 2,
        title: "Great Plains Flatsedge",
        slug: "5aecd7b0-cf28-40b4-ad48-7d4c6718837e",
        createdAt: "2024-04-24T13:20:10.200327+00:00",
        content:
          "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.\n\nNulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.\n\nCras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.",
        categoryId: 8,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 09:09:31 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "0-1/*",
      "CF-Ray",
      "879d31d4bba76950-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Content-Location",
      "/posts?id=in.%281%2C2%29&select=%2A",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "3",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .get("/rest/v1/posts")
  .query({ select: "%2A", id: "in.%281%2C2%29" })
  .reply(
    406,
    {
      code: "PGRST106",
      details: null,
      hint: null,
      message: "The schema must be one of the following: public, storage",
    },
    [
      "Date",
      "Thu, 25 Apr 2024 09:09:31 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "CF-Ray",
      "879d31d7c8dd514d-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "0",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );
