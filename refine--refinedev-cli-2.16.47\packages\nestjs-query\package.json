{"name": "@refinedev/nestjs-query", "version": "1.3.5", "private": false, "description": "NestJS CRUD integration for Refine, simplifying CRUD operations.", "repository": {"type": "git", "url": "https://github.com/refinedev/refine.git", "directory": "packages/graphql"}, "license": "MIT", "author": "refine", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "typings": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"attw": "attw --pack .", "build": "tsup && node ../shared/generate-declarations.js", "dev": "tsup --watch", "prepare": "pnpm build", "publint": "publint --strict=true --level=suggestion", "test": "jest --passWithNoTests --runInBand", "types": "node ../shared/generate-declarations.js"}, "dependencies": {"camelcase": "^6.2.0", "gql-query-builder": "^3.5.5", "graphql": "^15.6.1", "graphql-request": "^5.2.0", "graphql-tag": "^2.12.6", "graphql-ws": "^5.9.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "pluralize": "^8.0.0"}, "devDependencies": {"@esbuild-plugins/node-resolve": "^0.1.4", "@refinedev/cli": "^2.16.42", "@refinedev/core": "^4.57.1", "@types/jest": "^29.2.4", "@types/lodash": "^4.14.171", "@types/pluralize": "^0.0.29", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "nock": "^13.4.0", "ts-jest": "^29.1.2", "tslib": "^2.6.2", "tsup": "^6.7.0", "typescript": "^5.4.2"}, "peerDependencies": {"@refinedev/core": "^4.46.1", "gql-query-builder": "^3.5.5", "graphql-request": "^5.2.0", "graphql-tag": "^2.12.6", "graphql-ws": "^5.9.1"}, "engines": {"node": ">=10"}, "publishConfig": {"access": "public"}}