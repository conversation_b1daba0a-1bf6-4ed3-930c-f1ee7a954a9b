import nock from "nock";

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .post("/auth/v1/token", {
    email: "<EMAIL>",
    password: "refine-supabase",
    gotrue_meta_security: {},
  })
  .query({ grant_type: "password" })
  .reply(
    200,
    {
      access_token:
        "eyJhbGciOiJIUzI1NiIsImtpZCI6IldGWnFuOWt6bnBJZTIvL2wiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yVJ6lWNwdQCsbPsP5tfJk7Xjtx2bHRBaiyaox6KcMX0",
      token_type: "bearer",
      expires_in: 3600,
      expires_at: **********,
      refresh_token: "R0tQR-e2RgQQdsS7OTMCWQ",
      user: {
        id: "bdefac81-2bd1-44d1-b5ed-7abedb96ccce",
        aud: "authenticated",
        role: "authenticated",
        email: "<EMAIL>",
        email_confirmed_at: "2021-09-08T11:09:24.284171Z",
        phone: "",
        confirmation_sent_at: "2021-09-08T11:08:06.793257Z",
        confirmed_at: "2021-09-08T11:09:24.284171Z",
        recovery_sent_at: "2024-02-04T09:33:53.383988Z",
        last_sign_in_at: "2024-04-25T08:58:15.046729193Z",
        app_metadata: { provider: "email" },
        user_metadata: {},
        identities: [
          {
            identity_id: "6b8dcf5b-f068-401b-95ae-ddd93d771b74",
            id: "bdefac81-2bd1-44d1-b5ed-7abedb96ccce",
            user_id: "bdefac81-2bd1-44d1-b5ed-7abedb96ccce",
            identity_data: {
              email: "<EMAIL>",
              sub: "bdefac81-2bd1-44d1-b5ed-7abedb96ccce",
            },
            provider: "email",
            last_sign_in_at: "2022-11-25T00:00:00Z",
            created_at: "2022-11-25T00:00:00Z",
            updated_at: "2022-11-25T00:00:00Z",
            email: "<EMAIL>",
          },
        ],
        created_at: "2021-09-08T11:08:06.789274Z",
        updated_at: "2024-04-25T08:58:15.04881Z",
        is_anonymous: false,
      },
    },
    [
      "Date",
      "Thu, 25 Apr 2024 08:58:15 GMT",
      "Content-Type",
      "application/json",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "CF-Ray",
      "879d21512ac6519b-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Set-Cookie",
      "sb-access-token=eyJhbGciOiJIUzI1NiIsImtpZCI6IldGWnFuOWt6bnBJZTIvL2wiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yVJ6lWNwdQCsbPsP5tfJk7Xjtx2bHRBaiyaox6KcMX0; Path=/; Expires=Fri, 26 Apr 2024 08:58:15 GMT; Max-Age=86400; HttpOnly; Secure",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding, Origin",
      "Via",
      "kong/2.8.1",
      "sb-gateway-version",
      "1",
      "Set-Cookie",
      "sb-refresh-token=R0tQR-e2RgQQdsS7OTMCWQ; Path=/; Expires=Fri, 26 Apr 2024 08:58:15 GMT; Max-Age=86400; HttpOnly; Secure",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "90",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .post("/rest/v1/posts", {
    title: "foo",
    slug: "foo-bar",
    content: "bar",
    categoryId: 2,
  })
  .query({ select: "%2A" })
  .reply(
    201,
    [
      {
        id: 12893,
        title: "foo",
        slug: "foo-bar",
        createdAt: "2024-04-25T08:58:15.561287+00:00",
        content: "bar",
        categoryId: 2,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 08:58:15 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "*/*",
      "CF-Ray",
      "879d2155cf286970-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "3",
      "x-kong-upstream-latency",
      "7",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .post("/rest/v1/posts", {
    title: "foo",
    slug: "foo-bar",
    content: "bar",
    categoryId: 2,
  })
  .query({ select: "%2A" })
  .reply(
    201,
    [
      {
        id: 12894,
        title: "foo",
        slug: "foo-bar",
        createdAt: "2024-04-25T08:58:15.88472+00:00",
        content: "bar",
        categoryId: 2,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 08:58:15 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "*/*",
      "CF-Ray",
      "879d21585813724e-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "7",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .post("/rest/v1/posts", {})
  .query({ select: "%2A" })
  .reply(
    406,
    {
      code: "PGRST106",
      details: null,
      hint: null,
      message: "The schema must be one of the following: public, storage",
    },
    [
      "Date",
      "Thu, 25 Apr 2024 08:58:16 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "CF-Ray",
      "879d2159d8847212-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "0",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .post("/auth/v1/logout")
  .query({ scope: "global" })
  .reply(204, "", [
    "Date",
    "Thu, 25 Apr 2024 08:58:16 GMT",
    "Connection",
    "close",
    "CF-Ray",
    "879d215b9ec65184-IST",
    "CF-Cache-Status",
    "DYNAMIC",
    "Access-Control-Allow-Origin",
    "*",
    "Set-Cookie",
    "sb-access-token=; Path=/; Expires=Wed, 24 Apr 2024 22:58:16 GMT; Max-Age=0; HttpOnly; Secure",
    "Strict-Transport-Security",
    "max-age=15552000; includeSubDomains",
    "Vary",
    "Origin, Accept-Encoding",
    "Via",
    "kong/2.8.1",
    "sb-gateway-version",
    "1",
    "Set-Cookie",
    "sb-refresh-token=; Path=/; Expires=Wed, 24 Apr 2024 22:58:16 GMT; Max-Age=0; HttpOnly; Secure",
    "x-kong-proxy-latency",
    "1",
    "x-kong-upstream-latency",
    "11",
    "Server",
    "cloudflare",
    "alt-svc",
    'h3=":443"; ma=86400',
  ]);
