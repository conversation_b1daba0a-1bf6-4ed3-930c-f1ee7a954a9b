import nock from "nock";

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .post("/auth/v1/token", {
    email: "<EMAIL>",
    password: "refine-supabase",
    gotrue_meta_security: {},
  })
  .query({ grant_type: "password" })
  .reply(
    200,
    {
      access_token:
        "eyJhbGciOiJIUzI1NiIsImtpZCI6IldGWnFuOWt6bnBJZTIvL2wiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JBhZFZU2u_ACen9Oqq4SnwxS1jMtu9V4Q9LUlkpWN1A",
      token_type: "bearer",
      expires_in: 3600,
      expires_at: **********,
      refresh_token: "StH_4ldWnYhySTr7QDsEMA",
      user: {
        id: "bdefac81-2bd1-44d1-b5ed-7abedb96ccce",
        aud: "authenticated",
        role: "authenticated",
        email: "<EMAIL>",
        email_confirmed_at: "2021-09-08T11:09:24.284171Z",
        phone: "",
        confirmation_sent_at: "2021-09-08T11:08:06.793257Z",
        confirmed_at: "2021-09-08T11:09:24.284171Z",
        recovery_sent_at: "2024-02-04T09:33:53.383988Z",
        last_sign_in_at: "2024-04-25T09:00:47.430784517Z",
        app_metadata: { provider: "email" },
        user_metadata: {},
        identities: [
          {
            identity_id: "6b8dcf5b-f068-401b-95ae-ddd93d771b74",
            id: "bdefac81-2bd1-44d1-b5ed-7abedb96ccce",
            user_id: "bdefac81-2bd1-44d1-b5ed-7abedb96ccce",
            identity_data: {
              email: "<EMAIL>",
              sub: "bdefac81-2bd1-44d1-b5ed-7abedb96ccce",
            },
            provider: "email",
            last_sign_in_at: "2022-11-25T00:00:00Z",
            created_at: "2022-11-25T00:00:00Z",
            updated_at: "2022-11-25T00:00:00Z",
            email: "<EMAIL>",
          },
        ],
        created_at: "2021-09-08T11:08:06.789274Z",
        updated_at: "2024-04-25T09:00:47.432602Z",
        is_anonymous: false,
      },
    },
    [
      "Date",
      "Thu, 25 Apr 2024 09:00:47 GMT",
      "Content-Type",
      "application/json",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "CF-Ray",
      "879d25098fce7207-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Set-Cookie",
      "sb-access-token=eyJhbGciOiJIUzI1NiIsImtpZCI6IldGWnFuOWt6bnBJZTIvL2wiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JBhZFZU2u_ACen9Oqq4SnwxS1jMtu9V4Q9LUlkpWN1A; Path=/; Expires=Fri, 26 Apr 2024 09:00:47 GMT; Max-Age=86400; HttpOnly; Secure",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding, Origin",
      "Via",
      "kong/2.8.1",
      "sb-gateway-version",
      "1",
      "Set-Cookie",
      "sb-refresh-token=StH_4ldWnYhySTr7QDsEMA; Path=/; Expires=Fri, 26 Apr 2024 09:00:47 GMT; Max-Age=86400; HttpOnly; Secure",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "90",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .post("/rest/v1/posts", [
    { title: "foo", slug: "foo-bar", content: "bar", categoryId: 2 },
    { title: "foo-2", slug: "foo-bar-2", content: "bar-2", categoryId: 1 },
  ])
  .query({
    columns: "%22title%22%2C%22slug%22%2C%22content%22%2C%22categoryId%22",
    select: "%2A",
  })
  .reply(
    201,
    [
      {
        id: 12895,
        title: "foo",
        slug: "foo-bar",
        createdAt: "2024-04-25T09:00:47.766685+00:00",
        content: "bar",
        categoryId: 2,
        images: null,
      },
      {
        id: 12896,
        title: "foo-2",
        slug: "foo-bar-2",
        createdAt: "2024-04-25T09:00:47.766685+00:00",
        content: "bar-2",
        categoryId: 1,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 09:00:47 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "*/*",
      "CF-Ray",
      "879d250dca78778e-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "5",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .post("/rest/v1/posts", [
    { title: "foo", slug: "foo-bar", content: "bar", categoryId: 2 },
    { title: "foo-2", slug: "foo-bar-2", content: "bar-2", categoryId: 1 },
  ])
  .query({
    columns: "%22title%22%2C%22slug%22%2C%22content%22%2C%22categoryId%22",
    select: "%2A",
  })
  .reply(
    201,
    [
      {
        id: 12897,
        title: "foo",
        slug: "foo-bar",
        createdAt: "2024-04-25T09:00:48.264931+00:00",
        content: "bar",
        categoryId: 2,
        images: null,
      },
      {
        id: 12898,
        title: "foo-2",
        slug: "foo-bar-2",
        createdAt: "2024-04-25T09:00:48.264931+00:00",
        content: "bar-2",
        categoryId: 1,
        images: null,
      },
    ],
    [
      "Date",
      "Thu, 25 Apr 2024 09:00:48 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "Content-Range",
      "*/*",
      "CF-Ray",
      "879d25103a3b693e-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "content-profile",
      "public",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "2",
      "x-kong-upstream-latency",
      "4",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .post("/rest/v1/posts", [])
  .query({ select: "%2A" })
  .reply(
    406,
    {
      code: "PGRST106",
      details: null,
      hint: null,
      message: "The schema must be one of the following: public, storage",
    },
    [
      "Date",
      "Thu, 25 Apr 2024 09:00:48 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "CF-Ray",
      "879d25125ed67240-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "0",
      "x-kong-upstream-latency",
      "1",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .post("/auth/v1/logout")
  .query({ scope: "global" })
  .reply(204, "", [
    "Date",
    "Thu, 25 Apr 2024 09:00:48 GMT",
    "Connection",
    "close",
    "CF-Ray",
    "879d2513ce1c7210-IST",
    "CF-Cache-Status",
    "DYNAMIC",
    "Access-Control-Allow-Origin",
    "*",
    "Set-Cookie",
    "sb-access-token=; Path=/; Expires=Wed, 24 Apr 2024 23:00:48 GMT; Max-Age=0; HttpOnly; Secure",
    "Strict-Transport-Security",
    "max-age=15552000; includeSubDomains",
    "Vary",
    "Origin, Accept-Encoding",
    "Via",
    "kong/2.8.1",
    "sb-gateway-version",
    "1",
    "Set-Cookie",
    "sb-refresh-token=; Path=/; Expires=Wed, 24 Apr 2024 23:00:48 GMT; Max-Age=0; HttpOnly; Secure",
    "x-kong-proxy-latency",
    "0",
    "x-kong-upstream-latency",
    "5",
    "Server",
    "cloudflare",
    "alt-svc",
    'h3=":443"; ma=86400',
  ]);
