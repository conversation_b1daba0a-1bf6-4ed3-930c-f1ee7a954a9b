{"name": "bops", "version": "1.0.1", "description": "buffer operations", "main": "index.js", "scripts": {"test": "node test/index.js"}, "browser": {"./copy.js": "./typedarray/copy.js", "./create.js": "./typedarray/create.js", "./from.js": "./typedarray/from.js", "./join.js": "./typedarray/join.js", "./mapped.js": "./typedarray/mapped.js", "./read.js": "./typedarray/read.js", "./subarray.js": "./typedarray/subarray.js", "./to.js": "./typedarray/to.js", "./is.js": "./typedarray/is.js", "./write.js": "./typedarray/write.js"}, "repository": {"type": "git", "url": "git://github.com/chris<PERSON><PERSON>on/bops.git"}, "keywords": ["buffer", "operations", "binary"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "gitHead": "11f41e69b1ab4507027ac162981913b268445b4a", "devDependencies": {"tape": "~2.0.0"}, "dependencies": {"base64-js": "1.0.2", "to-utf8": "0.0.1"}, "testling": {"files": "test/index.js", "browsers": ["firefox/15..nightly", "chrome/20..canary", "opera/11.6..latest", "safari/4..latest", "iphone/6", "ipad/6"]}}