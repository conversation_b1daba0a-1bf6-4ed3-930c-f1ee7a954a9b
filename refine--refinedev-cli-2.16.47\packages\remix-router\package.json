{"name": "@refinedev/remix-router", "version": "3.0.8", "private": false, "description": "Remix router integration package for Refine.", "repository": {"type": "git", "url": "https://github.com/refinedev/refine.git", "directory": "packages/remix-router"}, "license": "MIT", "author": "refine", "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./legacy": {"import": {"types": "./dist/legacy/index.d.mts", "default": "./dist/legacy.mjs"}, "require": {"types": "./dist/legacy/index.d.cts", "default": "./dist/legacy.cjs"}}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "typesVersions": {"*": {".": ["dist/index.d.ts"], "legacy": ["dist/legacy/index.d.ts"]}}, "typings": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"attw": "attw --pack .", "build": "tsup && node ../shared/generate-declarations.js", "dev": "tsup --watch", "prepare": "pnpm build", "publint": "publint --strict=true --level=suggestion", "test": "jest --passWithNoTests --runInBand", "types": "node ../shared/generate-declarations.js"}, "dependencies": {"qs": "^6.10.1"}, "devDependencies": {"@esbuild-plugins/node-resolve": "^0.1.4", "@refinedev/core": "^4.57.1", "@remix-run/node": "^2.4.0", "@remix-run/react": "^2.4.0", "@types/jest": "^29.2.4", "@types/qs": "^6.9.7", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "remix": "^2.4.0", "ts-jest": "^29.1.2", "tslib": "^2.6.2", "tsup": "^6.7.0", "typescript": "^5.4.2"}, "peerDependencies": {"@refinedev/core": "^4.46.1", "@remix-run/node": "*", "@remix-run/react": "*", "@types/react": "^17.0.0 || ^18.0.0", "@types/react-dom": "^17.0.0 || ^18.0.0", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0", "remix": "*"}, "engines": {"node": ">=10"}, "publishConfig": {"access": "public"}}