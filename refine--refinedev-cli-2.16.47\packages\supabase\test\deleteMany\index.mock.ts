import nock from "nock";

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .post("/auth/v1/token", {
    email: "<EMAIL>",
    password: "refine-supabase",
    gotrue_meta_security: {},
  })
  .query({ grant_type: "password" })
  .reply(
    200,
    {
      access_token:
        "eyJhbGciOiJIUzI1NiIsImtpZCI6IldGWnFuOWt6bnBJZTIvL2wiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GCCzlV2ZEibr22xEi5OnlcAD3vOzSRkmm1yIU_RwxHg",
      token_type: "bearer",
      expires_in: 3600,
      expires_at: **********,
      refresh_token: "rG9KcdigcT1zovxRbUs8Yg",
      user: {
        id: "bdefac81-2bd1-44d1-b5ed-7abedb96ccce",
        aud: "authenticated",
        role: "authenticated",
        email: "<EMAIL>",
        email_confirmed_at: "2021-09-08T11:09:24.284171Z",
        phone: "",
        confirmation_sent_at: "2021-09-08T11:08:06.793257Z",
        confirmed_at: "2021-09-08T11:09:24.284171Z",
        recovery_sent_at: "2024-02-04T09:33:53.383988Z",
        last_sign_in_at: "2024-04-25T09:06:32.672091588Z",
        app_metadata: { provider: "email" },
        user_metadata: {},
        identities: [
          {
            identity_id: "6b8dcf5b-f068-401b-95ae-ddd93d771b74",
            id: "bdefac81-2bd1-44d1-b5ed-7abedb96ccce",
            user_id: "bdefac81-2bd1-44d1-b5ed-7abedb96ccce",
            identity_data: {
              email: "<EMAIL>",
              sub: "bdefac81-2bd1-44d1-b5ed-7abedb96ccce",
            },
            provider: "email",
            last_sign_in_at: "2022-11-25T00:00:00Z",
            created_at: "2022-11-25T00:00:00Z",
            updated_at: "2022-11-25T00:00:00Z",
            email: "<EMAIL>",
          },
        ],
        created_at: "2021-09-08T11:08:06.789274Z",
        updated_at: "2024-04-25T09:06:32.674256Z",
        is_anonymous: false,
      },
    },
    [
      "Date",
      "Thu, 25 Apr 2024 09:06:32 GMT",
      "Content-Type",
      "application/json",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "CF-Ray",
      "879d2d778a795172-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Set-Cookie",
      "sb-access-token=eyJhbGciOiJIUzI1NiIsImtpZCI6IldGWnFuOWt6bnBJZTIvL2wiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GCCzlV2ZEibr22xEi5OnlcAD3vOzSRkmm1yIU_RwxHg; Path=/; Expires=Fri, 26 Apr 2024 09:06:32 GMT; Max-Age=86400; HttpOnly; Secure",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Vary",
      "Accept-Encoding, Origin",
      "Via",
      "kong/2.8.1",
      "sb-gateway-version",
      "1",
      "Set-Cookie",
      "sb-refresh-token=rG9KcdigcT1zovxRbUs8Yg; Path=/; Expires=Fri, 26 Apr 2024 09:06:32 GMT; Max-Age=86400; HttpOnly; Secure",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "90",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .delete("/rest/v1/posts")
  .query({ id: "eq.1" })
  .reply(204, "", [
    "Date",
    "Thu, 25 Apr 2024 09:06:33 GMT",
    "Connection",
    "close",
    "Content-Range",
    "*/*",
    "CF-Ray",
    "879d2d7b9d557255-IST",
    "CF-Cache-Status",
    "DYNAMIC",
    "Access-Control-Allow-Origin",
    "*",
    "Strict-Transport-Security",
    "max-age=15552000; includeSubDomains",
    "Via",
    "kong/2.8.1",
    "sb-gateway-version",
    "1",
    "x-kong-proxy-latency",
    "0",
    "x-kong-upstream-latency",
    "5",
    "Vary",
    "Accept-Encoding",
    "Server",
    "cloudflare",
    "alt-svc",
    'h3=":443"; ma=86400',
  ]);

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .delete("/rest/v1/posts")
  .query({ id: "eq.1" })
  .reply(204, "", [
    "Date",
    "Thu, 25 Apr 2024 09:06:33 GMT",
    "Connection",
    "close",
    "Content-Range",
    "*/*",
    "CF-Ray",
    "879d2d7eddbe7218-IST",
    "CF-Cache-Status",
    "DYNAMIC",
    "Access-Control-Allow-Origin",
    "*",
    "Strict-Transport-Security",
    "max-age=15552000; includeSubDomains",
    "Via",
    "kong/2.8.1",
    "sb-gateway-version",
    "1",
    "x-kong-proxy-latency",
    "1",
    "x-kong-upstream-latency",
    "3",
    "Vary",
    "Accept-Encoding",
    "Server",
    "cloudflare",
    "alt-svc",
    'h3=":443"; ma=86400',
  ]);

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .delete("/rest/v1/posts")
  .query({ id: "eq.123" })
  .reply(
    406,
    {
      code: "PGRST106",
      details: null,
      hint: null,
      message: "The schema must be one of the following: public, storage",
    },
    [
      "Date",
      "Thu, 25 Apr 2024 09:06:33 GMT",
      "Content-Type",
      "application/json; charset=utf-8",
      "Transfer-Encoding",
      "chunked",
      "Connection",
      "close",
      "CF-Ray",
      "879d2d8079c251a7-IST",
      "CF-Cache-Status",
      "DYNAMIC",
      "Access-Control-Allow-Origin",
      "*",
      "Strict-Transport-Security",
      "max-age=15552000; includeSubDomains",
      "Via",
      "kong/2.8.1",
      "sb-gateway-version",
      "1",
      "x-kong-proxy-latency",
      "1",
      "x-kong-upstream-latency",
      "1",
      "Vary",
      "Accept-Encoding",
      "Server",
      "cloudflare",
      "alt-svc",
      'h3=":443"; ma=86400',
    ],
  );

nock("https://iwdfzvfqbtokqetmbmbp.supabase.co:443", {
  encodedQueryParams: true,
})
  .post("/auth/v1/logout")
  .query({ scope: "global" })
  .reply(204, "", [
    "Date",
    "Thu, 25 Apr 2024 09:06:34 GMT",
    "Connection",
    "close",
    "CF-Ray",
    "879d2d822a47696d-IST",
    "CF-Cache-Status",
    "DYNAMIC",
    "Access-Control-Allow-Origin",
    "*",
    "Set-Cookie",
    "sb-access-token=; Path=/; Expires=Wed, 24 Apr 2024 23:06:34 GMT; Max-Age=0; HttpOnly; Secure",
    "Strict-Transport-Security",
    "max-age=15552000; includeSubDomains",
    "Vary",
    "Origin, Accept-Encoding",
    "Via",
    "kong/2.8.1",
    "sb-gateway-version",
    "1",
    "Set-Cookie",
    "sb-refresh-token=; Path=/; Expires=Wed, 24 Apr 2024 23:06:34 GMT; Max-Age=0; HttpOnly; Secure",
    "x-kong-proxy-latency",
    "1",
    "x-kong-upstream-latency",
    "4",
    "Server",
    "cloudflare",
    "alt-svc",
    'h3=":443"; ma=86400',
  ]);
